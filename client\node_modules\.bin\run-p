#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/npm-run-all@4.1.5/node_modules/npm-run-all/bin/run-p/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/npm-run-all@4.1.5/node_modules/npm-run-all/bin/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/npm-run-all@4.1.5/node_modules/npm-run-all/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/npm-run-all@4.1.5/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/npm-run-all@4.1.5/node_modules/npm-run-all/bin/run-p/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/npm-run-all@4.1.5/node_modules/npm-run-all/bin/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/npm-run-all@4.1.5/node_modules/npm-run-all/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/npm-run-all@4.1.5/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../npm-run-all/bin/run-p/index.js" "$@"
else
  exec node  "$basedir/../npm-run-all/bin/run-p/index.js" "$@"
fi
