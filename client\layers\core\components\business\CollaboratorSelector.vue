<script setup lang="ts">
import { ref, computed } from 'vue'
import { useNuiToasts } from '#imports'

interface User {
  id: number
  firstName: string
  lastName: string
  email: string
  avatar?: string
}

interface Collaborator {
  id: number
  userId: number
  user: User
  addedAt: string
  addedBy: number
}

interface Props {
  companyId: number
  collaborators: Collaborator[]
  teamMembers: User[]
}

interface Emits {
  (e: 'collaborator-added', collaborator: Collaborator): void
  (e: 'collaborator-removed', collaboratorId: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Composables
const { add: addToast } = useNuiToasts()

// State
const showAddModal = ref(false)
const selectedUserId = ref<number | null>(null)
const loading = ref(false)

// Computed
const availableTeamMembers = computed(() => {
  const collaboratorUserIds = new Set(props.collaborators.map(c => c.userId))
  return props.teamMembers.filter(member => !collaboratorUserIds.has(member.id))
})

const selectedUser = computed(() => {
  return props.teamMembers.find(member => member.id === selectedUserId.value)
})

// Methods
const openAddModal = () => {
  selectedUserId.value = null
  showAddModal.value = true
}

const closeAddModal = () => {
  showAddModal.value = false
  selectedUserId.value = null
}

const addCollaborator = async () => {
  if (!selectedUserId.value) return

  loading.value = true
  try {
    const response = await $fetch(`/api/v1/business-idea/company/${props.companyId}/collaborators`, {
      method: 'POST',
      body: {
        userId: selectedUserId.value
      }
    })

    emit('collaborator-added', response)
    closeAddModal()

    addToast({
      title: 'Success',
      message: 'Collaborator added successfully',
      color: 'success',
      icon: 'ph:check-duotone'
    })
  } catch (error) {
    console.error('Error adding collaborator:', error)
    addToast({
      title: 'Error',
      message: 'Failed to add collaborator',
      color: 'danger',
      icon: 'ph:warning-duotone'
    })
  } finally {
    loading.value = false
  }
}

const removeCollaborator = async (collaborator: Collaborator) => {
  try {
    await $fetch(`/api/v1/business-idea/company/${props.companyId}/collaborators/${collaborator.id}`, {
      method: 'DELETE'
    })

    emit('collaborator-removed', collaborator.id)

    addToast({
      title: 'Success',
      message: 'Collaborator removed successfully',
      color: 'success',
      icon: 'ph:check-duotone'
    })
  } catch (error) {
    console.error('Error removing collaborator:', error)
    addToast({
      title: 'Error',
      message: 'Failed to remove collaborator',
      color: 'danger',
      icon: 'ph:warning-duotone'
    })
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}
</script>

<template>
  <div class="space-y-4">
    <!-- Existing Collaborators -->
    <div v-if="collaborators.length > 0" class="space-y-3">
      <div
        v-for="collaborator in collaborators"
        :key="collaborator.id"
        class="flex items-center justify-between p-3 bg-muted-50 dark:bg-muted-900/50 rounded-lg"
      >
        <div class="flex items-center gap-3">
          <BaseAvatar
            :src="collaborator.user.avatar"
            size="sm"
            class="shrink-0"
          />
          <div>
            <p class="text-sm font-medium text-muted-800 dark:text-muted-200">
              {{ collaborator.user.firstName }} {{ collaborator.user.lastName }}
            </p>
            <p class="text-xs text-muted-500">
              {{ collaborator.user.email }}
            </p>
            <p class="text-xs text-muted-400">
              Added {{ formatDate(collaborator.addedAt) }}
            </p>
          </div>
        </div>

        <BaseButton
          size="icon-sm"
          variant="ghost"
          color="danger"
          @click="removeCollaborator(collaborator)"
        >
          <Icon name="ph:x-duotone" class="h-4 w-4" />
        </BaseButton>
      </div>
    </div>

    <!-- No Collaborators Message -->
    <div v-else class="text-center py-6 text-muted-400">
      <Icon name="ph:users-duotone" class="h-8 w-8 mx-auto mb-2" />
      <p class="text-sm">No collaborators added yet</p>
    </div>

    <!-- Add Collaborator Button -->
    <BaseButton
      v-if="availableTeamMembers.length > 0"
      variant="outline"
      color="primary"
      size="sm"
      class="w-full"
      @click="openAddModal"
    >
      <Icon name="ph:plus-duotone" class="h-4 w-4 mr-2" />
      {{ $t('business_idea.add_collaborator') }}
    </BaseButton>

    <!-- No Available Team Members -->
    <div v-else-if="teamMembers.length === collaborators.length" class="text-center py-4">
      <p class="text-sm text-muted-500">All team members are already collaborators</p>
    </div>

    <!-- Add Collaborator Modal -->
    <TairoModal
      :open="showAddModal"
      size="sm"
      @close="closeAddModal"
    >
      <template #header>
        <div class="flex w-full items-center justify-between p-4 md:p-6">
          <h3 class="font-heading text-muted-900 text-lg font-medium leading-6 dark:text-white">
            {{ $t('business_idea.add_collaborator') }}
          </h3>
          <BaseButtonClose @click="closeAddModal" />
        </div>
      </template>

      <div class="p-4 md:p-6">
        <div class="mx-auto w-full max-w-sm">
          <!-- Team Member Selection -->
          <BaseField label="Select Team Member" class="mb-6">
            <TairoSelect v-model="selectedUserId">
              <TairoSelectItem
                v-for="member in availableTeamMembers"
                :key="member.id"
                :value="member.id"
              >
                <div class="flex items-center gap-3">
                  <BaseAvatar
                    :src="member.avatar"
                    size="xs"
                    class="shrink-0"
                  />
                  <div>
                    <p class="text-sm font-medium">
                      {{ member.firstName }} {{ member.lastName }}
                    </p>
                    <p class="text-xs text-muted-500">
                      {{ member.email }}
                    </p>
                  </div>
                </div>
              </TairoSelectItem>
            </TairoSelect>
          </BaseField>

          <!-- Selected User Preview -->
          <div v-if="selectedUser" class="mb-6 p-3 bg-muted-50 dark:bg-muted-900/50 rounded-lg">
            <div class="flex items-center gap-3">
              <BaseAvatar
                :src="selectedUser.avatar"
                size="sm"
                class="shrink-0"
              />
              <div>
                <p class="text-sm font-medium text-muted-800 dark:text-muted-200">
                  {{ selectedUser.firstName }} {{ selectedUser.lastName }}
                </p>
                <p class="text-xs text-muted-500">
                  {{ selectedUser.email }}
                </p>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex gap-x-2">
            <BaseButton
              variant="primary"
              class="flex-1"
              :disabled="!selectedUserId || loading"
              :loading="loading"
              @click="addCollaborator"
            >
              {{ $t('business_idea.add_collaborator') }}
            </BaseButton>
            <BaseButton
              variant="outline"
              color="muted"
              class="flex-1"
              @click="closeAddModal"
              :disabled="loading"
            >
              {{ $t('profile.cancel') }}
            </BaseButton>
          </div>
        </div>
      </div>
    </TairoModal>
  </div>
</template>
