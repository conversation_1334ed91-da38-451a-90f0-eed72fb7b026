<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useUserStore } from "../../../../../.app/stores/useUserStore";
import { useApi } from "../../../../../.app/app/composables/useApi";

// Define page meta
definePageMeta({
  title: "business_idea.title",
  layout: "default",
});

// Types
interface LearningMaterial {
  id: number;
  key: string;
  title: string;
  content: string;
  videoUrls: string[];
  isActive: boolean;
  order: number;
}

interface BusinessIdea {
  id: number;
  content: string;
  isPublished: boolean;
  notes: any[];
  collaborators: any[];
}

interface Greeting {
  id: number;
  title: string;
  content: string;
  isActive: boolean;
}

// Stores and composables
const userStore = useUserStore();

// State
const loading = ref(false);
const businessIdea = ref<BusinessIdea | null>(null);
const greeting = ref<Greeting | null>(null);
const learningMaterials = ref<LearningMaterial[]>([]);
const teamMembers = ref<any[]>([]);
const isEditingIdea = ref(false);
const businessIdeaContent = ref("");
const notes = ref<any[]>([
  { id: 1, content: "Target market: Small businesses", position: 0 },
  { id: 2, content: "Key feature: AI-powered automation", position: 1 },
]);
const collaborators = ref<any[]>([]);
const isEditingGreeting = ref(false);
const greetingTitle = ref("");
const greetingContent = ref("");
const editingMaterialId = ref<number | null>(null);
const materialTitle = ref("");
const materialContent = ref("");
const expandedMaterialId = ref<number | null>(null);

// Computed
const companyId = computed(() => userStore.user?.companies?.[0]?.id);
const isAdmin = computed(() => {
  // Check if user is SUPERADMIN (has isSuperAdmin property) or has ADMIN role
  const user = userStore.user;
  if (!user) return false;

  // Check isSuperAdmin property
  if (user.isSuperAdmin) return true;

  // Check roles array for ADMIN or SUPERADMIN
  const roles = user.roles || [];
  return roles.some(
    (role) => role.role === "ADMIN" || role.role === "SUPERADMIN"
  );
});

const hasBusinessIdea = computed(() => {
  return (
    businessIdea.value?.content && businessIdea.value.content.trim().length > 0
  );
});

// API composable
const api = useApi();

// Methods
const fetchData = async () => {
  if (!companyId.value) return;

  loading.value = true;
  try {
    const { locale } = useI18n();
    const currentLocale = locale.value;

    // Fetch all data in parallel from actual API endpoints
    const [ideaResponse, greetingResponse, materialsResponse, teamResponse] =
      await Promise.all([
        api
          .get(`/core/business-idea/company/${companyId.value}`)
          .catch(() => ({ data: null })),
        api
          .get("/core/business-idea/greeting", { locale: currentLocale })
          .catch(() => ({ data: null })),
        api
          .get("/core/business-idea/learning-materials", {
            locale: currentLocale,
          })
          .catch(() => ({ data: [] })),
        api
          .get(`/core/business-idea/company/${companyId.value}/team-members`)
          .catch(() => ({ data: [] })),
      ]);

    // The useApi composable returns the data directly, not wrapped in a data property
    businessIdea.value = ideaResponse as any;
    greeting.value = greetingResponse as any;
    learningMaterials.value = (materialsResponse as any) || [];
    teamMembers.value = (teamResponse as any) || [];

    // Debug logging
    console.log("Fetched data:", {
      businessIdea: businessIdea.value,
      greeting: greeting.value,
      learningMaterials: learningMaterials.value?.length,
      teamMembers: teamMembers.value?.length,
    });

    // Extract notes and collaborators from business idea
    notes.value = businessIdea.value?.notes || [];
    collaborators.value = businessIdea.value?.collaborators || [];

    // Set initial content for editing
    businessIdeaContent.value = businessIdea.value?.content || "";
  } catch (error) {
    console.error("Error fetching data:", error);
  } finally {
    loading.value = false;
  }
};

const saveBusinessIdea = async () => {
  if (!companyId.value) return;

  try {
    const response = await api.post(
      `/core/business-idea/company/${companyId.value}`,
      {
        content: businessIdeaContent.value,
        isPublished: false,
      }
    );

    if (response) {
      businessIdea.value = response as any;
      isEditingIdea.value = false;
    }
  } catch (error) {
    console.error("Error saving business idea:", error);
  }
};

const startEditing = () => {
  businessIdeaContent.value = businessIdea.value?.content || "";
  isEditingIdea.value = true;
};

const cancelEditing = () => {
  businessIdeaContent.value = businessIdea.value?.content || "";
  isEditingIdea.value = false;
};

// Notes methods
const addNote = async () => {
  if (!companyId.value) return;

  try {
    const response = await api.post(
      `/core/business-idea/company/${companyId.value}/notes`,
      {
        content: "",
        position: notes.value.length,
      }
    );

    if (response) {
      notes.value.push(response as any);
    }
  } catch (error) {
    console.error("Error adding note:", error);
    // Fallback to local add
    notes.value.push({
      id: Date.now(),
      content: "",
      position: notes.value.length,
    });
  }
};

const removeNote = async (index: number) => {
  const note = notes.value[index];
  if (!note || !companyId.value) return;

  try {
    await api.delete(
      `/core/business-idea/company/${companyId.value}/notes/${note.id}`
    );
    notes.value.splice(index, 1);
  } catch (error) {
    console.error("Error removing note:", error);
  }
};

const saveNote = async (note: any) => {
  if (!note.id || !companyId.value) return;

  try {
    await api.put(
      `/core/business-idea/company/${companyId.value}/notes/${note.id}`,
      {
        content: note.content,
        position: note.position,
      }
    );
  } catch (error) {
    console.error("Error saving note:", error);
  }
};

// Greeting edit methods
const startEditingGreeting = () => {
  greetingTitle.value = greeting.value?.title || "";
  greetingContent.value = greeting.value?.content || "";
  isEditingGreeting.value = true;
};

const saveGreeting = async () => {
  try {
    const response = await api.put("/core/business-idea/greeting", {
      title: greetingTitle.value,
      content: greetingContent.value,
      isActive: true,
    });

    if (response) {
      greeting.value = response as any;
      isEditingGreeting.value = false;
    }
  } catch (error) {
    console.error("Error saving greeting:", error);
  }
};

const cancelEditingGreeting = () => {
  greetingTitle.value = greeting.value?.title || "";
  greetingContent.value = greeting.value?.content || "";
  isEditingGreeting.value = false;
};

// Learning material edit methods
const startEditingMaterial = (material: LearningMaterial) => {
  editingMaterialId.value = material.id;
  materialTitle.value = material.title;
  materialContent.value = material.content;
};

const saveMaterial = async () => {
  if (!editingMaterialId.value) return;

  try {
    const response = await api.put(
      `/core/business-idea/learning-materials/${editingMaterialId.value}`,
      {
        title: materialTitle.value,
        content: materialContent.value,
      }
    );

    if (response) {
      const index = learningMaterials.value.findIndex(
        (m) => m.id === editingMaterialId.value
      );
      if (index >= 0) {
        learningMaterials.value[index] = response as any;
      }
      editingMaterialId.value = null;
    }
  } catch (error) {
    console.error("Error saving learning material:", error);
  }
};

const cancelEditingMaterial = () => {
  editingMaterialId.value = null;
  materialTitle.value = "";
  materialContent.value = "";
};

// Accordion toggle functionality
const toggleMaterial = (materialId: number) => {
  if (expandedMaterialId.value === materialId) {
    expandedMaterialId.value = null;
  } else {
    expandedMaterialId.value = materialId;
  }
};

// Lifecycle
onMounted(() => {
  fetchData();
});
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pt-6 pb-20">
    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-12">
      <Icon name="svg-spinners:ring-resize" class="h-8 w-8 text-primary-500" />
    </div>

    <div v-else class="space-y-6">
      <!-- Greeting Section -->
      <BaseCard rounded="md" class="p-4 md:p-6">
        <div v-if="!isEditingGreeting" class="flex items-start justify-between">
          <div class="flex-1">
            <BaseHeading
              as="h2"
              size="lg"
              weight="medium"
              class="text-muted-800 dark:text-white mb-2"
            >
              {{ greeting?.title || "Welcome to Your Business Journey!" }}
            </BaseHeading>

            <div
              v-if="greeting?.content"
              class="prose prose-sm prose-muted dark:prose-invert max-w-none text-muted-600 dark:text-muted-400"
              v-html="greeting.content"
            />
            <BaseParagraph
              v-else
              size="sm"
              class="text-muted-600 dark:text-muted-400"
            >
              It's fantastic that you're thinking about starting a company! This
              step will help you focus on your core business idea.
            </BaseParagraph>
          </div>

          <!-- Admin Edit Button -->
          <BaseButton
            v-if="isAdmin"
            size="sm"
            variant="outline"
            color="muted"
            class="ml-4"
            @click="startEditingGreeting"
          >
            <Icon name="ph:pencil-duotone" class="h-4 w-4" />
          </BaseButton>
        </div>

        <!-- Greeting Edit Form -->
        <div v-else class="space-y-4">
          <BaseField label="Title">
            <BaseInput
              v-model="greetingTitle"
              placeholder="Enter greeting title"
              rounded="md"
            />
          </BaseField>

          <BaseField label="Content">
            <TiptapEditor
              v-model="greetingContent"
              placeholder="Enter greeting content with rich formatting..."
            />
          </BaseField>

          <div class="flex gap-2">
            <BaseButton
              variant="primary"
              @click="saveGreeting"
              :disabled="!greetingTitle.trim() || !greetingContent.trim()"
            >
              <Icon name="ph:check-duotone" class="h-4 w-4 mr-2" />
              Save
            </BaseButton>

            <BaseButton
              variant="outline"
              color="muted"
              @click="cancelEditingGreeting"
            >
              Cancel
            </BaseButton>
          </div>
        </div>
      </BaseCard>

      <!-- Learning Materials Accordion -->
      <BaseCard rounded="md" class="p-4 md:p-6">
        <BaseHeading
          as="h3"
          size="md"
          weight="medium"
          class="text-muted-800 dark:text-white mb-4"
        >
          Learning Materials
        </BaseHeading>

        <!-- Custom Accordion with Edit Functionality -->
        <div class="space-y-2">
          <div
            v-for="material in learningMaterials"
            :key="material.id"
            class="border border-muted-200 dark:border-muted-800 rounded-md"
          >
            <!-- Accordion Header -->
            <div
              class="flex items-center justify-between p-3 cursor-pointer hover:bg-muted-50 dark:hover:bg-muted-900/50"
              @click="toggleMaterial(material.id)"
            >
              <div class="flex items-center gap-2">
                <Icon
                  :name="
                    expandedMaterialId === material.id
                      ? 'ph:chevron-down-duotone'
                      : 'ph:chevron-right-duotone'
                  "
                  class="h-4 w-4 text-muted-400 transition-transform duration-200"
                />
                <span
                  class="text-sm font-medium text-muted-800 dark:text-white"
                >
                  {{ material.title }}
                </span>
              </div>

              <!-- Admin Edit Button -->
              <BaseButton
                v-if="isAdmin"
                size="sm"
                variant="outline"
                color="muted"
                @click.stop="startEditingMaterial(material)"
              >
                <Icon name="ph:pencil-duotone" class="h-4 w-4" />
              </BaseButton>
            </div>

            <!-- Material Edit Form -->
            <div
              v-if="editingMaterialId === material.id"
              class="p-4 border-t border-muted-200 dark:border-muted-800 bg-muted-50 dark:bg-muted-900/30"
            >
              <div class="space-y-4">
                <BaseField label="Title">
                  <BaseInput
                    v-model="materialTitle"
                    placeholder="Enter material title"
                    rounded="md"
                  />
                </BaseField>

                <BaseField label="Content">
                  <TiptapEditor
                    v-model="materialContent"
                    placeholder="Enter material content with rich formatting..."
                  />
                </BaseField>

                <div class="flex gap-2">
                  <BaseButton
                    variant="primary"
                    size="sm"
                    @click="saveMaterial"
                    :disabled="!materialTitle.trim() || !materialContent.trim()"
                  >
                    <Icon name="ph:check-duotone" class="h-4 w-4 mr-2" />
                    Save
                  </BaseButton>

                  <BaseButton
                    variant="outline"
                    color="muted"
                    size="sm"
                    @click="cancelEditingMaterial"
                  >
                    Cancel
                  </BaseButton>
                </div>
              </div>
            </div>

            <!-- Material Content Display -->
            <div
              v-else-if="expandedMaterialId === material.id"
              class="p-4 border-t border-muted-200 dark:border-muted-800"
            >
              <div
                class="prose prose-sm prose-muted dark:prose-invert max-w-none text-muted-600 dark:text-muted-400"
                v-html="material.content"
              />

              <!-- Video URLs if available -->
              <div
                v-if="material.videoUrls && material.videoUrls.length > 0"
                class="mt-4 space-y-4"
              >
                <div
                  v-for="(videoUrl, index) in material.videoUrls"
                  :key="index"
                  class="aspect-video rounded-lg overflow-hidden"
                >
                  <iframe
                    :src="videoUrl"
                    class="w-full h-full"
                    frameborder="0"
                    allowfullscreen
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </BaseCard>

      <!-- Business Idea Editor Section -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Business Idea Editor (Left Side - 2/3 width) -->
        <div class="lg:col-span-2">
          <BaseCard rounded="md" class="p-4 md:p-6">
            <div class="flex items-center justify-between mb-4">
              <BaseHeading
                as="h3"
                size="md"
                weight="medium"
                class="text-muted-800 dark:text-white"
              >
                Your Business Idea
              </BaseHeading>

              <BaseButton
                v-if="hasBusinessIdea && !isEditingIdea"
                size="sm"
                variant="outline"
                color="primary"
                @click="startEditing"
              >
                <Icon name="ph:pencil-duotone" class="h-4 w-4 mr-2" />
                Edit
              </BaseButton>
            </div>

            <!-- Business Idea Display/Editor -->
            <div v-if="!hasBusinessIdea || isEditingIdea">
              <TiptapEditor
                v-model="businessIdeaContent"
                placeholder="Describe your business idea here... What problem does it solve? Who are your target customers? What makes your solution unique?"
                class="mb-4"
              />

              <div class="flex gap-2">
                <BaseButton
                  variant="primary"
                  @click="saveBusinessIdea"
                  :disabled="!businessIdeaContent.trim()"
                >
                  <Icon name="ph:check-duotone" class="h-4 w-4 mr-2" />
                  Save
                </BaseButton>

                <BaseButton
                  v-if="hasBusinessIdea"
                  variant="outline"
                  color="muted"
                  @click="cancelEditing"
                >
                  Cancel
                </BaseButton>
              </div>
            </div>

            <div
              v-else
              class="prose prose-sm prose-muted dark:prose-invert max-w-none"
            >
              <div v-if="businessIdea?.content" v-html="businessIdea.content" />
              <p v-else class="text-muted-500 italic">
                No business idea entered yet.
              </p>
            </div>

            <!-- Upload Document Option -->
            <div
              class="mt-6 pt-4 border-t border-muted-200 dark:border-muted-800"
            >
              <BaseButton variant="outline" color="muted" size="sm">
                <Icon name="ph:upload-duotone" class="h-4 w-4 mr-2" />
                Upload Document
              </BaseButton>
            </div>
          </BaseCard>
        </div>

        <!-- Notes Section (Right Side - 1/3 width) -->
        <div class="lg:col-span-1 space-y-6">
          <!-- Quick Notes -->
          <BaseCard rounded="md" class="p-4 md:p-6">
            <BaseHeading
              as="h3"
              size="md"
              weight="medium"
              class="text-muted-800 dark:text-white mb-4"
            >
              Quick Notes
            </BaseHeading>

            <!-- Post-it Style Notes -->
            <div class="space-y-3">
              <div
                v-for="(note, index) in notes"
                :key="index"
                class="relative bg-yellow-100 dark:bg-yellow-900/20 p-3 rounded-lg shadow-sm border-l-4 border-yellow-400"
                style="
                  transform: rotate(-1deg);
                  font-family: 'Comic Sans MS', cursive, sans-serif;
                "
              >
                <textarea
                  v-model="note.content"
                  class="w-full bg-transparent border-none outline-none resize-none text-sm text-muted-800 dark:text-muted-200 placeholder:text-muted-400"
                  placeholder="Quick idea..."
                  rows="2"
                  @input="saveNote(note)"
                />
                <div
                  class="flex justify-between items-center mt-2 text-xs text-muted-500"
                >
                  <span>Auto-saved</span>
                  <button
                    @click="removeNote(index)"
                    class="text-red-500 hover:text-red-700"
                  >
                    <Icon name="ph:trash-duotone" class="h-3 w-3" />
                  </button>
                </div>
              </div>

              <!-- Add Note Button -->
              <BaseButton
                variant="outline"
                color="muted"
                size="sm"
                class="w-full border-dashed"
                @click="addNote"
              >
                <Icon name="ph:plus-duotone" class="h-4 w-4 mr-2" />
                Add Note
              </BaseButton>
            </div>
          </BaseCard>

          <!-- Collaborators Section -->
          <BaseCard rounded="md" class="p-4 md:p-6">
            <BaseHeading
              as="h3"
              size="md"
              weight="medium"
              class="text-muted-800 dark:text-white mb-4"
            >
              Collaborators
            </BaseHeading>

            <div class="text-center py-4 text-muted-400">
              <Icon name="ph:users-duotone" class="h-8 w-8 mx-auto mb-2" />
              <p class="text-sm">Add team members to collaborate</p>
              <BaseButton
                variant="outline"
                color="primary"
                size="sm"
                class="mt-2"
              >
                <Icon name="ph:plus-duotone" class="h-4 w-4 mr-2" />
                Add Collaborator
              </BaseButton>
            </div>
          </BaseCard>
        </div>
      </div>
    </div>
  </div>
</template>
