<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useUserStore } from "../../../../../.app/stores/useUserStore";

// Define page meta
definePageMeta({
  title: "business_idea.title",
  layout: "default",
});

// Types
interface LearningMaterial {
  id: number;
  key: string;
  title: string;
  content: string;
  videoUrls: string[];
  isActive: boolean;
  order: number;
}

interface BusinessIdea {
  id: number;
  content: string;
  isPublished: boolean;
  notes: any[];
  collaborators: any[];
}

interface Greeting {
  id: number;
  title: string;
  content: string;
  isActive: boolean;
}

// Stores and composables
const userStore = useUserStore();

// State
const loading = ref(false);
const businessIdea = ref<BusinessIdea | null>(null);
const greeting = ref<Greeting | null>(null);
const learningMaterials = ref<LearningMaterial[]>([]);
const teamMembers = ref<any[]>([]);
const isEditingIdea = ref(false);
const businessIdeaContent = ref("");
const notes = ref<any[]>([]);
const collaborators = ref<any[]>([]);
const showEditGreeting = ref(false);
const showEditMaterial = ref(false);
const selectedMaterial = ref<LearningMaterial | null>(null);

// Computed
const companyId = computed(() => userStore.user?.companies?.[0]?.id);
const isAdmin = computed(() => {
  const roles = userStore.user?.roles || [];
  return roles.some(
    (role) => role.role === "ADMIN" || role.role === "SUPERADMIN"
  );
});

const hasBusinessIdea = computed(() => {
  return (
    businessIdea.value?.content && businessIdea.value.content.trim().length > 0
  );
});

// Create accordion items for learning materials
const accordionItems = computed(() => {
  return learningMaterials.value.map((material) => ({
    value: `material-${material.id}`,
    title: material.title,
    content: material.content,
  }));
});

// Methods
const fetchData = async () => {
  if (!companyId.value) return;

  loading.value = true;
  try {
    // Mock data for now - replace with actual API calls
    businessIdea.value = {
      id: 1,
      content: "",
      isPublished: false,
      notes: [],
      collaborators: [],
    };

    greeting.value = {
      id: 1,
      title: "Welcome to Your Business Journey!",
      content:
        "It's fantastic that you're thinking about starting a company! Don't worry if you don't have a complete business plan yet - this step is designed to help you focus on your core business idea.",
      isActive: true,
    };

    learningMaterials.value = [
      {
        id: 1,
        key: "what_is_business_idea",
        title: "What is a Business Idea?",
        content:
          "A business idea is a concept that can be used for financial gain that is usually centered on a product or service.",
        videoUrls: [],
        isActive: true,
        order: 1,
      },
      {
        id: 2,
        key: "market_research",
        title: "Market Research Fundamentals",
        content:
          "Market research is the process of gathering information about your target market and customers.",
        videoUrls: [],
        isActive: true,
        order: 2,
      },
    ];

    teamMembers.value = [];
    notes.value = businessIdea.value?.notes || [];
    collaborators.value = businessIdea.value?.collaborators || [];
    businessIdeaContent.value = businessIdea.value?.content || "";
  } catch (error) {
    console.error("Error fetching data:", error);
  } finally {
    loading.value = false;
  }
};

const saveBusinessIdea = async () => {
  if (!companyId.value) return;

  // Mock save for now
  if (businessIdea.value) {
    businessIdea.value.content = businessIdeaContent.value;
  }
  isEditingIdea.value = false;
};

const startEditing = () => {
  businessIdeaContent.value = businessIdea.value?.content || "";
  isEditingIdea.value = true;
};

const cancelEditing = () => {
  businessIdeaContent.value = businessIdea.value?.content || "";
  isEditingIdea.value = false;
};

// Notes methods
const addNote = () => {
  notes.value.push({
    id: Date.now(),
    content: "",
    position: notes.value.length,
  });
};

const removeNote = (index: number) => {
  notes.value.splice(index, 1);
};

// Lifecycle
onMounted(() => {
  fetchData();
});
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-12">
      <BaseSpinner size="lg" />
    </div>

    <div v-else class="space-y-6">
      <!-- Greeting Section -->
      <BaseCard rounded="md" class="p-4 md:p-6">
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <BaseHeading
              as="h2"
              size="lg"
              weight="medium"
              class="text-muted-800 dark:text-white mb-2"
            >
              {{ greeting?.title || "Welcome to Your Business Journey!" }}
            </BaseHeading>

            <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
              {{
                greeting?.content ||
                "It's fantastic that you're thinking about starting a company! This step will help you focus on your core business idea."
              }}
            </BaseParagraph>
          </div>

          <!-- Admin Edit Button -->
          <BaseButton
            v-if="isAdmin"
            size="sm"
            variant="outline"
            color="muted"
            class="ml-4"
          >
            <Icon name="ph:pencil-duotone" class="h-4 w-4" />
          </BaseButton>
        </div>
      </BaseCard>

      <!-- Learning Materials Accordion -->
      <BaseCard rounded="md" class="p-4 md:p-6">
        <BaseHeading
          as="h3"
          size="md"
          weight="medium"
          class="text-muted-800 dark:text-white mb-4"
        >
          Learning Materials
        </BaseHeading>

        <BaseAccordion
          :items="accordionItems"
          type="single"
          collapsible
          action="chevron"
          rounded="sm"
        />
      </BaseCard>

      <!-- Business Idea Editor Section -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Business Idea Editor (Left Side - 2/3 width) -->
        <div class="lg:col-span-2">
          <BaseCard rounded="md" class="p-4 md:p-6">
            <div class="flex items-center justify-between mb-4">
              <BaseHeading
                as="h3"
                size="md"
                weight="medium"
                class="text-muted-800 dark:text-white"
              >
                Your Business Idea
              </BaseHeading>

              <BaseButton
                v-if="hasBusinessIdea && !isEditingIdea"
                size="sm"
                variant="outline"
                color="primary"
                @click="startEditing"
              >
                <Icon name="ph:pencil-duotone" class="h-4 w-4 mr-2" />
                Edit
              </BaseButton>
            </div>

            <!-- Business Idea Display/Editor -->
            <div v-if="!hasBusinessIdea || isEditingIdea">
              <BaseTextarea
                v-model="businessIdeaContent"
                placeholder="Describe your business idea here... What problem does it solve? Who are your target customers? What makes your solution unique?"
                autogrow
                rounded="md"
                class="w-full mb-4"
              />

              <div class="flex gap-2">
                <BaseButton
                  variant="primary"
                  @click="saveBusinessIdea"
                  :disabled="!businessIdeaContent.trim()"
                >
                  <Icon name="ph:check-duotone" class="h-4 w-4 mr-2" />
                  Save
                </BaseButton>

                <BaseButton
                  v-if="hasBusinessIdea"
                  variant="outline"
                  color="muted"
                  @click="cancelEditing"
                >
                  Cancel
                </BaseButton>
              </div>
            </div>

            <div
              v-else
              class="prose prose-sm prose-muted dark:prose-invert max-w-none"
            >
              <p>
                {{ businessIdea?.content || "No business idea entered yet." }}
              </p>
            </div>

            <!-- Upload Document Option -->
            <div
              class="mt-6 pt-4 border-t border-muted-200 dark:border-muted-800"
            >
              <BaseButton variant="outline" color="muted" size="sm">
                <Icon name="ph:upload-duotone" class="h-4 w-4 mr-2" />
                Upload Document
              </BaseButton>
            </div>
          </BaseCard>
        </div>

        <!-- Notes Section (Right Side - 1/3 width) -->
        <div class="lg:col-span-1 space-y-6">
          <!-- Quick Notes -->
          <BaseCard rounded="md" class="p-4 md:p-6">
            <BaseHeading
              as="h3"
              size="md"
              weight="medium"
              class="text-muted-800 dark:text-white mb-4"
            >
              Quick Notes
            </BaseHeading>

            <!-- Post-it Style Notes -->
            <div class="space-y-3">
              <div
                v-for="(note, index) in notes"
                :key="index"
                class="relative bg-yellow-100 dark:bg-yellow-900/20 p-3 rounded-lg shadow-sm border-l-4 border-yellow-400"
                style="
                  transform: rotate(-1deg);
                  font-family: 'Comic Sans MS', cursive, sans-serif;
                "
              >
                <textarea
                  v-model="note.content"
                  class="w-full bg-transparent border-none outline-none resize-none text-sm text-muted-800 dark:text-muted-200 placeholder:text-muted-400"
                  placeholder="Quick idea..."
                  rows="2"
                />
                <div
                  class="flex justify-between items-center mt-2 text-xs text-muted-500"
                >
                  <span>Auto-saved</span>
                  <button
                    @click="removeNote(index)"
                    class="text-red-500 hover:text-red-700"
                  >
                    <Icon name="ph:trash-duotone" class="h-3 w-3" />
                  </button>
                </div>
              </div>

              <!-- Add Note Button -->
              <BaseButton
                variant="outline"
                color="muted"
                size="sm"
                class="w-full border-dashed"
                @click="addNote"
              >
                <Icon name="ph:plus-duotone" class="h-4 w-4 mr-2" />
                Add Note
              </BaseButton>
            </div>
          </BaseCard>

          <!-- Collaborators Section -->
          <BaseCard rounded="md" class="p-4 md:p-6">
            <BaseHeading
              as="h3"
              size="md"
              weight="medium"
              class="text-muted-800 dark:text-white mb-4"
            >
              Collaborators
            </BaseHeading>

            <div class="text-center py-4 text-muted-400">
              <Icon name="ph:users-duotone" class="h-8 w-8 mx-auto mb-2" />
              <p class="text-sm">Add team members to collaborate</p>
              <BaseButton
                variant="outline"
                color="primary"
                size="sm"
                class="mt-2"
              >
                <Icon name="ph:plus-duotone" class="h-4 w-4 mr-2" />
                Add Collaborator
              </BaseButton>
            </div>
          </BaseCard>
        </div>
      </div>
    </div>
  </div>
</template>
