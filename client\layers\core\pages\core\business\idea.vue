<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useUserStore } from "../../../../stores/useUserStore";
import { useNuiToasts } from "#imports";

// Define page meta
definePageMeta({
  title: "business_idea.title",
  layout: "default",
});

// Stores and composables
const userStore = useUserStore();
const { add: addToast } = useNuiToasts();

// State
const loading = ref(false);
const businessIdea = ref(null);
const greeting = ref(null);
const learningMaterials = ref([]);
const teamMembers = ref([]);
const isEditingIdea = ref(false);
const businessIdeaContent = ref("");
const notes = ref([]);
const collaborators = ref([]);

// Computed
const companyId = computed(() => userStore.user?.companies?.[0]?.id);
const isAdmin = computed(() => {
  const roles = userStore.user?.roles || [];
  return roles.some(
    (role) => role.role === "ADMIN" || role.role === "SUPERADMIN"
  );
});

const hasBusinessIdea = computed(() => {
  return (
    businessIdea.value?.content && businessIdea.value.content.trim().length > 0
  );
});

// Methods
const fetchData = async () => {
  if (!companyId.value) return;

  loading.value = true;
  try {
    // Fetch all data in parallel
    const [ideaResponse, greetingResponse, materialsResponse, teamResponse] =
      await Promise.all([
        $fetch(`/api/v1/business-idea/company/${companyId.value}`),
        $fetch("/api/v1/business-idea/greeting"),
        $fetch("/api/v1/business-idea/learning-materials"),
        $fetch(`/api/v1/business-idea/company/${companyId.value}/team-members`),
      ]);

    businessIdea.value = ideaResponse;
    greeting.value = greetingResponse;
    learningMaterials.value = materialsResponse;
    teamMembers.value = teamResponse;

    // Extract notes and collaborators from business idea
    notes.value = businessIdea.value?.notes || [];
    collaborators.value = businessIdea.value?.collaborators || [];

    // Set initial content for editing
    businessIdeaContent.value = businessIdea.value?.content || "";
  } catch (error) {
    console.error("Error fetching data:", error);
    addToast({
      title: "Error",
      message: "Failed to load business idea data",
      color: "danger",
      icon: "ph:warning-duotone",
    });
  } finally {
    loading.value = false;
  }
};

const saveBusinessIdea = async () => {
  if (!companyId.value) return;

  try {
    const response = await $fetch(
      `/api/v1/business-idea/company/${companyId.value}`,
      {
        method: "POST",
        body: {
          content: businessIdeaContent.value,
          isPublished: false,
        },
      }
    );

    businessIdea.value = response;
    isEditingIdea.value = false;

    addToast({
      title: "Success",
      message: "Business idea saved successfully",
      color: "success",
      icon: "ph:check-duotone",
    });
  } catch (error) {
    console.error("Error saving business idea:", error);
    addToast({
      title: "Error",
      message: "Failed to save business idea",
      color: "danger",
      icon: "ph:warning-duotone",
    });
  }
};

const startEditing = () => {
  businessIdeaContent.value = businessIdea.value?.content || "";
  isEditingIdea.value = true;
};

const cancelEditing = () => {
  businessIdeaContent.value = businessIdea.value?.content || "";
  isEditingIdea.value = false;
};

// Event handlers for components
const handleNoteSaved = (note) => {
  const existingIndex = notes.value.findIndex((n) => n.id === note.id);
  if (existingIndex >= 0) {
    notes.value[existingIndex] = note;
  } else {
    notes.value.push(note);
  }
};

const handleNoteDeleted = (noteId) => {
  const index = notes.value.findIndex((n) => n.id === noteId);
  if (index >= 0) {
    notes.value.splice(index, 1);
  }
};

const handleCollaboratorAdded = (collaborator) => {
  collaborators.value.push(collaborator);
};

const handleCollaboratorRemoved = (collaboratorId) => {
  const index = collaborators.value.findIndex((c) => c.id === collaboratorId);
  if (index >= 0) {
    collaborators.value.splice(index, 1);
  }
};

// Lifecycle
onMounted(() => {
  fetchData();
});
</script>

<template>
  <BaseLayerPage
    :title="$t('business_idea.title')"
    :description="$t('business_idea.description')"
  >
    <div v-if="loading" class="flex items-center justify-center py-12">
      <BaseSpinner size="lg" />
    </div>

    <div v-else class="space-y-8">
      <!-- Greeting Section -->
      <BaseCard class="p-6 bg-white dark:bg-black">
        <div class="mb-6">
          <BaseHeading
            as="h2"
            size="xl"
            weight="semibold"
            class="text-muted-800 dark:text-white mb-4"
          >
            {{ greeting?.title || $t("business_idea.greeting_title") }}
          </BaseHeading>

          <div
            v-if="greeting?.content"
            class="prose prose-muted dark:prose-invert max-w-none"
            v-html="greeting.content"
          />
          <BaseParagraph
            v-else
            size="lg"
            class="text-muted-600 dark:text-muted-400 leading-relaxed"
          >
            {{ $t("business_idea.greeting_text") }}
          </BaseParagraph>

          <!-- Admin Edit Button for Greeting -->
          <div v-if="isAdmin" class="mt-4">
            <BaseButton size="sm" variant="outline" color="muted">
              <Icon name="ph:pencil-duotone" class="h-4 w-4 mr-2" />
              {{ $t("business_idea.edit") }}
            </BaseButton>
          </div>
        </div>
      </BaseCard>

      <!-- Learning Materials Accordion -->
      <BaseCard class="p-6 bg-white dark:bg-black">
        <BaseHeading
          as="h3"
          size="lg"
          weight="medium"
          class="text-muted-800 dark:text-white mb-4"
        >
          {{ $t("business_idea.learning_materials_title") }}
        </BaseHeading>

        <BaseAccordion
          type="single"
          collapsible
          action="chevron"
          class="w-full"
        >
          <BaseAccordionItem
            v-for="material in learningMaterials"
            :key="material.id"
            :value="`material-${material.id}`"
            :title="material.title"
          >
            <div class="space-y-4">
              <!-- Material Content -->
              <div
                v-if="material.content"
                class="prose prose-muted dark:prose-invert max-w-none"
                v-html="material.content"
              />

              <!-- YouTube Videos -->
              <div
                v-if="material.videoUrls && material.videoUrls.length > 0"
                class="space-y-4"
              >
                <div
                  v-for="(videoUrl, index) in material.videoUrls"
                  :key="index"
                  class="aspect-video"
                >
                  <iframe
                    :src="videoUrl"
                    class="w-full h-full rounded-lg"
                    frameborder="0"
                    allowfullscreen
                  />
                </div>
              </div>

              <!-- Admin Edit Button for Material -->
              <div
                v-if="isAdmin"
                class="pt-4 border-t border-muted-200 dark:border-muted-800"
              >
                <BaseButton size="sm" variant="outline" color="muted">
                  <Icon name="ph:pencil-duotone" class="h-4 w-4 mr-2" />
                  {{ $t("business_idea.edit") }}
                </BaseButton>
              </div>
            </div>
          </BaseAccordionItem>
        </BaseAccordion>
      </BaseCard>

      <!-- Business Idea Editor Section -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Business Idea Editor (Left Side - 2/3 width) -->
        <div class="lg:col-span-2">
          <BaseCard class="p-6 bg-white dark:bg-black">
            <div class="flex items-center justify-between mb-4">
              <BaseHeading
                as="h3"
                size="lg"
                weight="medium"
                class="text-muted-800 dark:text-white"
              >
                {{ $t("business_idea.your_business_idea") }}
              </BaseHeading>

              <div v-if="hasBusinessIdea && !isEditingIdea">
                <BaseButton
                  size="sm"
                  variant="outline"
                  color="primary"
                  @click="startEditing"
                >
                  <Icon name="ph:pencil-duotone" class="h-4 w-4 mr-2" />
                  {{ $t("business_idea.edit") }}
                </BaseButton>
              </div>
            </div>

            <!-- Business Idea Display/Editor -->
            <div v-if="!hasBusinessIdea || isEditingIdea">
              <BaseTextarea
                v-model="businessIdeaContent"
                :placeholder="$t('business_idea.business_idea_placeholder')"
                autogrow
                :min-height="200"
                class="w-full mb-4"
              />

              <div class="flex gap-2">
                <BaseButton
                  variant="primary"
                  @click="saveBusinessIdea"
                  :disabled="!businessIdeaContent.trim()"
                >
                  <Icon name="ph:check-duotone" class="h-4 w-4 mr-2" />
                  {{ $t("business_idea.save") }}
                </BaseButton>

                <BaseButton
                  v-if="hasBusinessIdea"
                  variant="outline"
                  color="muted"
                  @click="cancelEditing"
                >
                  {{ $t("profile.cancel") }}
                </BaseButton>
              </div>
            </div>

            <div v-else class="prose prose-muted dark:prose-invert max-w-none">
              <div v-html="businessIdea.content" />
            </div>

            <!-- Upload Document Option -->
            <div
              class="mt-6 pt-6 border-t border-muted-200 dark:border-muted-800"
            >
              <BaseButton variant="outline" color="muted" size="sm">
                <Icon name="ph:upload-duotone" class="h-4 w-4 mr-2" />
                {{ $t("business_idea.upload_document") }}
              </BaseButton>
            </div>
          </BaseCard>
        </div>

        <!-- Notes Section (Right Side - 1/3 width) -->
        <div class="lg:col-span-1">
          <BaseCard class="p-6 bg-white dark:bg-black">
            <BaseHeading
              as="h3"
              size="lg"
              weight="medium"
              class="text-muted-800 dark:text-white mb-4"
            >
              {{ $t("business_idea.notes_section") }}
            </BaseHeading>

            <!-- Post-it Notes Component -->
            <PostItNotes
              v-if="companyId"
              :company-id="companyId"
              :notes="notes"
              @update:notes="notes = $event"
              @note-saved="handleNoteSaved"
              @note-deleted="handleNoteDeleted"
            />
          </BaseCard>

          <!-- Collaborators Section -->
          <BaseCard class="p-6 bg-white dark:bg-black mt-6">
            <BaseHeading
              as="h3"
              size="lg"
              weight="medium"
              class="text-muted-800 dark:text-white mb-4"
            >
              {{ $t("business_idea.collaborators") }}
            </BaseHeading>

            <!-- Collaborator Selector Component -->
            <CollaboratorSelector
              v-if="companyId"
              :company-id="companyId"
              :collaborators="collaborators"
              :team-members="teamMembers"
              @collaborator-added="handleCollaboratorAdded"
              @collaborator-removed="handleCollaboratorRemoved"
            />
          </BaseCard>
        </div>
      </div>
    </div>
  </BaseLayerPage>
</template>
