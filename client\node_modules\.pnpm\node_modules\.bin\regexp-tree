#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/regexp-tree@0.1.27/node_modules/regexp-tree/bin/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/regexp-tree@0.1.27/node_modules/regexp-tree/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/regexp-tree@0.1.27/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/regexp-tree@0.1.27/node_modules/regexp-tree/bin/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/regexp-tree@0.1.27/node_modules/regexp-tree/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/regexp-tree@0.1.27/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../regexp-tree/bin/regexp-tree" "$@"
else
  exec node  "$basedir/../regexp-tree/bin/regexp-tree" "$@"
fi
