#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/jsesc@3.1.0/node_modules/jsesc/bin/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/jsesc@3.1.0/node_modules/jsesc/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/jsesc@3.1.0/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/jsesc@3.1.0/node_modules/jsesc/bin/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/jsesc@3.1.0/node_modules/jsesc/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/jsesc@3.1.0/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../jsesc/bin/jsesc" "$@"
else
  exec node  "$basedir/../jsesc/bin/jsesc" "$@"
fi
