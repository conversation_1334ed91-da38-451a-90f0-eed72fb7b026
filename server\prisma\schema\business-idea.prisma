// Business Idea Module Schema

model BusinessIdea {
  id          Int      @id @default(autoincrement())
  companyId   Int      @unique
  content     String?  @db.Text // Rich text content of the business idea
  isPublished Boolean  @default(false)
  createdBy   Int
  updatedBy   Int?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  company     Company  @relation(fields: [companyId], references: [id], onDelete: Cascade)
  creator     User     @relation("BusinessIdeaCreator", fields: [createdBy], references: [id])
  updater     User?    @relation("BusinessIdeaUpdater", fields: [updatedBy], references: [id])
  
  // Collaborators
  collaborators BusinessIdeaCollaborator[]
  
  // Notes
  notes       BusinessIdeaNote[]
}

model BusinessIdeaCollaborator {
  id            Int          @id @default(autoincrement())
  businessIdeaId Int
  userId        Int
  addedBy       Int
  addedAt       DateTime     @default(now())

  // Relations
  businessIdea  BusinessIdea @relation(fields: [businessIdeaId], references: [id], onDelete: Cascade)
  user          User         @relation("BusinessIdeaCollaborators", fields: [userId], references: [id])
  addedByUser   User         @relation("BusinessIdeaCollaboratorAdder", fields: [addedBy], references: [id])

  @@unique([businessIdeaId, userId])
}

model BusinessIdeaNote {
  id            Int          @id @default(autoincrement())
  businessIdeaId Int
  userId        Int
  content       String       @db.Text
  position      Int          @default(0) // For ordering notes
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt

  // Relations
  businessIdea  BusinessIdea @relation(fields: [businessIdeaId], references: [id], onDelete: Cascade)
  user          User         @relation("BusinessIdeaNotes", fields: [userId], references: [id])
}

model LearningMaterial {
  id          Int      @id @default(autoincrement())
  key         String   @unique // Unique identifier for the material (e.g., "what_is_business_idea")
  titleEn     String   // English title
  titleEt     String?  // Estonian title
  contentEn   String   @db.Text // English rich text content
  contentEt   String?  @db.Text // Estonian rich text content
  videoUrls   String[] // Array of YouTube video URLs
  isActive    Boolean  @default(true)
  order       Int      @default(0) // For ordering materials
  createdBy   Int?
  updatedBy   Int?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  creator     User?    @relation("LearningMaterialCreator", fields: [createdBy], references: [id])
  updater     User?    @relation("LearningMaterialUpdater", fields: [updatedBy], references: [id])
}

model BusinessIdeaGreeting {
  id        Int      @id @default(autoincrement())
  titleEn   String   // English title
  titleEt   String?  // Estonian title
  contentEn String   @db.Text // English rich text content
  contentEt String?  @db.Text // Estonian rich text content
  isActive  Boolean  @default(true)
  createdBy Int?
  updatedBy Int?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  creator   User?    @relation("GreetingCreator", fields: [createdBy], references: [id])
  updater   User?    @relation("GreetingUpdater", fields: [updatedBy], references: [id])
}
