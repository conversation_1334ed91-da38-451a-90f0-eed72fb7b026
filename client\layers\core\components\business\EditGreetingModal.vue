<script setup lang="ts">
import { ref, watch } from 'vue'
import { useNuiToasts } from '#imports'

interface Greeting {
  id: number
  title: string
  content: string
  isActive: boolean
}

interface Props {
  isOpen: boolean
  greeting: Greeting | null
}

interface Emits {
  (e: 'close'): void
  (e: 'saved', greeting: Greeting): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Composables
const { add: addToast } = useNuiToasts()

// State
const loading = ref(false)
const formData = ref({
  title: '',
  content: ''
})

// Watch for greeting changes
watch(() => props.greeting, (greeting) => {
  if (greeting) {
    formData.value = {
      title: greeting.title,
      content: greeting.content
    }
  }
}, { immediate: true })

// Methods
const saveGreeting = async () => {
  loading.value = true
  try {
    const response = await $fetch('/api/v1/business-idea/greeting', {
      method: 'PUT',
      body: {
        title: formData.value.title,
        content: formData.value.content
      }
    })

    emit('saved', response)
    emit('close')

    addToast({
      title: 'Success',
      message: 'Greeting updated successfully',
      color: 'success',
      icon: 'ph:check-duotone'
    })
  } catch (error) {
    console.error('Error saving greeting:', error)
    addToast({
      title: 'Error',
      message: 'Failed to save greeting',
      color: 'danger',
      icon: 'ph:warning-duotone'
    })
  } finally {
    loading.value = false
  }
}

const closeModal = () => {
  emit('close')
}
</script>

<template>
  <TairoModal
    :open="isOpen"
    size="lg"
    @close="closeModal"
  >
    <template #header>
      <div class="flex w-full items-center justify-between p-4 md:p-6">
        <h3 class="font-heading text-muted-900 text-lg font-medium leading-6 dark:text-white">
          Edit Welcome Greeting
        </h3>
        <BaseButtonClose @click="closeModal" />
      </div>
    </template>

    <div class="p-4 md:p-6">
      <div class="mx-auto w-full max-w-2xl space-y-6">
        <!-- Title -->
        <BaseField label="Title" required>
          <BaseInput
            v-model="formData.title"
            placeholder="Enter greeting title"
          />
        </BaseField>

        <!-- Content -->
        <BaseField label="Content" required>
          <RichTextEditor
            v-model="formData.content"
            placeholder="Enter the welcome message content..."
            :min-height="250"
          />
        </BaseField>

        <!-- Preview -->
        <BaseField label="Preview">
          <div class="border border-muted-200 dark:border-muted-800 rounded-lg p-4 bg-muted-50 dark:bg-muted-900/50">
            <h4 class="font-semibold text-muted-800 dark:text-white mb-2">
              {{ formData.title || 'Preview Title' }}
            </h4>
            <div 
              v-if="formData.content"
              class="prose prose-sm prose-muted dark:prose-invert max-w-none"
              v-html="formData.content"
            />
            <p v-else class="text-muted-500 italic">
              Content preview will appear here...
            </p>
          </div>
        </BaseField>

        <!-- Action Buttons -->
        <div class="flex gap-x-2 pt-4">
          <BaseButton
            variant="primary"
            class="flex-1"
            :disabled="!formData.title.trim() || !formData.content.trim() || loading"
            :loading="loading"
            @click="saveGreeting"
          >
            Save Changes
          </BaseButton>
          <BaseButton
            variant="outline"
            color="muted"
            class="flex-1"
            @click="closeModal"
            :disabled="loading"
          >
            Cancel
          </BaseButton>
        </div>
      </div>
    </div>
  </TairoModal>
</template>
