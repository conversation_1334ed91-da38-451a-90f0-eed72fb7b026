<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useNuiToasts } from '#imports'

interface Note {
  id?: number
  content: string
  position: number
  user?: {
    id: number
    firstName: string
    lastName: string
    avatar?: string
  }
  createdAt?: string
  updatedAt?: string
  isSaving?: boolean
  isNew?: boolean
}

interface Props {
  companyId: number
  notes: Note[]
}

interface Emits {
  (e: 'update:notes', notes: Note[]): void
  (e: 'note-saved', note: Note): void
  (e: 'note-deleted', noteId: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Composables
const { add: addToast } = useNuiToasts()

// State
const localNotes = ref<Note[]>([...props.notes])
const saveTimeouts = ref<Map<number | string, NodeJS.Timeout>>(new Map())

// Computed
const sortedNotes = computed(() => {
  return [...localNotes.value].sort((a, b) => a.position - b.position)
})

// Watch for prop changes
watch(() => props.notes, (newNotes) => {
  localNotes.value = [...newNotes]
}, { deep: true })

// Methods
const addNote = () => {
  const newNote: Note = {
    content: '',
    position: localNotes.value.length,
    isNew: true,
    isSaving: false
  }
  
  localNotes.value.push(newNote)
  emit('update:notes', localNotes.value)
  
  // Focus the new note after DOM update
  nextTick(() => {
    const textareas = document.querySelectorAll('.post-it-textarea')
    const lastTextarea = textareas[textareas.length - 1] as HTMLTextAreaElement
    if (lastTextarea) {
      lastTextarea.focus()
    }
  })
}

const updateNoteContent = (index: number, content: string) => {
  const note = localNotes.value[index]
  if (!note) return

  note.content = content
  
  // Clear existing timeout
  const timeoutKey = note.id || `new-${index}`
  const existingTimeout = saveTimeouts.value.get(timeoutKey)
  if (existingTimeout) {
    clearTimeout(existingTimeout)
  }

  // Set new timeout for auto-save
  const timeout = setTimeout(() => {
    saveNote(index)
  }, 3000) // 3 seconds delay

  saveTimeouts.value.set(timeoutKey, timeout)
}

const saveNote = async (index: number) => {
  const note = localNotes.value[index]
  if (!note || note.isSaving) return

  // Don't save empty notes
  if (!note.content.trim()) {
    if (!note.isNew) {
      // Delete empty existing note
      await deleteNote(index)
    }
    return
  }

  note.isSaving = true

  try {
    const response = await $fetch(`/api/v1/business-idea/company/${props.companyId}/notes`, {
      method: 'POST',
      body: {
        id: note.id,
        content: note.content,
        position: note.position
      }
    })

    // Update the note with response data
    Object.assign(note, response)
    note.isNew = false
    note.isSaving = false

    emit('note-saved', note)

    // Show save indicator briefly
    setTimeout(() => {
      note.isSaving = false
    }, 1000)

  } catch (error) {
    console.error('Error saving note:', error)
    note.isSaving = false
    
    addToast({
      title: 'Error',
      message: 'Failed to save note',
      color: 'danger',
      icon: 'ph:warning-duotone'
    })
  }
}

const deleteNote = async (index: number) => {
  const note = localNotes.value[index]
  if (!note) return

  if (note.id) {
    try {
      await $fetch(`/api/v1/business-idea/company/${props.companyId}/notes/${note.id}`, {
        method: 'DELETE'
      })
      
      emit('note-deleted', note.id)
    } catch (error) {
      console.error('Error deleting note:', error)
      addToast({
        title: 'Error',
        message: 'Failed to delete note',
        color: 'danger',
        icon: 'ph:warning-duotone'
      })
      return
    }
  }

  // Remove from local array
  localNotes.value.splice(index, 1)
  
  // Update positions
  localNotes.value.forEach((n, i) => {
    n.position = i
  })
  
  emit('update:notes', localNotes.value)
}

const autoResize = (event: Event) => {
  const textarea = event.target as HTMLTextAreaElement
  textarea.style.height = 'auto'
  textarea.style.height = textarea.scrollHeight + 'px'
}

// Cleanup timeouts on unmount
onUnmounted(() => {
  saveTimeouts.value.forEach(timeout => clearTimeout(timeout))
  saveTimeouts.value.clear()
})
</script>

<template>
  <div class="space-y-4">
    <!-- Existing Notes -->
    <div
      v-for="(note, index) in sortedNotes"
      :key="note.id || `new-${index}`"
      class="relative"
    >
      <!-- Post-it Note -->
      <div class="bg-yellow-100 dark:bg-yellow-900/20 border-l-4 border-yellow-400 p-4 rounded-r-lg shadow-sm hover:shadow-md transition-shadow">
        <textarea
          :value="note.content"
          class="post-it-textarea w-full bg-transparent border-none outline-none resize-none text-sm text-muted-800 dark:text-muted-200 placeholder:text-muted-400"
          :placeholder="$t('business_idea.start_writing')"
          rows="3"
          @input="updateNoteContent(index, ($event.target as HTMLTextAreaElement).value)"
          @input.capture="autoResize"
        />
        
        <!-- Note Footer -->
        <div class="flex items-center justify-between mt-2 pt-2 border-t border-yellow-200 dark:border-yellow-800">
          <div class="flex items-center gap-2">
            <!-- User Avatar -->
            <BaseAvatar
              v-if="note.user"
              :src="note.user.avatar"
              size="xs"
              class="shrink-0"
            />
            <span v-if="note.user" class="text-xs text-muted-500">
              {{ note.user.firstName }} {{ note.user.lastName }}
            </span>
          </div>
          
          <div class="flex items-center gap-2">
            <!-- Save Status -->
            <div class="flex items-center gap-1">
              <BaseSpinner v-if="note.isSaving" size="xs" />
              <span 
                v-else-if="!note.isNew && note.content.trim()"
                class="text-xs text-green-600 dark:text-green-400"
              >
                {{ $t('business_idea.note_saved') }}
              </span>
            </div>
            
            <!-- Delete Button -->
            <BaseButton
              size="icon-xs"
              variant="ghost"
              color="danger"
              @click="deleteNote(index)"
              class="opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <Icon name="ph:trash-duotone" class="h-3 w-3" />
            </BaseButton>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Note Button -->
    <BaseButton
      variant="outline"
      color="muted"
      size="sm"
      class="w-full border-dashed"
      @click="addNote"
    >
      <Icon name="ph:plus-duotone" class="h-4 w-4 mr-2" />
      {{ $t('business_idea.add_note') }}
    </BaseButton>
  </div>
</template>

<style scoped>
.post-it-textarea {
  font-family: 'Comic Sans MS', cursive, sans-serif;
}

.post-it-textarea:focus {
  outline: none;
}

/* Auto-resize textarea */
.post-it-textarea {
  overflow: hidden;
  min-height: 60px;
}
</style>
