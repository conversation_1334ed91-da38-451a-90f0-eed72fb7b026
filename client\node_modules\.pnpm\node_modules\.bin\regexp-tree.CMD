@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\regexp-tree@0.1.27\node_modules\regexp-tree\bin\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\regexp-tree@0.1.27\node_modules\regexp-tree\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\regexp-tree@0.1.27\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\regexp-tree@0.1.27\node_modules\regexp-tree\bin\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\regexp-tree@0.1.27\node_modules\regexp-tree\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\regexp-tree@0.1.27\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\regexp-tree\bin\regexp-tree" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\regexp-tree\bin\regexp-tree" %*
)
