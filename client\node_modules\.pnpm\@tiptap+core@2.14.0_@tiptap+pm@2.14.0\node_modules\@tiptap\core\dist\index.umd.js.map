{"version": 3, "file": "index.umd.js", "sources": ["../src/helpers/createChainableState.ts", "../src/CommandManager.ts", "../src/EventEmitter.ts", "../src/helpers/getExtensionField.ts", "../src/helpers/splitExtensions.ts", "../src/helpers/getAttributesFromExtensions.ts", "../src/helpers/getNodeType.ts", "../src/utilities/mergeAttributes.ts", "../src/helpers/getRenderedAttributes.ts", "../src/utilities/isFunction.ts", "../src/utilities/callOrReturn.ts", "../src/utilities/isEmptyObject.ts", "../src/utilities/fromString.ts", "../src/helpers/injectExtensionAttributesToParseRule.ts", "../src/helpers/getSchemaByResolvedExtensions.ts", "../src/helpers/getSchemaTypeByName.ts", "../src/helpers/isExtensionRulesEnabled.ts", "../src/helpers/getHTMLFromFragment.ts", "../src/helpers/getTextContentFromNodes.ts", "../src/utilities/isRegExp.ts", "../src/InputRule.ts", "../src/utilities/isPlainObject.ts", "../src/utilities/mergeDeep.ts", "../src/Mark.ts", "../src/utilities/isNumber.ts", "../src/PasteRule.ts", "../src/utilities/findDuplicates.ts", "../src/ExtensionManager.ts", "../src/Extension.ts", "../src/helpers/getTextBetween.ts", "../src/helpers/getTextSerializersFromSchema.ts", "../src/extensions/clipboardTextSerializer.ts", "../src/commands/blur.ts", "../src/commands/clearContent.ts", "../src/commands/clearNodes.ts", "../src/commands/command.ts", "../src/commands/createParagraphNear.ts", "../src/commands/cut.ts", "../src/commands/deleteCurrentNode.ts", "../src/commands/deleteNode.ts", "../src/commands/deleteRange.ts", "../src/commands/deleteSelection.ts", "../src/commands/enter.ts", "../src/commands/exitCode.ts", "../src/utilities/objectIncludes.ts", "../src/helpers/getMarkRange.ts", "../src/helpers/getMarkType.ts", "../src/commands/extendMarkRange.ts", "../src/commands/first.ts", "../src/helpers/isTextSelection.ts", "../src/utilities/minMax.ts", "../src/helpers/resolveFocusPosition.ts", "../src/utilities/isAndroid.ts", "../src/utilities/isiOS.ts", "../src/commands/focus.ts", "../src/commands/forEach.ts", "../src/commands/insertContent.ts", "../src/utilities/elementFromString.ts", "../src/helpers/createNodeFromContent.ts", "../src/helpers/selectionToInsertionEnd.ts", "../src/commands/insertContentAt.ts", "../src/commands/join.ts", "../src/commands/joinItemBackward.ts", "../src/commands/joinItemForward.ts", "../src/commands/joinTextblockBackward.ts", "../src/commands/joinTextblockForward.ts", "../src/utilities/isMacOS.ts", "../src/commands/keyboardShortcut.ts", "../src/helpers/isNodeActive.ts", "../src/commands/lift.ts", "../src/commands/liftEmptyBlock.ts", "../src/commands/liftListItem.ts", "../src/commands/newlineInCode.ts", "../src/helpers/getSchemaTypeNameByName.ts", "../src/utilities/deleteProps.ts", "../src/commands/resetAttributes.ts", "../src/commands/scrollIntoView.ts", "../src/commands/selectAll.ts", "../src/commands/selectNodeBackward.ts", "../src/commands/selectNodeForward.ts", "../src/commands/selectParentNode.ts", "../src/commands/selectTextblockEnd.ts", "../src/commands/selectTextblockStart.ts", "../src/helpers/createDocument.ts", "../src/commands/setContent.ts", "../src/helpers/getMarkAttributes.ts", "../src/helpers/combineTransactionSteps.ts", "../src/helpers/defaultBlockAt.ts", "../src/helpers/findChildren.ts", "../src/helpers/findChildrenInRange.ts", "../src/helpers/findParentNodeClosestToPos.ts", "../src/helpers/findParentNode.ts", "../src/helpers/getSchema.ts", "../src/helpers/generateHTML.ts", "../src/helpers/generateJSON.ts", "../src/helpers/getText.ts", "../src/helpers/generateText.ts", "../src/helpers/getNodeAttributes.ts", "../src/helpers/getAttributes.ts", "../src/utilities/removeDuplicates.ts", "../src/helpers/getChangedRanges.ts", "../src/helpers/getDebugJSON.ts", "../src/helpers/getMarksBetween.ts", "../src/helpers/getNodeAtPosition.ts", "../src/helpers/getSplittedAttributes.ts", "../src/helpers/isMarkActive.ts", "../src/helpers/isActive.ts", "../src/helpers/isAtEndOfNode.ts", "../src/helpers/isAtStartOfNode.ts", "../src/helpers/isList.ts", "../src/helpers/isNodeEmpty.ts", "../src/helpers/isNodeSelection.ts", "../src/helpers/posToDOMRect.ts", "../src/helpers/rewriteUnknownContent.ts", "../src/commands/setMark.ts", "../src/commands/setMeta.ts", "../src/commands/setNode.ts", "../src/commands/setNodeSelection.ts", "../src/commands/setTextSelection.ts", "../src/commands/sinkListItem.ts", "../src/commands/splitBlock.ts", "../src/commands/splitListItem.ts", "../src/commands/toggleList.ts", "../src/commands/toggleMark.ts", "../src/commands/toggleNode.ts", "../src/commands/toggleWrap.ts", "../src/commands/undoInputRule.ts", "../src/commands/unsetAllMarks.ts", "../src/commands/unsetMark.ts", "../src/commands/updateAttributes.ts", "../src/commands/wrapIn.ts", "../src/commands/wrapInList.ts", "../src/extensions/commands.ts", "../src/extensions/drop.ts", "../src/extensions/editable.ts", "../src/extensions/focusEvents.ts", "../src/extensions/keymap.ts", "../src/extensions/paste.ts", "../src/extensions/tabindex.ts", "../src/NodePos.ts", "../src/style.ts", "../src/utilities/createStyleTag.ts", "../src/Editor.ts", "../src/inputRules/markInputRule.ts", "../src/inputRules/nodeInputRule.ts", "../src/inputRules/textblockTypeInputRule.ts", "../src/inputRules/textInputRule.ts", "../src/inputRules/wrappingInputRule.ts", "../src/Node.ts", "../src/NodeView.ts", "../src/pasteRules/markPasteRule.ts", "../src/utilities/escapeForRegEx.ts", "../src/utilities/isString.ts", "../src/pasteRules/nodePasteRule.ts", "../src/pasteRules/textPasteRule.ts", "../src/Tracker.ts"], "sourcesContent": ["import { EditorState, Transaction } from '@tiptap/pm/state'\n\n/**\n * Takes a Transaction & Editor State and turns it into a chainable state object\n * @param config The transaction and state to create the chainable state from\n * @returns A chainable Editor state object\n */\nexport function createChainableState(config: {\n  transaction: Transaction\n  state: EditorState\n}): EditorState {\n  const { state, transaction } = config\n  let { selection } = transaction\n  let { doc } = transaction\n  let { storedMarks } = transaction\n\n  return {\n    ...state,\n    apply: state.apply.bind(state),\n    applyTransaction: state.applyTransaction.bind(state),\n    plugins: state.plugins,\n    schema: state.schema,\n    reconfigure: state.reconfigure.bind(state),\n    toJSON: state.toJSON.bind(state),\n    get storedMarks() {\n      return storedMarks\n    },\n    get selection() {\n      return selection\n    },\n    get doc() {\n      return doc\n    },\n    get tr() {\n      selection = transaction.selection\n      doc = transaction.doc\n      storedMarks = transaction.storedMarks\n\n      return transaction\n    },\n  }\n}\n", "import { EditorState, Transaction } from '@tiptap/pm/state'\n\nimport { Editor } from './Editor.js'\nimport { createChainableState } from './helpers/createChainableState.js'\nimport {\n  AnyCommands, CanCommands, ChainedCommands, CommandProps, SingleCommands,\n} from './types.js'\n\nexport class CommandManager {\n  editor: Editor\n\n  rawCommands: AnyCommands\n\n  customState?: EditorState\n\n  constructor(props: { editor: Editor; state?: EditorState }) {\n    this.editor = props.editor\n    this.rawCommands = this.editor.extensionManager.commands\n    this.customState = props.state\n  }\n\n  get hasCustomState(): boolean {\n    return !!this.customState\n  }\n\n  get state(): EditorState {\n    return this.customState || this.editor.state\n  }\n\n  get commands(): SingleCommands {\n    const { rawCommands, editor, state } = this\n    const { view } = editor\n    const { tr } = state\n    const props = this.buildProps(tr)\n\n    return Object.fromEntries(\n      Object.entries(rawCommands).map(([name, command]) => {\n        const method = (...args: any[]) => {\n          const callback = command(...args)(props)\n\n          if (!tr.getMeta('preventDispatch') && !this.hasCustomState) {\n            view.dispatch(tr)\n          }\n\n          return callback\n        }\n\n        return [name, method]\n      }),\n    ) as unknown as SingleCommands\n  }\n\n  get chain(): () => ChainedCommands {\n    return () => this.createChain()\n  }\n\n  get can(): () => CanCommands {\n    return () => this.createCan()\n  }\n\n  public createChain(startTr?: Transaction, shouldDispatch = true): ChainedCommands {\n    const { rawCommands, editor, state } = this\n    const { view } = editor\n    const callbacks: boolean[] = []\n    const hasStartTransaction = !!startTr\n    const tr = startTr || state.tr\n\n    const run = () => {\n      if (\n        !hasStartTransaction\n        && shouldDispatch\n        && !tr.getMeta('preventDispatch')\n        && !this.hasCustomState\n      ) {\n        view.dispatch(tr)\n      }\n\n      return callbacks.every(callback => callback === true)\n    }\n\n    const chain = {\n      ...Object.fromEntries(\n        Object.entries(rawCommands).map(([name, command]) => {\n          const chainedCommand = (...args: never[]) => {\n            const props = this.buildProps(tr, shouldDispatch)\n            const callback = command(...args)(props)\n\n            callbacks.push(callback)\n\n            return chain\n          }\n\n          return [name, chainedCommand]\n        }),\n      ),\n      run,\n    } as unknown as ChainedCommands\n\n    return chain\n  }\n\n  public createCan(startTr?: Transaction): CanCommands {\n    const { rawCommands, state } = this\n    const dispatch = false\n    const tr = startTr || state.tr\n    const props = this.buildProps(tr, dispatch)\n    const formattedCommands = Object.fromEntries(\n      Object.entries(rawCommands).map(([name, command]) => {\n        return [name, (...args: never[]) => command(...args)({ ...props, dispatch: undefined })]\n      }),\n    ) as unknown as SingleCommands\n\n    return {\n      ...formattedCommands,\n      chain: () => this.createChain(tr, dispatch),\n    } as CanCommands\n  }\n\n  public buildProps(tr: Transaction, shouldDispatch = true): CommandProps {\n    const { rawCommands, editor, state } = this\n    const { view } = editor\n\n    const props: CommandProps = {\n      tr,\n      editor,\n      view,\n      state: createChainableState({\n        state,\n        transaction: tr,\n      }),\n      dispatch: shouldDispatch ? () => undefined : undefined,\n      chain: () => this.createChain(tr, shouldDispatch),\n      can: () => this.createCan(tr),\n      get commands() {\n        return Object.fromEntries(\n          Object.entries(rawCommands).map(([name, command]) => {\n            return [name, (...args: never[]) => command(...args)(props)]\n          }),\n        ) as unknown as SingleCommands\n      },\n    }\n\n    return props\n  }\n}\n", "type StringKeyOf<T> = Extract<keyof T, string>\ntype CallbackType<\n  T extends Record<string, any>,\n  EventName extends StringKeyOf<T>,\n> = T[EventName] extends any[] ? T[EventName] : [T[EventName]]\ntype CallbackFunction<\n  T extends Record<string, any>,\n  EventName extends StringKeyOf<T>,\n> = (...props: CallbackType<T, EventName>) => any\n\nexport class EventEmitter<T extends Record<string, any>> {\n\n  private callbacks: { [key: string]: Array<(...args: any[])=>void> } = {}\n\n  public on<EventName extends StringKeyOf<T>>(event: EventName, fn: CallbackFunction<T, EventName>): this {\n    if (!this.callbacks[event]) {\n      this.callbacks[event] = []\n    }\n\n    this.callbacks[event].push(fn)\n\n    return this\n  }\n\n  public emit<EventName extends StringKeyOf<T>>(event: EventName, ...args: CallbackType<T, EventName>): this {\n    const callbacks = this.callbacks[event]\n\n    if (callbacks) {\n      callbacks.forEach(callback => callback.apply(this, args))\n    }\n\n    return this\n  }\n\n  public off<EventName extends StringKeyOf<T>>(event: EventName, fn?: CallbackFunction<T, EventName>): this {\n    const callbacks = this.callbacks[event]\n\n    if (callbacks) {\n      if (fn) {\n        this.callbacks[event] = callbacks.filter(callback => callback !== fn)\n      } else {\n        delete this.callbacks[event]\n      }\n    }\n\n    return this\n  }\n\n  public once<EventName extends StringKeyOf<T>>(event: EventName, fn: CallbackFunction<T, EventName>): this {\n    const onceFn = (...args: CallbackType<T, EventName>) => {\n      this.off(event, onceFn)\n      fn.apply(this, args)\n    }\n\n    return this.on(event, onceFn)\n  }\n\n  public removeAllListeners(): void {\n    this.callbacks = {}\n  }\n}\n", "import { AnyExtension, MaybeThisParameterType, RemoveThis } from '../types.js'\n\n/**\n * Returns a field from an extension\n * @param extension The Tiptap extension\n * @param field The field, for example `renderHTML` or `priority`\n * @param context The context object that should be passed as `this` into the function\n * @returns The field value\n */\nexport function getExtensionField<T = any>(\n  extension: AnyExtension,\n  field: string,\n  context?: Omit<MaybeThisParameterType<T>, 'parent'>,\n): RemoveThis<T> {\n\n  if (extension.config[field] === undefined && extension.parent) {\n    return getExtensionField(extension.parent, field, context)\n  }\n\n  if (typeof extension.config[field] === 'function') {\n    const value = extension.config[field].bind({\n      ...context,\n      parent: extension.parent\n        ? getExtensionField(extension.parent, field, context)\n        : null,\n    })\n\n    return value\n  }\n\n  return extension.config[field]\n}\n", "import { Extension } from '../Extension.js'\nimport { Mark } from '../Mark.js'\nimport { Node } from '../Node.js'\nimport { Extensions } from '../types.js'\n\nexport function splitExtensions(extensions: Extensions) {\n  const baseExtensions = extensions.filter(extension => extension.type === 'extension') as Extension[]\n  const nodeExtensions = extensions.filter(extension => extension.type === 'node') as Node[]\n  const markExtensions = extensions.filter(extension => extension.type === 'mark') as Mark[]\n\n  return {\n    baseExtensions,\n    nodeExtensions,\n    markExtensions,\n  }\n}\n", "import { MarkConfig, NodeConfig } from '../index.js'\nimport {\n  AnyConfig,\n  Attribute,\n  Attributes,\n  ExtensionAttribute,\n  Extensions,\n} from '../types.js'\nimport { getExtensionField } from './getExtensionField.js'\nimport { splitExtensions } from './splitExtensions.js'\n\n/**\n * Get a list of all extension attributes defined in `addAttribute` and `addGlobalAttribute`.\n * @param extensions List of extensions\n */\nexport function getAttributesFromExtensions(extensions: Extensions): ExtensionAttribute[] {\n  const extensionAttributes: ExtensionAttribute[] = []\n  const { nodeExtensions, markExtensions } = splitExtensions(extensions)\n  const nodeAndMarkExtensions = [...nodeExtensions, ...markExtensions]\n  const defaultAttribute: Required<Attribute> = {\n    default: null,\n    rendered: true,\n    renderHTML: null,\n    parseHTML: null,\n    keepOnSplit: true,\n    isRequired: false,\n  }\n\n  extensions.forEach(extension => {\n    const context = {\n      name: extension.name,\n      options: extension.options,\n      storage: extension.storage,\n      extensions: nodeAndMarkExtensions,\n    }\n\n    const addGlobalAttributes = getExtensionField<AnyConfig['addGlobalAttributes']>(\n      extension,\n      'addGlobalAttributes',\n      context,\n    )\n\n    if (!addGlobalAttributes) {\n      return\n    }\n\n    const globalAttributes = addGlobalAttributes()\n\n    globalAttributes.forEach(globalAttribute => {\n      globalAttribute.types.forEach(type => {\n        Object\n          .entries(globalAttribute.attributes)\n          .forEach(([name, attribute]) => {\n            extensionAttributes.push({\n              type,\n              name,\n              attribute: {\n                ...defaultAttribute,\n                ...attribute,\n              },\n            })\n          })\n      })\n    })\n  })\n\n  nodeAndMarkExtensions.forEach(extension => {\n    const context = {\n      name: extension.name,\n      options: extension.options,\n      storage: extension.storage,\n    }\n\n    const addAttributes = getExtensionField<NodeConfig['addAttributes'] | MarkConfig['addAttributes']>(\n      extension,\n      'addAttributes',\n      context,\n    )\n\n    if (!addAttributes) {\n      return\n    }\n\n    // TODO: remove `as Attributes`\n    const attributes = addAttributes() as Attributes\n\n    Object\n      .entries(attributes)\n      .forEach(([name, attribute]) => {\n        const mergedAttr = {\n          ...defaultAttribute,\n          ...attribute,\n        }\n\n        if (typeof mergedAttr?.default === 'function') {\n          mergedAttr.default = mergedAttr.default()\n        }\n\n        if (mergedAttr?.isRequired && mergedAttr?.default === undefined) {\n          delete mergedAttr.default\n        }\n\n        extensionAttributes.push({\n          type: extension.name,\n          name,\n          attribute: mergedAttr,\n        })\n      })\n  })\n\n  return extensionAttributes\n}\n", "import { NodeType, Schema } from '@tiptap/pm/model'\n\nexport function getNodeType(nameOrType: string | NodeType, schema: Schema): NodeType {\n  if (typeof nameOrType === 'string') {\n    if (!schema.nodes[nameOrType]) {\n      throw Error(\n        `There is no node type named '${nameOrType}'. Maybe you forgot to add the extension?`,\n      )\n    }\n\n    return schema.nodes[nameOrType]\n  }\n\n  return nameOrType\n}\n", "export function mergeAttributes(...objects: Record<string, any>[]): Record<string, any> {\n  return objects\n    .filter(item => !!item)\n    .reduce((items, item) => {\n      const mergedAttributes = { ...items }\n\n      Object.entries(item).forEach(([key, value]) => {\n        const exists = mergedAttributes[key]\n\n        if (!exists) {\n          mergedAttributes[key] = value\n\n          return\n        }\n\n        if (key === 'class') {\n          const valueClasses: string[] = value ? String(value).split(' ') : []\n          const existingClasses: string[] = mergedAttributes[key] ? mergedAttributes[key].split(' ') : []\n\n          const insertClasses = valueClasses.filter(\n            valueClass => !existingClasses.includes(valueClass),\n          )\n\n          mergedAttributes[key] = [...existingClasses, ...insertClasses].join(' ')\n        } else if (key === 'style') {\n          const newStyles: string[] = value ? value.split(';').map((style: string) => style.trim()).filter(Boolean) : []\n          const existingStyles: string[] = mergedAttributes[key] ? mergedAttributes[key].split(';').map((style: string) => style.trim()).filter(Boolean) : []\n\n          const styleMap = new Map<string, string>()\n\n          existingStyles.forEach(style => {\n            const [property, val] = style.split(':').map(part => part.trim())\n\n            styleMap.set(property, val)\n          })\n\n          newStyles.forEach(style => {\n            const [property, val] = style.split(':').map(part => part.trim())\n\n            styleMap.set(property, val)\n          })\n\n          mergedAttributes[key] = Array.from(styleMap.entries()).map(([property, val]) => `${property}: ${val}`).join('; ')\n        } else {\n          mergedAttributes[key] = value\n        }\n      })\n\n      return mergedAttributes\n    }, {})\n}\n", "import { Mark, Node } from '@tiptap/pm/model'\n\nimport { ExtensionAttribute } from '../types.js'\nimport { mergeAttributes } from '../utilities/mergeAttributes.js'\n\nexport function getRenderedAttributes(\n  nodeOrMark: Node | Mark,\n  extensionAttributes: ExtensionAttribute[],\n): Record<string, any> {\n  return extensionAttributes\n    .filter(\n      attribute => attribute.type === nodeOrMark.type.name,\n    )\n    .filter(item => item.attribute.rendered)\n    .map(item => {\n      if (!item.attribute.renderHTML) {\n        return {\n          [item.name]: nodeOrMark.attrs[item.name],\n        }\n      }\n\n      return item.attribute.renderHTML(nodeOrMark.attrs) || {}\n    })\n    .reduce((attributes, attribute) => mergeAttributes(attributes, attribute), {})\n}\n", "// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nexport function isFunction(value: any): value is Function {\n  return typeof value === 'function'\n}\n", "import { MaybeReturnType } from '../types.js'\nimport { isFunction } from './isFunction.js'\n\n/**\n * Optionally calls `value` as a function.\n * Otherwise it is returned directly.\n * @param value Function or any value.\n * @param context Optional context to bind to function.\n * @param props Optional props to pass to function.\n */\nexport function callOrReturn<T>(value: T, context: any = undefined, ...props: any[]): MaybeReturnType<T> {\n  if (isFunction(value)) {\n    if (context) {\n      return value.bind(context)(...props)\n    }\n\n    return value(...props)\n  }\n\n  return value as MaybeReturnType<T>\n}\n", "export function isEmptyObject(value = {}): boolean {\n  return Object.keys(value).length === 0 && value.constructor === Object\n}\n", "export function fromString(value: any): any {\n  if (typeof value !== 'string') {\n    return value\n  }\n\n  if (value.match(/^[+-]?(?:\\d*\\.)?\\d+$/)) {\n    return Number(value)\n  }\n\n  if (value === 'true') {\n    return true\n  }\n\n  if (value === 'false') {\n    return false\n  }\n\n  return value\n}\n", "import { ParseRule } from '@tiptap/pm/model'\n\nimport { ExtensionAttribute } from '../types.js'\nimport { fromString } from '../utilities/fromString.js'\n\n/**\n * This function merges extension attributes into parserule attributes (`attrs` or `getAttrs`).\n * Cancels when `getAttrs` returned `false`.\n * @param parseRule ProseMirror ParseRule\n * @param extensionAttributes List of attributes to inject\n */\nexport function injectExtensionAttributesToParseRule(\n  parseRule: ParseRule,\n  extensionAttributes: ExtensionAttribute[],\n): ParseRule {\n  if ('style' in parseRule) {\n    return parseRule\n  }\n\n  return {\n    ...parseRule,\n    getAttrs: (node: HTMLElement) => {\n      const oldAttributes = parseRule.getAttrs ? parseRule.getAttrs(node) : parseRule.attrs\n\n      if (oldAttributes === false) {\n        return false\n      }\n\n      const newAttributes = extensionAttributes.reduce((items, item) => {\n        const value = item.attribute.parseHTML\n          ? item.attribute.parseHTML(node)\n          : fromString((node).getAttribute(item.name))\n\n        if (value === null || value === undefined) {\n          return items\n        }\n\n        return {\n          ...items,\n          [item.name]: value,\n        }\n      }, {})\n\n      return { ...oldAttributes, ...newAttributes }\n    },\n  }\n}\n", "import {\n  <PERSON><PERSON><PERSON>, <PERSON>de<PERSON><PERSON>, <PERSON>hem<PERSON>, TagParseRule,\n} from '@tiptap/pm/model'\n\nimport { Editor, MarkConfig, NodeConfig } from '../index.js'\nimport { AnyConfig, Extensions } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\nimport { isEmptyObject } from '../utilities/isEmptyObject.js'\nimport { getAttributesFromExtensions } from './getAttributesFromExtensions.js'\nimport { getExtensionField } from './getExtensionField.js'\nimport { getRenderedAttributes } from './getRenderedAttributes.js'\nimport { injectExtensionAttributesToParseRule } from './injectExtensionAttributesToParseRule.js'\nimport { splitExtensions } from './splitExtensions.js'\n\nfunction cleanUpSchemaItem<T>(data: T) {\n  return Object.fromEntries(\n    // @ts-ignore\n    Object.entries(data).filter(([key, value]) => {\n      if (key === 'attrs' && isEmptyObject(value as object | undefined)) {\n        return false\n      }\n\n      return value !== null && value !== undefined\n    }),\n  ) as T\n}\n\n/**\n * Creates a new Prosemirror schema based on the given extensions.\n * @param extensions An array of Tiptap extensions\n * @param editor The editor instance\n * @returns A Prosemirror schema\n */\nexport function getSchemaByResolvedExtensions(extensions: Extensions, editor?: Editor): Schema {\n  const allAttributes = getAttributesFromExtensions(extensions)\n  const { nodeExtensions, markExtensions } = splitExtensions(extensions)\n  const topNode = nodeExtensions.find(extension => getExtensionField(extension, 'topNode'))?.name\n\n  const nodes = Object.fromEntries(\n    nodeExtensions.map(extension => {\n      const extensionAttributes = allAttributes.filter(\n        attribute => attribute.type === extension.name,\n      )\n      const context = {\n        name: extension.name,\n        options: extension.options,\n        storage: extension.storage,\n        editor,\n      }\n\n      const extraNodeFields = extensions.reduce((fields, e) => {\n        const extendNodeSchema = getExtensionField<AnyConfig['extendNodeSchema']>(\n          e,\n          'extendNodeSchema',\n          context,\n        )\n\n        return {\n          ...fields,\n          ...(extendNodeSchema ? extendNodeSchema(extension) : {}),\n        }\n      }, {})\n\n      const schema: NodeSpec = cleanUpSchemaItem({\n        ...extraNodeFields,\n        content: callOrReturn(\n          getExtensionField<NodeConfig['content']>(extension, 'content', context),\n        ),\n        marks: callOrReturn(getExtensionField<NodeConfig['marks']>(extension, 'marks', context)),\n        group: callOrReturn(getExtensionField<NodeConfig['group']>(extension, 'group', context)),\n        inline: callOrReturn(getExtensionField<NodeConfig['inline']>(extension, 'inline', context)),\n        atom: callOrReturn(getExtensionField<NodeConfig['atom']>(extension, 'atom', context)),\n        selectable: callOrReturn(\n          getExtensionField<NodeConfig['selectable']>(extension, 'selectable', context),\n        ),\n        draggable: callOrReturn(\n          getExtensionField<NodeConfig['draggable']>(extension, 'draggable', context),\n        ),\n        code: callOrReturn(getExtensionField<NodeConfig['code']>(extension, 'code', context)),\n        whitespace: callOrReturn(getExtensionField<NodeConfig['whitespace']>(extension, 'whitespace', context)),\n        linebreakReplacement: callOrReturn(getExtensionField<NodeConfig['linebreakReplacement']>(extension, 'linebreakReplacement', context)),\n        defining: callOrReturn(\n          getExtensionField<NodeConfig['defining']>(extension, 'defining', context),\n        ),\n        isolating: callOrReturn(\n          getExtensionField<NodeConfig['isolating']>(extension, 'isolating', context),\n        ),\n        attrs: Object.fromEntries(\n          extensionAttributes.map(extensionAttribute => {\n            return [extensionAttribute.name, { default: extensionAttribute?.attribute?.default }]\n          }),\n        ),\n      })\n\n      const parseHTML = callOrReturn(\n        getExtensionField<NodeConfig['parseHTML']>(extension, 'parseHTML', context),\n      )\n\n      if (parseHTML) {\n        schema.parseDOM = parseHTML.map(parseRule => injectExtensionAttributesToParseRule(parseRule, extensionAttributes)) as TagParseRule[]\n      }\n\n      const renderHTML = getExtensionField<NodeConfig['renderHTML']>(\n        extension,\n        'renderHTML',\n        context,\n      )\n\n      if (renderHTML) {\n        schema.toDOM = node => renderHTML({\n          node,\n          HTMLAttributes: getRenderedAttributes(node, extensionAttributes),\n        })\n      }\n\n      const renderText = getExtensionField<NodeConfig['renderText']>(\n        extension,\n        'renderText',\n        context,\n      )\n\n      if (renderText) {\n        schema.toText = renderText\n      }\n\n      return [extension.name, schema]\n    }),\n  )\n\n  const marks = Object.fromEntries(\n    markExtensions.map(extension => {\n      const extensionAttributes = allAttributes.filter(\n        attribute => attribute.type === extension.name,\n      )\n      const context = {\n        name: extension.name,\n        options: extension.options,\n        storage: extension.storage,\n        editor,\n      }\n\n      const extraMarkFields = extensions.reduce((fields, e) => {\n        const extendMarkSchema = getExtensionField<AnyConfig['extendMarkSchema']>(\n          e,\n          'extendMarkSchema',\n          context,\n        )\n\n        return {\n          ...fields,\n          ...(extendMarkSchema ? extendMarkSchema(extension as any) : {}),\n        }\n      }, {})\n\n      const schema: MarkSpec = cleanUpSchemaItem({\n        ...extraMarkFields,\n        inclusive: callOrReturn(\n          getExtensionField<MarkConfig['inclusive']>(extension, 'inclusive', context),\n        ),\n        excludes: callOrReturn(\n          getExtensionField<MarkConfig['excludes']>(extension, 'excludes', context),\n        ),\n        group: callOrReturn(getExtensionField<MarkConfig['group']>(extension, 'group', context)),\n        spanning: callOrReturn(\n          getExtensionField<MarkConfig['spanning']>(extension, 'spanning', context),\n        ),\n        code: callOrReturn(getExtensionField<MarkConfig['code']>(extension, 'code', context)),\n        attrs: Object.fromEntries(\n          extensionAttributes.map(extensionAttribute => {\n            return [extensionAttribute.name, { default: extensionAttribute?.attribute?.default }]\n          }),\n        ),\n      })\n\n      const parseHTML = callOrReturn(\n        getExtensionField<MarkConfig['parseHTML']>(extension, 'parseHTML', context),\n      )\n\n      if (parseHTML) {\n        schema.parseDOM = parseHTML.map(parseRule => injectExtensionAttributesToParseRule(parseRule, extensionAttributes))\n      }\n\n      const renderHTML = getExtensionField<MarkConfig['renderHTML']>(\n        extension,\n        'renderHTML',\n        context,\n      )\n\n      if (renderHTML) {\n        schema.toDOM = mark => renderHTML({\n          mark,\n          HTMLAttributes: getRenderedAttributes(mark, extensionAttributes),\n        })\n      }\n\n      return [extension.name, schema]\n    }),\n  )\n\n  return new Schema({\n    topNode,\n    nodes,\n    marks,\n  })\n}\n", "import { MarkType, NodeType, Schema } from '@tiptap/pm/model'\n\n/**\n * Tries to get a node or mark type by its name.\n * @param name The name of the node or mark type\n * @param schema The Prosemiror schema to search in\n * @returns The node or mark type, or null if it doesn't exist\n */\nexport function getSchemaTypeByName(name: string, schema: Schema): NodeType | MarkType | null {\n  return schema.nodes[name] || schema.marks[name] || null\n}\n", "import { AnyExtension, EnableRules } from '../types.js'\n\nexport function isExtensionRulesEnabled(extension: AnyExtension, enabled: EnableRules): boolean {\n  if (Array.isArray(enabled)) {\n    return enabled.some(enabledExtension => {\n      const name = typeof enabledExtension === 'string'\n        ? enabledExtension\n        : enabledExtension.name\n\n      return name === extension.name\n    })\n  }\n\n  return enabled\n}\n", "import { DOMSerializer, Fragment, Schema } from '@tiptap/pm/model'\n\nexport function getHTMLFromFragment(fragment: Fragment, schema: Schema): string {\n  const documentFragment = DOMSerializer.fromSchema(schema).serializeFragment(fragment)\n\n  const temporaryDocument = document.implementation.createHTMLDocument()\n  const container = temporaryDocument.createElement('div')\n\n  container.appendChild(documentFragment)\n\n  return container.innerHTML\n}\n", "import { ResolvedPos } from '@tiptap/pm/model'\n\n/**\n * Returns the text content of a resolved prosemirror position\n * @param $from The resolved position to get the text content from\n * @param maxMatch The maximum number of characters to match\n * @returns The text content\n */\nexport const getTextContentFromNodes = ($from: ResolvedPos, maxMatch = 500) => {\n  let textBefore = ''\n\n  const sliceEndPos = $from.parentOffset\n\n  $from.parent.nodesBetween(\n    Math.max(0, sliceEndPos - maxMatch),\n    sliceEndPos,\n    (node, pos, parent, index) => {\n      const chunk = node.type.spec.toText?.({\n        node,\n        pos,\n        parent,\n        index,\n      })\n        || node.textContent\n        || '%leaf%'\n\n      textBefore += node.isAtom && !node.isText ? chunk : chunk.slice(0, Math.max(0, sliceEndPos - pos))\n    },\n  )\n\n  return textBefore\n}\n", "export function isRegExp(value: any): value is RegExp {\n  return Object.prototype.toString.call(value) === '[object RegExp]'\n}\n", "import { Fragment, Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { EditorState, Plugin, TextSelection } from '@tiptap/pm/state'\n\nimport { CommandManager } from './CommandManager.js'\nimport { Editor } from './Editor.js'\nimport { createChainableState } from './helpers/createChainableState.js'\nimport { getHTMLFromFragment } from './helpers/getHTMLFromFragment.js'\nimport { getTextContentFromNodes } from './helpers/getTextContentFromNodes.js'\nimport {\n  CanCommands,\n  ChainedCommands,\n  ExtendedRegExpMatchArray,\n  Range,\n  SingleCommands,\n} from './types.js'\nimport { isRegExp } from './utilities/isRegExp.js'\n\nexport type InputRuleMatch = {\n  index: number;\n  text: string;\n  replaceWith?: string;\n  match?: RegExpMatchArray;\n  data?: Record<string, any>;\n};\n\nexport type InputRuleFinder = RegExp | ((text: string) => InputRuleMatch | null);\n\nexport class InputRule {\n  find: InputRuleFinder\n\n  handler: (props: {\n    state: EditorState;\n    range: Range;\n    match: ExtendedRegExpMatchArray;\n    commands: SingleCommands;\n    chain: () => ChainedCommands;\n    can: () => CanCommands;\n  }) => void | null\n\n  constructor(config: {\n    find: InputRuleFinder;\n    handler: (props: {\n      state: EditorState;\n      range: Range;\n      match: ExtendedRegExpMatchArray;\n      commands: SingleCommands;\n      chain: () => ChainedCommands;\n      can: () => CanCommands;\n    }) => void | null;\n  }) {\n    this.find = config.find\n    this.handler = config.handler\n  }\n}\n\nconst inputRuleMatcherHandler = (\n  text: string,\n  find: InputRuleFinder,\n): ExtendedRegExpMatchArray | null => {\n  if (isRegExp(find)) {\n    return find.exec(text)\n  }\n\n  const inputRuleMatch = find(text)\n\n  if (!inputRuleMatch) {\n    return null\n  }\n\n  const result: ExtendedRegExpMatchArray = [inputRuleMatch.text]\n\n  result.index = inputRuleMatch.index\n  result.input = text\n  result.data = inputRuleMatch.data\n\n  if (inputRuleMatch.replaceWith) {\n    if (!inputRuleMatch.text.includes(inputRuleMatch.replaceWith)) {\n      console.warn(\n        '[tiptap warn]: \"inputRuleMatch.replaceWith\" must be part of \"inputRuleMatch.text\".',\n      )\n    }\n\n    result.push(inputRuleMatch.replaceWith)\n  }\n\n  return result\n}\n\nfunction run(config: {\n  editor: Editor;\n  from: number;\n  to: number;\n  text: string;\n  rules: InputRule[];\n  plugin: Plugin;\n}): boolean {\n  const {\n    editor, from, to, text, rules, plugin,\n  } = config\n  const { view } = editor\n\n  if (view.composing) {\n    return false\n  }\n\n  const $from = view.state.doc.resolve(from)\n\n  if (\n    // check for code node\n    $from.parent.type.spec.code\n    // check for code mark\n    || !!($from.nodeBefore || $from.nodeAfter)?.marks.find(mark => mark.type.spec.code)\n  ) {\n    return false\n  }\n\n  let matched = false\n\n  const textBefore = getTextContentFromNodes($from) + text\n\n  rules.forEach(rule => {\n    if (matched) {\n      return\n    }\n\n    const match = inputRuleMatcherHandler(textBefore, rule.find)\n\n    if (!match) {\n      return\n    }\n\n    const tr = view.state.tr\n    const state = createChainableState({\n      state: view.state,\n      transaction: tr,\n    })\n    const range = {\n      from: from - (match[0].length - text.length),\n      to,\n    }\n\n    const { commands, chain, can } = new CommandManager({\n      editor,\n      state,\n    })\n\n    const handler = rule.handler({\n      state,\n      range,\n      match,\n      commands,\n      chain,\n      can,\n    })\n\n    // stop if there are no changes\n    if (handler === null || !tr.steps.length) {\n      return\n    }\n\n    // store transform as meta data\n    // so we can undo input rules within the `undoInputRules` command\n    tr.setMeta(plugin, {\n      transform: tr,\n      from,\n      to,\n      text,\n    })\n\n    view.dispatch(tr)\n    matched = true\n  })\n\n  return matched\n}\n\n/**\n * Create an input rules plugin. When enabled, it will cause text\n * input that matches any of the given rules to trigger the rule’s\n * action.\n */\nexport function inputRulesPlugin(props: { editor: Editor; rules: InputRule[] }): Plugin {\n  const { editor, rules } = props\n  const plugin = new Plugin({\n    state: {\n      init() {\n        return null\n      },\n      apply(tr, prev, state) {\n        const stored = tr.getMeta(plugin)\n\n        if (stored) {\n          return stored\n        }\n\n        // if InputRule is triggered by insertContent()\n        const simulatedInputMeta = tr.getMeta('applyInputRules') as\n          | undefined\n          | {\n              from: number;\n              text: string | ProseMirrorNode | Fragment;\n            }\n        const isSimulatedInput = !!simulatedInputMeta\n\n        if (isSimulatedInput) {\n          setTimeout(() => {\n            let { text } = simulatedInputMeta\n\n            if (typeof text === 'string') {\n              text = text as string\n            } else {\n              text = getHTMLFromFragment(Fragment.from(text), state.schema)\n            }\n\n            const { from } = simulatedInputMeta\n            const to = from + text.length\n\n            run({\n              editor,\n              from,\n              to,\n              text,\n              rules,\n              plugin,\n            })\n          })\n        }\n\n        return tr.selectionSet || tr.docChanged ? null : prev\n      },\n    },\n\n    props: {\n      handleTextInput(view, from, to, text) {\n        return run({\n          editor,\n          from,\n          to,\n          text,\n          rules,\n          plugin,\n        })\n      },\n\n      handleDOMEvents: {\n        compositionend: view => {\n          setTimeout(() => {\n            const { $cursor } = view.state.selection as TextSelection\n\n            if ($cursor) {\n              run({\n                editor,\n                from: $cursor.pos,\n                to: $cursor.pos,\n                text: '',\n                rules,\n                plugin,\n              })\n            }\n          })\n\n          return false\n        },\n      },\n\n      // add support for input rules to trigger on enter\n      // this is useful for example for code blocks\n      handleKeyDown(view, event) {\n        if (event.key !== 'Enter') {\n          return false\n        }\n\n        const { $cursor } = view.state.selection as TextSelection\n\n        if ($cursor) {\n          return run({\n            editor,\n            from: $cursor.pos,\n            to: $cursor.pos,\n            text: '\\n',\n            rules,\n            plugin,\n          })\n        }\n\n        return false\n      },\n    },\n\n    // @ts-ignore\n    isInputRules: true,\n  }) as Plugin\n\n  return plugin\n}\n", "// see: https://github.com/mesqueeb/is-what/blob/88d6e4ca92fb2baab6003c54e02eedf4e729e5ab/src/index.ts\n\nfunction getType(value: any): string {\n  return Object.prototype.toString.call(value).slice(8, -1)\n}\n\nexport function isPlainObject(value: any): value is Record<string, any> {\n  if (getType(value) !== 'Object') {\n    return false\n  }\n\n  return value.constructor === Object && Object.getPrototypeOf(value) === Object.prototype\n}\n", "import { isPlainObject } from './isPlainObject.js'\n\nexport function mergeDeep(target: Record<string, any>, source: Record<string, any>): Record<string, any> {\n  const output = { ...target }\n\n  if (isPlainObject(target) && isPlainObject(source)) {\n    Object.keys(source).forEach(key => {\n      if (isPlainObject(source[key]) && isPlainObject(target[key])) {\n        output[key] = mergeDeep(target[key], source[key])\n      } else {\n        output[key] = source[key]\n      }\n    })\n  }\n\n  return output\n}\n", "import {\n  DOMOutputSpec, Mark as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Mark<PERSON><PERSON>,\n} from '@tiptap/pm/model'\nimport { Plugin, Transaction } from '@tiptap/pm/state'\n\nimport { Editor } from './Editor.js'\nimport { getExtensionField } from './helpers/getExtensionField.js'\nimport { MarkConfig } from './index.js'\nimport { InputRule } from './InputRule.js'\nimport { Node } from './Node.js'\nimport { PasteRule } from './PasteRule.js'\nimport {\n  AnyConfig,\n  Attributes,\n  Extensions,\n  GlobalAttributes,\n  KeyboardShortcutCommand,\n  ParentConfig,\n  RawCommands,\n} from './types.js'\nimport { callOrReturn } from './utilities/callOrReturn.js'\nimport { mergeDeep } from './utilities/mergeDeep.js'\n\ndeclare module '@tiptap/core' {\n  export interface MarkConfig<Options = any, Storage = any> {\n    // @ts-ignore - this is a dynamic key\n    [key: string]: any\n\n    /**\n     * The extension name - this must be unique.\n     * It will be used to identify the extension.\n     *\n     * @example 'myExtension'\n     */\n    name: string\n\n    /**\n     * The priority of your extension. The higher, the earlier it will be called\n     * and will take precedence over other extensions with a lower priority.\n     * @default 100\n     * @example 101\n     */\n    priority?: number\n\n    /**\n     * The default options for this extension.\n     * @example\n     * defaultOptions: {\n     *   myOption: 'foo',\n     *   myOtherOption: 10,\n     * }\n     */\n    defaultOptions?: Options\n\n    /**\n     * This method will add options to this extension\n     * @see https://tiptap.dev/guide/custom-extensions#settings\n     * @example\n     * addOptions() {\n     *  return {\n     *    myOption: 'foo',\n     *    myOtherOption: 10,\n     * }\n     */\n    addOptions?: (this: {\n      name: string\n      parent: Exclude<ParentConfig<MarkConfig<Options, Storage>>['addOptions'], undefined>\n    }) => Options\n\n    /**\n     * The default storage this extension can save data to.\n     * @see https://tiptap.dev/guide/custom-extensions#storage\n     * @example\n     * defaultStorage: {\n     *   prefetchedUsers: [],\n     *   loading: false,\n     * }\n     */\n    addStorage?: (this: {\n      name: string\n      options: Options\n      parent: Exclude<ParentConfig<MarkConfig<Options, Storage>>['addStorage'], undefined>\n    }) => Storage\n\n    /**\n     * This function adds globalAttributes to specific nodes.\n     * @see https://tiptap.dev/guide/custom-extensions#global-attributes\n     * @example\n     * addGlobalAttributes() {\n     *   return [\n     *     {\n             // Extend the following extensions\n     *       types: [\n     *         'heading',\n     *         'paragraph',\n     *       ],\n     *       // … with those attributes\n     *       attributes: {\n     *         textAlign: {\n     *           default: 'left',\n     *           renderHTML: attributes => ({\n     *             style: `text-align: ${attributes.textAlign}`,\n     *           }),\n     *           parseHTML: element => element.style.textAlign || 'left',\n     *         },\n     *       },\n     *     },\n     *   ]\n     * }\n     */\n    addGlobalAttributes?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      extensions: (Node | Mark)[]\n      parent: ParentConfig<MarkConfig<Options, Storage>>['addGlobalAttributes']\n    }) => GlobalAttributes\n\n    /**\n     * This function adds commands to the editor\n     * @see https://tiptap.dev/guide/custom-extensions#keyboard-shortcuts\n     * @example\n     * addCommands() {\n     *   return {\n     *     myCommand: () => ({ chain }) => chain().setMark('type', 'foo').run(),\n     *   }\n     * }\n     */\n    addCommands?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: MarkType\n      parent: ParentConfig<MarkConfig<Options, Storage>>['addCommands']\n    }) => Partial<RawCommands>\n\n    /**\n     * This function registers keyboard shortcuts.\n     * @see https://tiptap.dev/guide/custom-extensions#keyboard-shortcuts\n     * @example\n     * addKeyboardShortcuts() {\n     *   return {\n     *     'Mod-l': () => this.editor.commands.toggleBulletList(),\n     *   }\n     * },\n     */\n    addKeyboardShortcuts?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: MarkType\n      parent: ParentConfig<MarkConfig<Options, Storage>>['addKeyboardShortcuts']\n    }) => {\n      [key: string]: KeyboardShortcutCommand\n    }\n\n    /**\n     * This function adds input rules to the editor.\n     * @see https://tiptap.dev/guide/custom-extensions#input-rules\n     * @example\n     * addInputRules() {\n     *   return [\n     *     markInputRule({\n     *       find: inputRegex,\n     *       type: this.type,\n     *     }),\n     *   ]\n     * },\n     */\n    addInputRules?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: MarkType\n      parent: ParentConfig<MarkConfig<Options, Storage>>['addInputRules']\n    }) => InputRule[]\n\n    /**\n     * This function adds paste rules to the editor.\n     * @see https://tiptap.dev/guide/custom-extensions#paste-rules\n     * @example\n     * addPasteRules() {\n     *   return [\n     *     markPasteRule({\n     *       find: pasteRegex,\n     *       type: this.type,\n     *     }),\n     *   ]\n     * },\n     */\n    addPasteRules?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: MarkType\n      parent: ParentConfig<MarkConfig<Options, Storage>>['addPasteRules']\n    }) => PasteRule[]\n\n    /**\n     * This function adds Prosemirror plugins to the editor\n     * @see https://tiptap.dev/guide/custom-extensions#prosemirror-plugins\n     * @example\n     * addProseMirrorPlugins() {\n     *   return [\n     *     customPlugin(),\n     *   ]\n     * }\n     */\n    addProseMirrorPlugins?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: MarkType\n      parent: ParentConfig<MarkConfig<Options, Storage>>['addProseMirrorPlugins']\n    }) => Plugin[]\n\n    /**\n     * This function adds additional extensions to the editor. This is useful for\n     * building extension kits.\n     * @example\n     * addExtensions() {\n     *   return [\n     *     BulletList,\n     *     OrderedList,\n     *     ListItem\n     *   ]\n     * }\n     */\n    addExtensions?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      parent: ParentConfig<MarkConfig<Options, Storage>>['addExtensions']\n    }) => Extensions\n\n    /**\n     * This function extends the schema of the node.\n     * @example\n     * extendNodeSchema() {\n     *   return {\n     *     group: 'inline',\n     *     selectable: false,\n     *   }\n     * }\n     */\n    extendNodeSchema?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<MarkConfig<Options, Storage>>['extendNodeSchema']\n          },\n          extension: Node,\n        ) => Record<string, any>)\n      | null\n\n    /**\n     * This function extends the schema of the mark.\n     * @example\n     * extendMarkSchema() {\n     *   return {\n     *     group: 'inline',\n     *     selectable: false,\n     *   }\n     * }\n     */\n    extendMarkSchema?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<MarkConfig<Options, Storage>>['extendMarkSchema']\n          },\n          extension: Mark,\n        ) => Record<string, any>)\n      | null\n\n    /**\n     * The editor is not ready yet.\n     */\n    onBeforeCreate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: MarkType\n          parent: ParentConfig<MarkConfig<Options, Storage>>['onBeforeCreate']\n        }) => void)\n      | null\n\n    /**\n     * The editor is ready.\n     */\n    onCreate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: MarkType\n          parent: ParentConfig<MarkConfig<Options, Storage>>['onCreate']\n        }) => void)\n      | null\n\n    /**\n     * The content has changed.\n     */\n    onUpdate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: MarkType\n          parent: ParentConfig<MarkConfig<Options, Storage>>['onUpdate']\n        }) => void)\n      | null\n\n    /**\n     * The selection has changed.\n     */\n    onSelectionUpdate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: MarkType\n          parent: ParentConfig<MarkConfig<Options, Storage>>['onSelectionUpdate']\n        }) => void)\n      | null\n\n    /**\n     * The editor state has changed.\n     */\n    onTransaction?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            type: MarkType\n            parent: ParentConfig<MarkConfig<Options, Storage>>['onTransaction']\n          },\n          props: {\n            editor: Editor\n            transaction: Transaction\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor is focused.\n     */\n    onFocus?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            type: MarkType\n            parent: ParentConfig<MarkConfig<Options, Storage>>['onFocus']\n          },\n          props: {\n            event: FocusEvent\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor isn’t focused anymore.\n     */\n    onBlur?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            type: MarkType\n            parent: ParentConfig<MarkConfig<Options, Storage>>['onBlur']\n          },\n          props: {\n            event: FocusEvent\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor is destroyed.\n     */\n    onDestroy?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: MarkType\n          parent: ParentConfig<MarkConfig<Options, Storage>>['onDestroy']\n        }) => void)\n      | null\n\n    /**\n     * Keep mark after split node\n     */\n    keepOnSplit?: boolean | (() => boolean)\n\n    /**\n     * Inclusive\n     */\n    inclusive?:\n      | MarkSpec['inclusive']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<MarkConfig<Options, Storage>>['inclusive']\n          editor?: Editor\n        }) => MarkSpec['inclusive'])\n\n    /**\n     * Excludes\n     */\n    excludes?:\n      | MarkSpec['excludes']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<MarkConfig<Options, Storage>>['excludes']\n          editor?: Editor\n        }) => MarkSpec['excludes'])\n\n    /**\n     * Marks this Mark as exitable\n     */\n    exitable?: boolean | (() => boolean)\n\n    /**\n     * Group\n     */\n    group?:\n      | MarkSpec['group']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<MarkConfig<Options, Storage>>['group']\n          editor?: Editor\n        }) => MarkSpec['group'])\n\n    /**\n     * Spanning\n     */\n    spanning?:\n      | MarkSpec['spanning']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<MarkConfig<Options, Storage>>['spanning']\n          editor?: Editor\n        }) => MarkSpec['spanning'])\n\n    /**\n     * Code\n     */\n    code?:\n      | boolean\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<MarkConfig<Options, Storage>>['code']\n          editor?: Editor\n        }) => boolean)\n\n    /**\n     * Parse HTML\n     */\n    parseHTML?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      parent: ParentConfig<MarkConfig<Options, Storage>>['parseHTML']\n      editor?: Editor\n    }) => MarkSpec['parseDOM']\n\n    /**\n     * Render HTML\n     */\n    renderHTML?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<MarkConfig<Options, Storage>>['renderHTML']\n            editor?: Editor\n          },\n          props: {\n            mark: ProseMirrorMark\n            HTMLAttributes: Record<string, any>\n          },\n        ) => DOMOutputSpec)\n      | null\n\n    /**\n     * Attributes\n     */\n    addAttributes?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      parent: ParentConfig<MarkConfig<Options, Storage>>['addAttributes']\n      editor?: Editor\n    // eslint-disable-next-line @typescript-eslint/no-empty-object-type\n    }) => Attributes | {}\n  }\n}\n\n/**\n * The Mark class is used to create custom mark extensions.\n * @see https://tiptap.dev/api/extensions#create-a-new-extension\n */\nexport class Mark<Options = any, Storage = any> {\n  type = 'mark'\n\n  name = 'mark'\n\n  parent: Mark | null = null\n\n  child: Mark | null = null\n\n  options: Options\n\n  storage: Storage\n\n  config: MarkConfig = {\n    name: this.name,\n    defaultOptions: {},\n  }\n\n  constructor(config: Partial<MarkConfig<Options, Storage>> = {}) {\n    this.config = {\n      ...this.config,\n      ...config,\n    }\n\n    this.name = this.config.name\n\n    if (config.defaultOptions && Object.keys(config.defaultOptions).length > 0) {\n      console.warn(\n        `[tiptap warn]: BREAKING CHANGE: \"defaultOptions\" is deprecated. Please use \"addOptions\" instead. Found in extension: \"${this.name}\".`,\n      )\n    }\n\n    // TODO: remove `addOptions` fallback\n    this.options = this.config.defaultOptions\n\n    if (this.config.addOptions) {\n      this.options = callOrReturn(\n        getExtensionField<AnyConfig['addOptions']>(this, 'addOptions', {\n          name: this.name,\n        }),\n      )\n    }\n\n    this.storage = callOrReturn(\n      getExtensionField<AnyConfig['addStorage']>(this, 'addStorage', {\n        name: this.name,\n        options: this.options,\n      }),\n    ) || {}\n  }\n\n  static create<O = any, S = any>(config: Partial<MarkConfig<O, S>> = {}) {\n    return new Mark<O, S>(config)\n  }\n\n  configure(options: Partial<Options> = {}) {\n    // return a new instance so we can use the same extension\n    // with different calls of `configure`\n    const extension = this.extend<Options, Storage>({\n      ...this.config,\n      addOptions: () => {\n        return mergeDeep(this.options as Record<string, any>, options) as Options\n      },\n    })\n\n    // Always preserve the current name\n    extension.name = this.name\n    // Set the parent to be our parent\n    extension.parent = this.parent\n\n    return extension\n  }\n\n  extend<ExtendedOptions = Options, ExtendedStorage = Storage>(\n    extendedConfig: Partial<MarkConfig<ExtendedOptions, ExtendedStorage>> = {},\n  ) {\n    const extension = new Mark<ExtendedOptions, ExtendedStorage>(extendedConfig)\n\n    extension.parent = this\n\n    this.child = extension\n\n    extension.name = extendedConfig.name ? extendedConfig.name : extension.parent.name\n\n    if (extendedConfig.defaultOptions && Object.keys(extendedConfig.defaultOptions).length > 0) {\n      console.warn(\n        `[tiptap warn]: BREAKING CHANGE: \"defaultOptions\" is deprecated. Please use \"addOptions\" instead. Found in extension: \"${extension.name}\".`,\n      )\n    }\n\n    extension.options = callOrReturn(\n      getExtensionField<AnyConfig['addOptions']>(extension, 'addOptions', {\n        name: extension.name,\n      }),\n    )\n\n    extension.storage = callOrReturn(\n      getExtensionField<AnyConfig['addStorage']>(extension, 'addStorage', {\n        name: extension.name,\n        options: extension.options,\n      }),\n    )\n\n    return extension\n  }\n\n  static handleExit({ editor, mark }: { editor: Editor; mark: Mark }) {\n    const { tr } = editor.state\n    const currentPos = editor.state.selection.$from\n    const isAtEnd = currentPos.pos === currentPos.end()\n\n    if (isAtEnd) {\n      const currentMarks = currentPos.marks()\n      const isInMark = !!currentMarks.find(m => m?.type.name === mark.name)\n\n      if (!isInMark) {\n        return false\n      }\n\n      const removeMark = currentMarks.find(m => m?.type.name === mark.name)\n\n      if (removeMark) {\n        tr.removeStoredMark(removeMark)\n      }\n      tr.insertText(' ', currentPos.pos)\n\n      editor.view.dispatch(tr)\n\n      return true\n    }\n\n    return false\n  }\n}\n", "export function isNumber(value: any): value is number {\n  return typeof value === 'number'\n}\n", "import { Fragment, Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { EditorState, Plugin } from '@tiptap/pm/state'\n\nimport { CommandManager } from './CommandManager.js'\nimport { Editor } from './Editor.js'\nimport { createChainableState } from './helpers/createChainableState.js'\nimport { getHTMLFromFragment } from './helpers/getHTMLFromFragment.js'\nimport {\n  CanCommands,\n  ChainedCommands,\n  ExtendedRegExpMatchArray,\n  Range,\n  SingleCommands,\n} from './types.js'\nimport { isNumber } from './utilities/isNumber.js'\nimport { isRegExp } from './utilities/isRegExp.js'\n\nexport type PasteRuleMatch = {\n  index: number;\n  text: string;\n  replaceWith?: string;\n  match?: RegExpMatchArray;\n  data?: Record<string, any>;\n};\n\nexport type PasteRuleFinder =\n  | RegExp\n  | ((text: string, event?: ClipboardEvent | null) => PasteRuleMatch[] | null | undefined);\n\n/**\n * Paste rules are used to react to pasted content.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#paste-rules\n */\nexport class PasteRule {\n  find: PasteRuleFinder\n\n  handler: (props: {\n    state: EditorState;\n    range: Range;\n    match: ExtendedRegExpMatchArray;\n    commands: SingleCommands;\n    chain: () => ChainedCommands;\n    can: () => CanCommands;\n    pasteEvent: ClipboardEvent | null;\n    dropEvent: DragEvent | null;\n  }) => void | null\n\n  constructor(config: {\n    find: PasteRuleFinder;\n    handler: (props: {\n      can: () => CanCommands;\n      chain: () => ChainedCommands;\n      commands: SingleCommands;\n      dropEvent: DragEvent | null;\n      match: ExtendedRegExpMatchArray;\n      pasteEvent: ClipboardEvent | null;\n      range: Range;\n      state: EditorState;\n    }) => void | null;\n  }) {\n    this.find = config.find\n    this.handler = config.handler\n  }\n}\n\nconst pasteRuleMatcherHandler = (\n  text: string,\n  find: PasteRuleFinder,\n  event?: ClipboardEvent | null,\n): ExtendedRegExpMatchArray[] => {\n  if (isRegExp(find)) {\n    return [...text.matchAll(find)]\n  }\n\n  const matches = find(text, event)\n\n  if (!matches) {\n    return []\n  }\n\n  return matches.map(pasteRuleMatch => {\n    const result: ExtendedRegExpMatchArray = [pasteRuleMatch.text]\n\n    result.index = pasteRuleMatch.index\n    result.input = text\n    result.data = pasteRuleMatch.data\n\n    if (pasteRuleMatch.replaceWith) {\n      if (!pasteRuleMatch.text.includes(pasteRuleMatch.replaceWith)) {\n        console.warn(\n          '[tiptap warn]: \"pasteRuleMatch.replaceWith\" must be part of \"pasteRuleMatch.text\".',\n        )\n      }\n\n      result.push(pasteRuleMatch.replaceWith)\n    }\n\n    return result\n  })\n}\n\nfunction run(config: {\n  editor: Editor;\n  state: EditorState;\n  from: number;\n  to: number;\n  rule: PasteRule;\n  pasteEvent: ClipboardEvent | null;\n  dropEvent: DragEvent | null;\n}): boolean {\n  const {\n    editor, state, from, to, rule, pasteEvent, dropEvent,\n  } = config\n\n  const { commands, chain, can } = new CommandManager({\n    editor,\n    state,\n  })\n\n  const handlers: (void | null)[] = []\n\n  state.doc.nodesBetween(from, to, (node, pos) => {\n    if (!node.isTextblock || node.type.spec.code) {\n      return\n    }\n\n    const resolvedFrom = Math.max(from, pos)\n    const resolvedTo = Math.min(to, pos + node.content.size)\n    const textToMatch = node.textBetween(resolvedFrom - pos, resolvedTo - pos, undefined, '\\ufffc')\n\n    const matches = pasteRuleMatcherHandler(textToMatch, rule.find, pasteEvent)\n\n    matches.forEach(match => {\n      if (match.index === undefined) {\n        return\n      }\n\n      const start = resolvedFrom + match.index + 1\n      const end = start + match[0].length\n      const range = {\n        from: state.tr.mapping.map(start),\n        to: state.tr.mapping.map(end),\n      }\n\n      const handler = rule.handler({\n        state,\n        range,\n        match,\n        commands,\n        chain,\n        can,\n        pasteEvent,\n        dropEvent,\n      })\n\n      handlers.push(handler)\n    })\n  })\n\n  const success = handlers.every(handler => handler !== null)\n\n  return success\n}\n\n// When dragging across editors, must get another editor instance to delete selection content.\nlet tiptapDragFromOtherEditor: Editor | null = null\n\nconst createClipboardPasteEvent = (text: string) => {\n  const event = new ClipboardEvent('paste', {\n    clipboardData: new DataTransfer(),\n  })\n\n  event.clipboardData?.setData('text/html', text)\n\n  return event\n}\n\n/**\n * Create an paste rules plugin. When enabled, it will cause pasted\n * text that matches any of the given rules to trigger the rule’s\n * action.\n */\nexport function pasteRulesPlugin(props: { editor: Editor; rules: PasteRule[] }): Plugin[] {\n  const { editor, rules } = props\n  let dragSourceElement: Element | null = null\n  let isPastedFromProseMirror = false\n  let isDroppedFromProseMirror = false\n  let pasteEvent = typeof ClipboardEvent !== 'undefined' ? new ClipboardEvent('paste') : null\n  let dropEvent: DragEvent | null\n\n  try {\n    dropEvent = typeof DragEvent !== 'undefined' ? new DragEvent('drop') : null\n  } catch {\n    dropEvent = null\n  }\n\n  const processEvent = ({\n    state,\n    from,\n    to,\n    rule,\n    pasteEvt,\n  }: {\n    state: EditorState;\n    from: number;\n    to: { b: number };\n    rule: PasteRule;\n    pasteEvt: ClipboardEvent | null;\n  }) => {\n    const tr = state.tr\n    const chainableState = createChainableState({\n      state,\n      transaction: tr,\n    })\n\n    const handler = run({\n      editor,\n      state: chainableState,\n      from: Math.max(from - 1, 0),\n      to: to.b - 1,\n      rule,\n      pasteEvent: pasteEvt,\n      dropEvent,\n    })\n\n    if (!handler || !tr.steps.length) {\n      return\n    }\n\n    try {\n      dropEvent = typeof DragEvent !== 'undefined' ? new DragEvent('drop') : null\n    } catch {\n      dropEvent = null\n    }\n    pasteEvent = typeof ClipboardEvent !== 'undefined' ? new ClipboardEvent('paste') : null\n\n    return tr\n  }\n\n  const plugins = rules.map(rule => {\n    return new Plugin({\n      // we register a global drag handler to track the current drag source element\n      view(view) {\n        const handleDragstart = (event: DragEvent) => {\n          dragSourceElement = view.dom.parentElement?.contains(event.target as Element)\n            ? view.dom.parentElement\n            : null\n\n          if (dragSourceElement) {\n            tiptapDragFromOtherEditor = editor\n          }\n        }\n\n        const handleDragend = () => {\n          if (tiptapDragFromOtherEditor) {\n            tiptapDragFromOtherEditor = null\n          }\n        }\n\n        window.addEventListener('dragstart', handleDragstart)\n        window.addEventListener('dragend', handleDragend)\n\n        return {\n          destroy() {\n            window.removeEventListener('dragstart', handleDragstart)\n            window.removeEventListener('dragend', handleDragend)\n          },\n        }\n      },\n\n      props: {\n        handleDOMEvents: {\n          drop: (view, event: Event) => {\n            isDroppedFromProseMirror = dragSourceElement === view.dom.parentElement\n            dropEvent = event as DragEvent\n\n            if (!isDroppedFromProseMirror) {\n              const dragFromOtherEditor = tiptapDragFromOtherEditor\n\n              if (dragFromOtherEditor) {\n                // setTimeout to avoid the wrong content after drop, timeout arg can't be empty or 0\n                setTimeout(() => {\n                  const selection = dragFromOtherEditor.state.selection\n\n                  if (selection) {\n                    dragFromOtherEditor.commands.deleteRange({ from: selection.from, to: selection.to })\n                  }\n                }, 10)\n              }\n            }\n            return false\n          },\n\n          paste: (_view, event: Event) => {\n            const html = (event as ClipboardEvent).clipboardData?.getData('text/html')\n\n            pasteEvent = event as ClipboardEvent\n\n            isPastedFromProseMirror = !!html?.includes('data-pm-slice')\n\n            return false\n          },\n        },\n      },\n\n      appendTransaction: (transactions, oldState, state) => {\n        const transaction = transactions[0]\n        const isPaste = transaction.getMeta('uiEvent') === 'paste' && !isPastedFromProseMirror\n        const isDrop = transaction.getMeta('uiEvent') === 'drop' && !isDroppedFromProseMirror\n\n        // if PasteRule is triggered by insertContent()\n        const simulatedPasteMeta = transaction.getMeta('applyPasteRules') as\n          | undefined\n          | { from: number; text: string | ProseMirrorNode | Fragment }\n        const isSimulatedPaste = !!simulatedPasteMeta\n\n        if (!isPaste && !isDrop && !isSimulatedPaste) {\n          return\n        }\n\n        // Handle simulated paste\n        if (isSimulatedPaste) {\n          let { text } = simulatedPasteMeta\n\n          if (typeof text === 'string') {\n            text = text as string\n          } else {\n            text = getHTMLFromFragment(Fragment.from(text), state.schema)\n          }\n\n          const { from } = simulatedPasteMeta\n          const to = from + text.length\n\n          const pasteEvt = createClipboardPasteEvent(text)\n\n          return processEvent({\n            rule,\n            state,\n            from,\n            to: { b: to },\n            pasteEvt,\n          })\n        }\n\n        // handle actual paste/drop\n        const from = oldState.doc.content.findDiffStart(state.doc.content)\n        const to = oldState.doc.content.findDiffEnd(state.doc.content)\n\n        // stop if there is no changed range\n        if (!isNumber(from) || !to || from === to.b) {\n          return\n        }\n\n        return processEvent({\n          rule,\n          state,\n          from,\n          to,\n          pasteEvt: pasteEvent,\n        })\n      },\n    })\n  })\n\n  return plugins\n}\n", "export function findDuplicates(items: any[]): any[] {\n  const filtered = items.filter((el, index) => items.indexOf(el) !== index)\n\n  return Array.from(new Set(filtered))\n}\n", "import { keymap } from '@tiptap/pm/keymap'\nimport { Schema } from '@tiptap/pm/model'\nimport { Plugin } from '@tiptap/pm/state'\nimport { NodeViewConstructor } from '@tiptap/pm/view'\n\nimport type { Editor } from './Editor.js'\nimport { getAttributesFromExtensions } from './helpers/getAttributesFromExtensions.js'\nimport { getExtensionField } from './helpers/getExtensionField.js'\nimport { getNodeType } from './helpers/getNodeType.js'\nimport { getRenderedAttributes } from './helpers/getRenderedAttributes.js'\nimport { getSchemaByResolvedExtensions } from './helpers/getSchemaByResolvedExtensions.js'\nimport { getSchemaTypeByName } from './helpers/getSchemaTypeByName.js'\nimport { isExtensionRulesEnabled } from './helpers/isExtensionRulesEnabled.js'\nimport { splitExtensions } from './helpers/splitExtensions.js'\nimport type { NodeConfig } from './index.js'\nimport { InputRule, inputRulesPlugin } from './InputRule.js'\nimport { Mark } from './Mark.js'\nimport { PasteRule, pasteRulesPlugin } from './PasteRule.js'\nimport { AnyConfig, Extensions, RawCommands } from './types.js'\nimport { callOrReturn } from './utilities/callOrReturn.js'\nimport { findDuplicates } from './utilities/findDuplicates.js'\n\nexport class ExtensionManager {\n  editor: Editor\n\n  schema: Schema\n\n  extensions: Extensions\n\n  splittableMarks: string[] = []\n\n  constructor(extensions: Extensions, editor: Editor) {\n    this.editor = editor\n    this.extensions = ExtensionManager.resolve(extensions)\n    this.schema = getSchemaByResolvedExtensions(this.extensions, editor)\n    this.setupExtensions()\n  }\n\n  /**\n   * Returns a flattened and sorted extension list while\n   * also checking for duplicated extensions and warns the user.\n   * @param extensions An array of Tiptap extensions\n   * @returns An flattened and sorted array of Tiptap extensions\n   */\n  static resolve(extensions: Extensions): Extensions {\n    const resolvedExtensions = ExtensionManager.sort(ExtensionManager.flatten(extensions))\n    const duplicatedNames = findDuplicates(resolvedExtensions.map(extension => extension.name))\n\n    if (duplicatedNames.length) {\n      console.warn(\n        `[tiptap warn]: Duplicate extension names found: [${duplicatedNames\n          .map(item => `'${item}'`)\n          .join(', ')}]. This can lead to issues.`,\n      )\n    }\n\n    return resolvedExtensions\n  }\n\n  /**\n   * Create a flattened array of extensions by traversing the `addExtensions` field.\n   * @param extensions An array of Tiptap extensions\n   * @returns A flattened array of Tiptap extensions\n   */\n  static flatten(extensions: Extensions): Extensions {\n    return (\n      extensions\n        .map(extension => {\n          const context = {\n            name: extension.name,\n            options: extension.options,\n            storage: extension.storage,\n          }\n\n          const addExtensions = getExtensionField<AnyConfig['addExtensions']>(\n            extension,\n            'addExtensions',\n            context,\n          )\n\n          if (addExtensions) {\n            return [extension, ...this.flatten(addExtensions())]\n          }\n\n          return extension\n        })\n        // `Infinity` will break TypeScript so we set a number that is probably high enough\n        .flat(10)\n    )\n  }\n\n  /**\n   * Sort extensions by priority.\n   * @param extensions An array of Tiptap extensions\n   * @returns A sorted array of Tiptap extensions by priority\n   */\n  static sort(extensions: Extensions): Extensions {\n    const defaultPriority = 100\n\n    return extensions.sort((a, b) => {\n      const priorityA = getExtensionField<AnyConfig['priority']>(a, 'priority') || defaultPriority\n      const priorityB = getExtensionField<AnyConfig['priority']>(b, 'priority') || defaultPriority\n\n      if (priorityA > priorityB) {\n        return -1\n      }\n\n      if (priorityA < priorityB) {\n        return 1\n      }\n\n      return 0\n    })\n  }\n\n  /**\n   * Get all commands from the extensions.\n   * @returns An object with all commands where the key is the command name and the value is the command function\n   */\n  get commands(): RawCommands {\n    return this.extensions.reduce((commands, extension) => {\n      const context = {\n        name: extension.name,\n        options: extension.options,\n        storage: extension.storage,\n        editor: this.editor,\n        type: getSchemaTypeByName(extension.name, this.schema),\n      }\n\n      const addCommands = getExtensionField<AnyConfig['addCommands']>(\n        extension,\n        'addCommands',\n        context,\n      )\n\n      if (!addCommands) {\n        return commands\n      }\n\n      return {\n        ...commands,\n        ...addCommands(),\n      }\n    }, {} as RawCommands)\n  }\n\n  /**\n   * Get all registered Prosemirror plugins from the extensions.\n   * @returns An array of Prosemirror plugins\n   */\n  get plugins(): Plugin[] {\n    const { editor } = this\n\n    // With ProseMirror, first plugins within an array are executed first.\n    // In Tiptap, we provide the ability to override plugins,\n    // so it feels more natural to run plugins at the end of an array first.\n    // That’s why we have to reverse the `extensions` array and sort again\n    // based on the `priority` option.\n    const extensions = ExtensionManager.sort([...this.extensions].reverse())\n\n    const inputRules: InputRule[] = []\n    const pasteRules: PasteRule[] = []\n\n    const allPlugins = extensions\n      .map(extension => {\n        const context = {\n          name: extension.name,\n          options: extension.options,\n          storage: extension.storage,\n          editor,\n          type: getSchemaTypeByName(extension.name, this.schema),\n        }\n\n        const plugins: Plugin[] = []\n\n        const addKeyboardShortcuts = getExtensionField<AnyConfig['addKeyboardShortcuts']>(\n          extension,\n          'addKeyboardShortcuts',\n          context,\n        )\n\n        let defaultBindings: Record<string, () => boolean> = {}\n\n        // bind exit handling\n        if (extension.type === 'mark' && getExtensionField<AnyConfig['exitable']>(extension, 'exitable', context)) {\n          defaultBindings.ArrowRight = () => Mark.handleExit({ editor, mark: extension as Mark })\n        }\n\n        if (addKeyboardShortcuts) {\n          const bindings = Object.fromEntries(\n            Object.entries(addKeyboardShortcuts()).map(([shortcut, method]) => {\n              return [shortcut, () => method({ editor })]\n            }),\n          )\n\n          defaultBindings = { ...defaultBindings, ...bindings }\n        }\n\n        const keyMapPlugin = keymap(defaultBindings)\n\n        plugins.push(keyMapPlugin)\n\n        const addInputRules = getExtensionField<AnyConfig['addInputRules']>(\n          extension,\n          'addInputRules',\n          context,\n        )\n\n        if (isExtensionRulesEnabled(extension, editor.options.enableInputRules) && addInputRules) {\n          inputRules.push(...addInputRules())\n        }\n\n        const addPasteRules = getExtensionField<AnyConfig['addPasteRules']>(\n          extension,\n          'addPasteRules',\n          context,\n        )\n\n        if (isExtensionRulesEnabled(extension, editor.options.enablePasteRules) && addPasteRules) {\n          pasteRules.push(...addPasteRules())\n        }\n\n        const addProseMirrorPlugins = getExtensionField<AnyConfig['addProseMirrorPlugins']>(\n          extension,\n          'addProseMirrorPlugins',\n          context,\n        )\n\n        if (addProseMirrorPlugins) {\n          const proseMirrorPlugins = addProseMirrorPlugins()\n\n          plugins.push(...proseMirrorPlugins)\n        }\n\n        return plugins\n      })\n      .flat()\n\n    return [\n      inputRulesPlugin({\n        editor,\n        rules: inputRules,\n      }),\n      ...pasteRulesPlugin({\n        editor,\n        rules: pasteRules,\n      }),\n      ...allPlugins,\n    ]\n  }\n\n  /**\n   * Get all attributes from the extensions.\n   * @returns An array of attributes\n   */\n  get attributes() {\n    return getAttributesFromExtensions(this.extensions)\n  }\n\n  /**\n   * Get all node views from the extensions.\n   * @returns An object with all node views where the key is the node name and the value is the node view function\n   */\n  get nodeViews(): Record<string, NodeViewConstructor> {\n    const { editor } = this\n    const { nodeExtensions } = splitExtensions(this.extensions)\n\n    return Object.fromEntries(\n      nodeExtensions\n        .filter(extension => !!getExtensionField(extension, 'addNodeView'))\n        .map(extension => {\n          const extensionAttributes = this.attributes.filter(\n            attribute => attribute.type === extension.name,\n          )\n          const context = {\n            name: extension.name,\n            options: extension.options,\n            storage: extension.storage,\n            editor,\n            type: getNodeType(extension.name, this.schema),\n          }\n          const addNodeView = getExtensionField<NodeConfig['addNodeView']>(\n            extension,\n            'addNodeView',\n            context,\n          )\n\n          if (!addNodeView) {\n            return []\n          }\n\n          const nodeview: NodeViewConstructor = (\n            node,\n            view,\n            getPos,\n            decorations,\n            innerDecorations,\n          ) => {\n            const HTMLAttributes = getRenderedAttributes(node, extensionAttributes)\n\n            return addNodeView()({\n              // pass-through\n              node,\n              view,\n              getPos: getPos as () => number,\n              decorations,\n              innerDecorations,\n              // tiptap-specific\n              editor,\n              extension,\n              HTMLAttributes,\n            })\n          }\n\n          return [extension.name, nodeview]\n        }),\n    )\n  }\n\n  /**\n   * Go through all extensions, create extension storages & setup marks\n   * & bind editor event listener.\n   */\n  private setupExtensions() {\n    this.extensions.forEach(extension => {\n      // store extension storage in editor\n      this.editor.extensionStorage[extension.name] = extension.storage\n\n      const context = {\n        name: extension.name,\n        options: extension.options,\n        storage: extension.storage,\n        editor: this.editor,\n        type: getSchemaTypeByName(extension.name, this.schema),\n      }\n\n      if (extension.type === 'mark') {\n        const keepOnSplit = callOrReturn(getExtensionField(extension, 'keepOnSplit', context)) ?? true\n\n        if (keepOnSplit) {\n          this.splittableMarks.push(extension.name)\n        }\n      }\n\n      const onBeforeCreate = getExtensionField<AnyConfig['onBeforeCreate']>(\n        extension,\n        'onBeforeCreate',\n        context,\n      )\n      const onCreate = getExtensionField<AnyConfig['onCreate']>(extension, 'onCreate', context)\n      const onUpdate = getExtensionField<AnyConfig['onUpdate']>(extension, 'onUpdate', context)\n      const onSelectionUpdate = getExtensionField<AnyConfig['onSelectionUpdate']>(\n        extension,\n        'onSelectionUpdate',\n        context,\n      )\n      const onTransaction = getExtensionField<AnyConfig['onTransaction']>(\n        extension,\n        'onTransaction',\n        context,\n      )\n      const onFocus = getExtensionField<AnyConfig['onFocus']>(extension, 'onFocus', context)\n      const onBlur = getExtensionField<AnyConfig['onBlur']>(extension, 'onBlur', context)\n      const onDestroy = getExtensionField<AnyConfig['onDestroy']>(extension, 'onDestroy', context)\n\n      if (onBeforeCreate) {\n        this.editor.on('beforeCreate', onBeforeCreate)\n      }\n\n      if (onCreate) {\n        this.editor.on('create', onCreate)\n      }\n\n      if (onUpdate) {\n        this.editor.on('update', onUpdate)\n      }\n\n      if (onSelectionUpdate) {\n        this.editor.on('selectionUpdate', onSelectionUpdate)\n      }\n\n      if (onTransaction) {\n        this.editor.on('transaction', onTransaction)\n      }\n\n      if (onFocus) {\n        this.editor.on('focus', onFocus)\n      }\n\n      if (onBlur) {\n        this.editor.on('blur', onBlur)\n      }\n\n      if (onDestroy) {\n        this.editor.on('destroy', onDestroy)\n      }\n    })\n  }\n}\n", "import { Plugin, Transaction } from '@tiptap/pm/state'\n\nimport { Editor } from './Editor.js'\nimport { getExtensionField } from './helpers/getExtensionField.js'\nimport { ExtensionConfig } from './index.js'\nimport { InputRule } from './InputRule.js'\nimport { Mark } from './Mark.js'\nimport { Node } from './Node.js'\nimport { PasteRule } from './PasteRule.js'\nimport {\n  AnyConfig,\n  Extensions,\n  GlobalAttributes,\n  KeyboardShortcutCommand,\n  ParentConfig,\n  RawCommands,\n} from './types.js'\nimport { callOrReturn } from './utilities/callOrReturn.js'\nimport { mergeDeep } from './utilities/mergeDeep.js'\n\ndeclare module '@tiptap/core' {\n  interface ExtensionConfig<Options = any, Storage = any> {\n    // @ts-ignore - this is a dynamic key\n    [key: string]: any\n\n    /**\n     * The extension name - this must be unique.\n     * It will be used to identify the extension.\n     *\n     * @example 'myExtension'\n     */\n    name: string\n\n    /**\n     * The priority of your extension. The higher, the earlier it will be called\n     * and will take precedence over other extensions with a lower priority.\n     * @default 100\n     * @example 101\n     */\n    priority?: number\n\n    /**\n     * The default options for this extension.\n     * @example\n     * defaultOptions: {\n     *   myOption: 'foo',\n     *   myOtherOption: 10,\n     * }\n     */\n    defaultOptions?: Options\n\n    /**\n     * This method will add options to this extension\n     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#settings\n     * @example\n     * addOptions() {\n     *  return {\n     *    myOption: 'foo',\n     *    myOtherOption: 10,\n     * }\n     */\n    addOptions?: (this: {\n      name: string\n      parent: Exclude<ParentConfig<ExtensionConfig<Options, Storage>>['addOptions'], undefined>\n    }) => Options\n\n    /**\n     * The default storage this extension can save data to.\n     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#storage\n     * @example\n     * defaultStorage: {\n     *   prefetchedUsers: [],\n     *   loading: false,\n     * }\n     */\n    addStorage?: (this: {\n      name: string\n      options: Options\n      parent: Exclude<ParentConfig<ExtensionConfig<Options, Storage>>['addStorage'], undefined>\n    }) => Storage\n\n    /**\n     * This function adds globalAttributes to specific nodes.\n     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#global-attributes\n     * @example\n     * addGlobalAttributes() {\n     *   return [\n     *     {\n             // Extend the following extensions\n     *       types: [\n     *         'heading',\n     *         'paragraph',\n     *       ],\n     *       // … with those attributes\n     *       attributes: {\n     *         textAlign: {\n     *           default: 'left',\n     *           renderHTML: attributes => ({\n     *             style: `text-align: ${attributes.textAlign}`,\n     *           }),\n     *           parseHTML: element => element.style.textAlign || 'left',\n     *         },\n     *       },\n     *     },\n     *   ]\n     * }\n     */\n    addGlobalAttributes?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      extensions: (Node | Mark)[]\n      parent: ParentConfig<ExtensionConfig<Options, Storage>>['addGlobalAttributes']\n    }) => GlobalAttributes\n\n    /**\n     * This function adds commands to the editor\n     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#commands\n     * @example\n     * addCommands() {\n     *   return {\n     *     myCommand: () => ({ chain }) => chain().setMark('type', 'foo').run(),\n     *   }\n     * }\n     */\n    addCommands?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      parent: ParentConfig<ExtensionConfig<Options, Storage>>['addCommands']\n    }) => Partial<RawCommands>\n\n    /**\n     * This function registers keyboard shortcuts.\n     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#keyboard-shortcuts\n     * @example\n     * addKeyboardShortcuts() {\n     *   return {\n     *     'Mod-l': () => this.editor.commands.toggleBulletList(),\n     *   }\n     * },\n     */\n    addKeyboardShortcuts?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      parent: ParentConfig<ExtensionConfig<Options, Storage>>['addKeyboardShortcuts']\n    }) => {\n      [key: string]: KeyboardShortcutCommand\n    }\n\n    /**\n     * This function adds input rules to the editor.\n     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#input-rules\n     * @example\n     * addInputRules() {\n     *   return [\n     *     markInputRule({\n     *       find: inputRegex,\n     *       type: this.type,\n     *     }),\n     *   ]\n     * },\n     */\n    addInputRules?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      parent: ParentConfig<ExtensionConfig<Options, Storage>>['addInputRules']\n    }) => InputRule[]\n\n    /**\n     * This function adds paste rules to the editor.\n     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#paste-rules\n     * @example\n     * addPasteRules() {\n     *   return [\n     *     markPasteRule({\n     *       find: pasteRegex,\n     *       type: this.type,\n     *     }),\n     *   ]\n     * },\n     */\n    addPasteRules?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      parent: ParentConfig<ExtensionConfig<Options, Storage>>['addPasteRules']\n    }) => PasteRule[]\n\n    /**\n     * This function adds Prosemirror plugins to the editor\n     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#prosemirror-plugins\n     * @example\n     * addProseMirrorPlugins() {\n     *   return [\n     *     customPlugin(),\n     *   ]\n     * }\n     */\n    addProseMirrorPlugins?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      parent: ParentConfig<ExtensionConfig<Options, Storage>>['addProseMirrorPlugins']\n    }) => Plugin[]\n\n    /**\n     * This function adds additional extensions to the editor. This is useful for\n     * building extension kits.\n     * @example\n     * addExtensions() {\n     *   return [\n     *     BulletList,\n     *     OrderedList,\n     *     ListItem\n     *   ]\n     * }\n     */\n    addExtensions?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      parent: ParentConfig<ExtensionConfig<Options, Storage>>['addExtensions']\n    }) => Extensions\n\n    /**\n     * This function extends the schema of the node.\n     * @example\n     * extendNodeSchema() {\n     *   return {\n     *     group: 'inline',\n     *     selectable: false,\n     *   }\n     * }\n     */\n    extendNodeSchema?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<ExtensionConfig<Options, Storage>>['extendNodeSchema']\n          },\n          extension: Node,\n        ) => Record<string, any>)\n      | null\n\n    /**\n     * This function extends the schema of the mark.\n     * @example\n     * extendMarkSchema() {\n     *   return {\n     *     group: 'inline',\n     *     selectable: false,\n     *   }\n     * }\n     */\n    extendMarkSchema?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<ExtensionConfig<Options, Storage>>['extendMarkSchema']\n          },\n          extension: Mark,\n        ) => Record<string, any>)\n      | null\n\n    /**\n     * The editor is not ready yet.\n     */\n    onBeforeCreate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          parent: ParentConfig<ExtensionConfig<Options, Storage>>['onBeforeCreate']\n        }) => void)\n      | null\n\n    /**\n     * The editor is ready.\n     */\n    onCreate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          parent: ParentConfig<ExtensionConfig<Options, Storage>>['onCreate']\n        }) => void)\n      | null\n\n    /**\n     * The content has changed.\n     */\n    onUpdate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          parent: ParentConfig<ExtensionConfig<Options, Storage>>['onUpdate']\n        }) => void)\n      | null\n\n    /**\n     * The selection has changed.\n     */\n    onSelectionUpdate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          parent: ParentConfig<ExtensionConfig<Options, Storage>>['onSelectionUpdate']\n        }) => void)\n      | null\n\n    /**\n     * The editor state has changed.\n     */\n    onTransaction?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            parent: ParentConfig<ExtensionConfig<Options, Storage>>['onTransaction']\n          },\n          props: {\n            editor: Editor\n            transaction: Transaction\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor is focused.\n     */\n    onFocus?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            parent: ParentConfig<ExtensionConfig<Options, Storage>>['onFocus']\n          },\n          props: {\n            event: FocusEvent\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor isn’t focused anymore.\n     */\n    onBlur?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            parent: ParentConfig<ExtensionConfig<Options, Storage>>['onBlur']\n          },\n          props: {\n            event: FocusEvent\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor is destroyed.\n     */\n    onDestroy?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          parent: ParentConfig<ExtensionConfig<Options, Storage>>['onDestroy']\n        }) => void)\n      | null\n  }\n}\n\n/**\n * The Extension class is the base class for all extensions.\n * @see https://tiptap.dev/api/extensions#create-a-new-extension\n */\nexport class Extension<Options = any, Storage = any> {\n  type = 'extension'\n\n  name = 'extension'\n\n  parent: Extension | null = null\n\n  child: Extension | null = null\n\n  options: Options\n\n  storage: Storage\n\n  config: ExtensionConfig = {\n    name: this.name,\n    defaultOptions: {},\n  }\n\n  constructor(config: Partial<ExtensionConfig<Options, Storage>> = {}) {\n    this.config = {\n      ...this.config,\n      ...config,\n    }\n\n    this.name = this.config.name\n\n    if (config.defaultOptions && Object.keys(config.defaultOptions).length > 0) {\n      console.warn(\n        `[tiptap warn]: BREAKING CHANGE: \"defaultOptions\" is deprecated. Please use \"addOptions\" instead. Found in extension: \"${this.name}\".`,\n      )\n    }\n\n    // TODO: remove `addOptions` fallback\n    this.options = this.config.defaultOptions\n\n    if (this.config.addOptions) {\n      this.options = callOrReturn(\n        getExtensionField<AnyConfig['addOptions']>(this, 'addOptions', {\n          name: this.name,\n        }),\n      )\n    }\n\n    this.storage = callOrReturn(\n      getExtensionField<AnyConfig['addStorage']>(this, 'addStorage', {\n        name: this.name,\n        options: this.options,\n      }),\n    ) || {}\n  }\n\n  static create<O = any, S = any>(config: Partial<ExtensionConfig<O, S>> = {}) {\n    return new Extension<O, S>(config)\n  }\n\n  configure(options: Partial<Options> = {}) {\n    // return a new instance so we can use the same extension\n    // with different calls of `configure`\n    const extension = this.extend<Options, Storage>({\n      ...this.config,\n      addOptions: () => {\n        return mergeDeep(this.options as Record<string, any>, options) as Options\n      },\n    })\n\n    // Always preserve the current name\n    extension.name = this.name\n    // Set the parent to be our parent\n    extension.parent = this.parent\n\n    return extension\n  }\n\n  extend<ExtendedOptions = Options, ExtendedStorage = Storage>(\n    extendedConfig: Partial<ExtensionConfig<ExtendedOptions, ExtendedStorage>> = {},\n  ) {\n    const extension = new Extension<ExtendedOptions, ExtendedStorage>({ ...this.config, ...extendedConfig })\n\n    extension.parent = this\n\n    this.child = extension\n\n    extension.name = extendedConfig.name ? extendedConfig.name : extension.parent.name\n\n    if (extendedConfig.defaultOptions && Object.keys(extendedConfig.defaultOptions).length > 0) {\n      console.warn(\n        `[tiptap warn]: BREAKING CHANGE: \"defaultOptions\" is deprecated. Please use \"addOptions\" instead. Found in extension: \"${extension.name}\".`,\n      )\n    }\n\n    extension.options = callOrReturn(\n      getExtensionField<AnyConfig['addOptions']>(extension, 'addOptions', {\n        name: extension.name,\n      }),\n    )\n\n    extension.storage = callOrReturn(\n      getExtensionField<AnyConfig['addStorage']>(extension, 'addStorage', {\n        name: extension.name,\n        options: extension.options,\n      }),\n    )\n\n    return extension\n  }\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport { Range, TextSerializer } from '../types.js'\n\n/**\n * Gets the text between two positions in a Prosemirror node\n * and serializes it using the given text serializers and block separator (see getText)\n * @param startNode The Prosemirror node to start from\n * @param range The range of the text to get\n * @param options Options for the text serializer & block separator\n * @returns The text between the two positions\n */\nexport function getTextBetween(\n  startNode: ProseMirrorNode,\n  range: Range,\n  options?: {\n    blockSeparator?: string\n    textSerializers?: Record<string, TextSerializer>\n  },\n): string {\n  const { from, to } = range\n  const { blockSeparator = '\\n\\n', textSerializers = {} } = options || {}\n  let text = ''\n\n  startNode.nodesBetween(from, to, (node, pos, parent, index) => {\n    if (node.isBlock && pos > from) {\n      text += blockSeparator\n    }\n\n    const textSerializer = textSerializers?.[node.type.name]\n\n    if (textSerializer) {\n      if (parent) {\n        text += textSerializer({\n          node,\n          pos,\n          parent,\n          index,\n          range,\n        })\n      }\n      // do not descend into child nodes when there exists a serializer\n      return false\n    }\n\n    if (node.isText) {\n      text += node?.text?.slice(Math.max(from, pos) - pos, to - pos) // eslint-disable-line\n    }\n  })\n\n  return text\n}\n", "import { Schema } from '@tiptap/pm/model'\n\nimport { TextSerializer } from '../types.js'\n\n/**\n * Find text serializers `toText` in a Prosemirror schema\n * @param schema The Prosemirror schema to search in\n * @returns A record of text serializers by node name\n */\nexport function getTextSerializersFromSchema(schema: Schema): Record<string, TextSerializer> {\n  return Object.fromEntries(\n    Object.entries(schema.nodes)\n      .filter(([, node]) => node.spec.toText)\n      .map(([name, node]) => [name, node.spec.toText]),\n  )\n}\n", "import { Plugin, Plugin<PERSON><PERSON> } from '@tiptap/pm/state'\n\nimport { Extension } from '../Extension.js'\nimport { getTextBetween } from '../helpers/getTextBetween.js'\nimport { getTextSerializersFromSchema } from '../helpers/getTextSerializersFromSchema.js'\n\nexport type ClipboardTextSerializerOptions = {\n  blockSeparator?: string,\n}\n\nexport const ClipboardTextSerializer = Extension.create<ClipboardTextSerializerOptions>({\n  name: 'clipboardTextSerializer',\n\n  addOptions() {\n    return {\n      blockSeparator: undefined,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      new Plugin({\n        key: new PluginKey('clipboardTextSerializer'),\n        props: {\n          clipboardTextSerializer: () => {\n            const { editor } = this\n            const { state, schema } = editor\n            const { doc, selection } = state\n            const { ranges } = selection\n            const from = Math.min(...ranges.map(range => range.$from.pos))\n            const to = Math.max(...ranges.map(range => range.$to.pos))\n            const textSerializers = getTextSerializersFromSchema(schema)\n            const range = { from, to }\n\n            return getTextBetween(doc, range, {\n              ...(this.options.blockSeparator !== undefined\n                ? { blockSeparator: this.options.blockSeparator }\n                : {}),\n              textSerializers,\n            })\n          },\n        },\n      }),\n    ]\n  },\n})\n", "import { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    blur: {\n      /**\n       * Removes focus from the editor.\n       * @example editor.commands.blur()\n       */\n      blur: () => ReturnType,\n    }\n  }\n}\n\nexport const blur: RawCommands['blur'] = () => ({ editor, view }) => {\n  requestAnimationFrame(() => {\n    if (!editor.isDestroyed) {\n      (view.dom as HTMLElement).blur()\n\n      // Browsers should remove the caret on blur but safari does not.\n      // See: https://github.com/ueberdosis/tiptap/issues/2405\n      window?.getSelection()?.removeAllRanges()\n    }\n  })\n\n  return true\n}\n", "import { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    clearContent: {\n      /**\n       * Clear the whole document.\n       * @param emitUpdate Whether to emit an update event.\n       * @example editor.commands.clearContent()\n       */\n      clearContent: (emitUpdate?: boolean) => ReturnType,\n    }\n  }\n}\n\nexport const clearContent: RawCommands['clearContent'] = (emitUpdate = false) => ({ commands }) => {\n  return commands.setContent('', emitUpdate)\n}\n", "import { liftTarget } from '@tiptap/pm/transform'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    clearNodes: {\n      /**\n       * Normalize nodes to a simple paragraph.\n       * @example editor.commands.clearNodes()\n       */\n      clearNodes: () => ReturnType,\n    }\n  }\n}\n\nexport const clearNodes: RawCommands['clearNodes'] = () => ({ state, tr, dispatch }) => {\n  const { selection } = tr\n  const { ranges } = selection\n\n  if (!dispatch) {\n    return true\n  }\n\n  ranges.forEach(({ $from, $to }) => {\n    state.doc.nodesBetween($from.pos, $to.pos, (node, pos) => {\n      if (node.type.isText) {\n        return\n      }\n\n      const { doc, mapping } = tr\n      const $mappedFrom = doc.resolve(mapping.map(pos))\n      const $mappedTo = doc.resolve(mapping.map(pos + node.nodeSize))\n      const nodeRange = $mappedFrom.blockRange($mappedTo)\n\n      if (!nodeRange) {\n        return\n      }\n\n      const targetLiftDepth = liftTarget(nodeRange)\n\n      if (node.type.isTextblock) {\n        const { defaultType } = $mappedFrom.parent.contentMatchAt($mappedFrom.index())\n\n        tr.setNodeMarkup(nodeRange.start, defaultType)\n      }\n\n      if (targetLiftDepth || targetLiftDepth === 0) {\n        tr.lift(nodeRange, targetLiftDepth)\n      }\n    })\n  })\n\n  return true\n}\n", "import { Command, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    command: {\n      /**\n       * Define a command inline.\n       * @param fn The command function.\n       * @example\n       * editor.commands.command(({ tr, state }) => {\n       *   ...\n       *   return true\n       * })\n       */\n      command: (fn: (props: Parameters<Command>[0]) => boolean) => ReturnType,\n    }\n  }\n}\n\nexport const command: RawCommands['command'] = fn => props => {\n  return fn(props)\n}\n", "import { createParagraphNear as originalCreateParagraph<PERSON>ear } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    createParagraphNear: {\n      /**\n       * Create a paragraph nearby.\n       * @example editor.commands.createParagraphNear()\n       */\n      createParagraphNear: () => ReturnType\n    }\n  }\n}\n\nexport const createParagraphNear: RawCommands['createParagraphNear'] = () => ({ state, dispatch }) => {\n  return originalCreateParagraphNear(state, dispatch)\n}\n", "import { TextSelection } from '@tiptap/pm/state'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    cut: {\n      /**\n       * Cuts content from a range and inserts it at a given position.\n       * @param range The range to cut.\n       * @param range.from The start position of the range.\n       * @param range.to The end position of the range.\n       * @param targetPos The position to insert the content at.\n       * @example editor.commands.cut({ from: 1, to: 3 }, 5)\n       */\n      cut: ({ from, to }: { from: number, to: number }, targetPos: number) => ReturnType,\n    }\n  }\n}\n\nexport const cut: RawCommands['cut'] = (originRange, targetPos) => ({ editor, tr }) => {\n  const { state } = editor\n\n  const contentSlice = state.doc.slice(originRange.from, originRange.to)\n\n  tr.deleteRange(originRange.from, originRange.to)\n  const newPos = tr.mapping.map(targetPos)\n\n  tr.insert(newPos, contentSlice.content)\n\n  tr.setSelection(new TextSelection(tr.doc.resolve(newPos - 1)))\n\n  return true\n}\n", "import { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    deleteCurrentNode: {\n      /**\n       * Delete the node that currently has the selection anchor.\n       * @example editor.commands.deleteCurrentNode()\n       */\n      deleteCurrentNode: () => ReturnType,\n    }\n  }\n}\n\nexport const deleteCurrentNode: RawCommands['deleteCurrentNode'] = () => ({ tr, dispatch }) => {\n  const { selection } = tr\n  const currentNode = selection.$anchor.node()\n\n  // if there is content inside the current node, break out of this command\n  if (currentNode.content.size > 0) {\n    return false\n  }\n\n  const $pos = tr.selection.$anchor\n\n  for (let depth = $pos.depth; depth > 0; depth -= 1) {\n    const node = $pos.node(depth)\n\n    if (node.type === currentNode.type) {\n      if (dispatch) {\n        const from = $pos.before(depth)\n        const to = $pos.after(depth)\n\n        tr.delete(from, to).scrollIntoView()\n      }\n\n      return true\n    }\n  }\n\n  return false\n}\n", "import { NodeType } from '@tiptap/pm/model'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    deleteNode: {\n      /**\n       * Delete a node with a given type or name.\n       * @param typeOrName The type or name of the node.\n       * @example editor.commands.deleteNode('paragraph')\n       */\n      deleteNode: (typeOrName: string | NodeType) => ReturnType,\n    }\n  }\n}\n\nexport const deleteNode: RawCommands['deleteNode'] = typeOrName => ({ tr, state, dispatch }) => {\n  const type = getNodeType(typeOrName, state.schema)\n  const $pos = tr.selection.$anchor\n\n  for (let depth = $pos.depth; depth > 0; depth -= 1) {\n    const node = $pos.node(depth)\n\n    if (node.type === type) {\n      if (dispatch) {\n        const from = $pos.before(depth)\n        const to = $pos.after(depth)\n\n        tr.delete(from, to).scrollIntoView()\n      }\n\n      return true\n    }\n  }\n\n  return false\n}\n", "import { Range, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    deleteRange: {\n      /**\n       * Delete a given range.\n       * @param range The range to delete.\n       * @example editor.commands.deleteRange({ from: 1, to: 3 })\n       */\n      deleteRange: (range: Range) => ReturnType,\n    }\n  }\n}\n\nexport const deleteRange: RawCommands['deleteRange'] = range => ({ tr, dispatch }) => {\n  const { from, to } = range\n\n  if (dispatch) {\n    tr.delete(from, to)\n  }\n\n  return true\n}\n", "import { deleteSelection as originalDeleteSelection } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    deleteSelection: {\n      /**\n       * Delete the selection, if there is one.\n       * @example editor.commands.deleteSelection()\n       */\n      deleteSelection: () => ReturnType\n    }\n  }\n}\n\nexport const deleteSelection: RawCommands['deleteSelection'] = () => ({ state, dispatch }) => {\n  return originalDeleteSelection(state, dispatch)\n}\n", "import { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    enter: {\n      /**\n       * Trigger enter.\n       * @example editor.commands.enter()\n       */\n      enter: () => ReturnType,\n    }\n  }\n}\n\nexport const enter: RawCommands['enter'] = () => ({ commands }) => {\n  return commands.keyboardShortcut('Enter')\n}\n", "import { exitCode as originalExitCode } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    exitCode: {\n      /**\n       * Exit from a code block.\n       * @example editor.commands.exitCode()\n       */\n      exitCode: () => ReturnType\n    }\n  }\n}\n\nexport const exitCode: RawCommands['exitCode'] = () => ({ state, dispatch }) => {\n  return originalExitCode(state, dispatch)\n}\n", "import { isRegExp } from './isRegExp.js'\n\n/**\n * Check if object1 includes object2\n * @param object1 Object\n * @param object2 Object\n */\nexport function objectIncludes(\n  object1: Record<string, any>,\n  object2: Record<string, any>,\n  options: { strict: boolean } = { strict: true },\n): boolean {\n  const keys = Object.keys(object2)\n\n  if (!keys.length) {\n    return true\n  }\n\n  return keys.every(key => {\n    if (options.strict) {\n      return object2[key] === object1[key]\n    }\n\n    if (isRegExp(object2[key])) {\n      return object2[key].test(object1[key])\n    }\n\n    return object2[key] === object1[key]\n  })\n}\n", "import { Mark as ProseMirrorMark, MarkType, ResolvedPos } from '@tiptap/pm/model'\n\nimport { Range } from '../types.js'\nimport { objectIncludes } from '../utilities/objectIncludes.js'\n\nfunction findMarkInSet(\n  marks: ProseMirrorMark[],\n  type: MarkType,\n  attributes: Record<string, any> = {},\n): ProseMirrorMark | undefined {\n  return marks.find(item => {\n    return (\n      item.type === type\n      && objectIncludes(\n        // Only check equality for the attributes that are provided\n        Object.fromEntries(Object.keys(attributes).map(k => [k, item.attrs[k]])),\n        attributes,\n      )\n    )\n  })\n}\n\nfunction isMarkInSet(\n  marks: ProseMirrorMark[],\n  type: MarkType,\n  attributes: Record<string, any> = {},\n): boolean {\n  return !!findMarkInSet(marks, type, attributes)\n}\n\n/**\n * Get the range of a mark at a resolved position.\n */\nexport function getMarkRange(\n  /**\n   * The position to get the mark range for.\n   */\n  $pos: ResolvedPos,\n  /**\n   * The mark type to get the range for.\n   */\n  type: MarkType,\n  /**\n   * The attributes to match against.\n   * If not provided, only the first mark at the position will be matched.\n   */\n  attributes?: Record<string, any>,\n): Range | void {\n  if (!$pos || !type) {\n    return\n  }\n  let start = $pos.parent.childAfter($pos.parentOffset)\n\n  // If the cursor is at the start of a text node that does not have the mark, look backward\n  if (!start.node || !start.node.marks.some(mark => mark.type === type)) {\n    start = $pos.parent.childBefore($pos.parentOffset)\n  }\n\n  // If there is no text node with the mark even backward, return undefined\n  if (!start.node || !start.node.marks.some(mark => mark.type === type)) {\n    return\n  }\n\n  // Default to only matching against the first mark's attributes\n  attributes = attributes || start.node.marks[0]?.attrs\n\n  // We now know that the cursor is either at the start, middle or end of a text node with the specified mark\n  // so we can look it up on the targeted mark\n  const mark = findMarkInSet([...start.node.marks], type, attributes)\n\n  if (!mark) {\n    return\n  }\n\n  let startIndex = start.index\n  let startPos = $pos.start() + start.offset\n  let endIndex = startIndex + 1\n  let endPos = startPos + start.node.nodeSize\n\n  while (\n    startIndex > 0\n    && isMarkInSet([...$pos.parent.child(startIndex - 1).marks], type, attributes)\n  ) {\n    startIndex -= 1\n    startPos -= $pos.parent.child(startIndex).nodeSize\n  }\n\n  while (\n    endIndex < $pos.parent.childCount\n    && isMarkInSet([...$pos.parent.child(endIndex).marks], type, attributes)\n  ) {\n    endPos += $pos.parent.child(endIndex).nodeSize\n    endIndex += 1\n  }\n\n  return {\n    from: startPos,\n    to: endPos,\n  }\n}\n", "import { MarkType, Schema } from '@tiptap/pm/model'\n\nexport function getMarkType(nameOrType: string | MarkType, schema: Schema): MarkType {\n  if (typeof nameOrType === 'string') {\n    if (!schema.marks[nameOrType]) {\n      throw Error(\n        `There is no mark type named '${nameOrType}'. Maybe you forgot to add the extension?`,\n      )\n    }\n\n    return schema.marks[nameOrType]\n  }\n\n  return nameOrType\n}\n", "import { MarkType } from '@tiptap/pm/model'\nimport { TextSelection } from '@tiptap/pm/state'\n\nimport { getMarkRange } from '../helpers/getMarkRange.js'\nimport { getMarkType } from '../helpers/getMarkType.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    extendMarkRange: {\n      /**\n       * Extends the text selection to the current mark by type or name.\n       * @param typeOrName The type or name of the mark.\n       * @param attributes The attributes of the mark.\n       * @example editor.commands.extendMarkRange('bold')\n       * @example editor.commands.extendMarkRange('mention', { userId: \"1\" })\n       */\n      extendMarkRange: (\n        /**\n         * The type or name of the mark.\n         */\n        typeOrName: string | MarkType,\n\n        /**\n         * The attributes of the mark.\n         */\n        attributes?: Record<string, any>,\n      ) => ReturnType\n    }\n  }\n}\n\nexport const extendMarkRange: RawCommands['extendMarkRange'] = (typeOrName, attributes = {}) => ({ tr, state, dispatch }) => {\n  const type = getMarkType(typeOrName, state.schema)\n  const { doc, selection } = tr\n  const { $from, from, to } = selection\n\n  if (dispatch) {\n    const range = getMarkRange($from, type, attributes)\n\n    if (range && range.from <= from && range.to >= to) {\n      const newSelection = TextSelection.create(doc, range.from, range.to)\n\n      tr.setSelection(newSelection)\n    }\n  }\n\n  return true\n}\n", "import { Command, CommandProps, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    first: {\n      /**\n       * Runs one command after the other and stops at the first which returns true.\n       * @param commands The commands to run.\n       * @example editor.commands.first([command1, command2])\n       */\n      first: (commands: Command[] | ((props: CommandProps) => Command[])) => ReturnType,\n    }\n  }\n}\n\nexport const first: RawCommands['first'] = commands => props => {\n  const items = typeof commands === 'function'\n    ? commands(props)\n    : commands\n\n  for (let i = 0; i < items.length; i += 1) {\n    if (items[i](props)) {\n      return true\n    }\n  }\n\n  return false\n}\n", "import { TextSelection } from '@tiptap/pm/state'\n\nexport function isTextSelection(value: unknown): value is TextSelection {\n  return value instanceof TextSelection\n}\n", "export function minMax(value = 0, min = 0, max = 0): number {\n  return Math.min(Math.max(value, min), max)\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { Selection, TextSelection } from '@tiptap/pm/state'\n\nimport { FocusPosition } from '../types.js'\nimport { minMax } from '../utilities/minMax.js'\n\nexport function resolveFocusPosition(\n  doc: ProseMirrorNode,\n  position: FocusPosition = null,\n): Selection | null {\n  if (!position) {\n    return null\n  }\n\n  const selectionAtStart = Selection.atStart(doc)\n  const selectionAtEnd = Selection.atEnd(doc)\n\n  if (position === 'start' || position === true) {\n    return selectionAtStart\n  }\n\n  if (position === 'end') {\n    return selectionAtEnd\n  }\n\n  const minPos = selectionAtStart.from\n  const maxPos = selectionAtEnd.to\n\n  if (position === 'all') {\n    return TextSelection.create(\n      doc,\n      minMax(0, minPos, maxPos),\n      minMax(doc.content.size, minPos, maxPos),\n    )\n  }\n\n  return TextSelection.create(\n    doc,\n    minMax(position, minPos, maxPos),\n    minMax(position, minPos, maxPos),\n  )\n}\n", "export function isAndroid(): boolean {\n  return navigator.platform === 'Android' || /android/i.test(navigator.userAgent)\n}\n", "export function isiOS(): boolean {\n  return [\n    'iPad Simulator',\n    'iPhone Simulator',\n    'iPod Simulator',\n    'iPad',\n    'iPhone',\n    'iPod',\n  ].includes(navigator.platform)\n  // iPad on iOS 13 detection\n  || (navigator.userAgent.includes('Mac') && 'ontouchend' in document)\n}\n", "import { isTextSelection } from '../helpers/isTextSelection.js'\nimport { resolveFocusPosition } from '../helpers/resolveFocusPosition.js'\nimport { FocusPosition, RawCommands } from '../types.js'\nimport { isAndroid } from '../utilities/isAndroid.js'\nimport { isiOS } from '../utilities/isiOS.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    focus: {\n      /**\n       * Focus the editor at the given position.\n       * @param position The position to focus at.\n       * @param options.scrollIntoView Scroll the focused position into view after focusing\n       * @example editor.commands.focus()\n       * @example editor.commands.focus(32, { scrollIntoView: false })\n       */\n      focus: (\n        /**\n         * The position to focus at.\n         */\n        position?: FocusPosition,\n\n        /**\n         * Optional options\n         * @default { scrollIntoView: true }\n         */\n        options?: {\n          scrollIntoView?: boolean,\n        },\n      ) => ReturnType,\n    }\n  }\n}\n\nexport const focus: RawCommands['focus'] = (position = null, options = {}) => ({\n  editor,\n  view,\n  tr,\n  dispatch,\n}) => {\n  options = {\n    scrollIntoView: true,\n    ...options,\n  }\n\n  const delayedFocus = () => {\n    // focus within `requestAnimationFrame` breaks focus on iOS and Android\n    // so we have to call this\n    if (isiOS() || isAndroid()) {\n      (view.dom as HTMLElement).focus()\n    }\n\n    // For React we have to focus asynchronously. Otherwise wild things happen.\n    // see: https://github.com/ueberdosis/tiptap/issues/1520\n    requestAnimationFrame(() => {\n      if (!editor.isDestroyed) {\n        view.focus()\n\n        if (options?.scrollIntoView) {\n          editor.commands.scrollIntoView()\n        }\n      }\n    })\n  }\n\n  if ((view.hasFocus() && position === null) || position === false) {\n    return true\n  }\n\n  // we don’t try to resolve a NodeSelection or CellSelection\n  if (dispatch && position === null && !isTextSelection(editor.state.selection)) {\n    delayedFocus()\n    return true\n  }\n\n  // pass through tr.doc instead of editor.state.doc\n  // since transactions could change the editors state before this command has been run\n  const selection = resolveFocusPosition(tr.doc, position) || editor.state.selection\n  const isSameSelection = editor.state.selection.eq(selection)\n\n  if (dispatch) {\n    if (!isSameSelection) {\n      tr.setSelection(selection)\n    }\n\n    // `tr.setSelection` resets the stored marks\n    // so we’ll restore them if the selection is the same as before\n    if (isSameSelection && tr.storedMarks) {\n      tr.setStoredMarks(tr.storedMarks)\n    }\n\n    delayedFocus()\n  }\n\n  return true\n}\n", "import { CommandProps, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    forEach: {\n      /**\n       * Loop through an array of items.\n       */\n      forEach: <T>(\n        items: T[],\n        fn: (\n          item: T,\n          props: CommandProps & {\n            index: number,\n          },\n        ) => boolean,\n      ) => ReturnType,\n    }\n  }\n}\n\nexport const forEach: RawCommands['forEach'] = (items, fn) => props => {\n  return items.every((item, index) => fn(item, { ...props, index }))\n}\n", "import { Fragment, Node as ProseMirrorNode, ParseOptions } from '@tiptap/pm/model'\n\nimport { Content, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    insertContent: {\n      /**\n       * Insert a node or string of HTML at the current position.\n       * @example editor.commands.insertContent('<h1>Example</h1>')\n       * @example editor.commands.insertContent('<h1>Example</h1>', { updateSelection: false })\n       */\n      insertContent: (\n        /**\n         * The ProseMirror content to insert.\n         */\n        value: Content | ProseMirrorNode | Fragment,\n\n        /**\n         * Optional options\n         */\n        options?: {\n          /**\n           * Options for parsing the content.\n           */\n          parseOptions?: ParseOptions;\n\n          /**\n           * Whether to update the selection after inserting the content.\n           */\n          updateSelection?: boolean;\n          applyInputRules?: boolean;\n          applyPasteRules?: boolean;\n        }\n      ) => ReturnType;\n    };\n  }\n}\n\nexport const insertContent: RawCommands['insertContent'] = (value, options) => ({ tr, commands }) => {\n  return commands.insertContentAt(\n    { from: tr.selection.from, to: tr.selection.to },\n    value,\n    options,\n  )\n}\n", "const removeWhitespaces = (node: HTMLElement) => {\n  const children = node.childNodes\n\n  for (let i = children.length - 1; i >= 0; i -= 1) {\n    const child = children[i]\n\n    if (child.nodeType === 3 && child.nodeValue && /^(\\n\\s\\s|\\n)$/.test(child.nodeValue)) {\n      node.removeChild(child)\n    } else if (child.nodeType === 1) {\n      removeWhitespaces(child as HTMLElement)\n    }\n  }\n\n  return node\n}\n\nexport function elementFromString(value: string): HTMLElement {\n  // add a wrapper to preserve leading and trailing whitespace\n  const wrappedValue = `<body>${value}</body>`\n\n  const html = new window.DOMParser().parseFromString(wrappedValue, 'text/html').body\n\n  return removeWhitespaces(html)\n}\n", "import {\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  Frag<PERSON>,\n  Node as ProseMirrorNode,\n  ParseOptions,\n  Schema,\n} from '@tiptap/pm/model'\n\nimport { Content } from '../types.js'\nimport { elementFromString } from '../utilities/elementFromString.js'\n\nexport type CreateNodeFromContentOptions = {\n  slice?: boolean\n  parseOptions?: ParseOptions\n  errorOnInvalidContent?: boolean\n}\n\n/**\n * Takes a JSON or HTML content and creates a Prosemirror node or fragment from it.\n * @param content The JSON or HTML content to create the node from\n * @param schema The Prosemirror schema to use for the node\n * @param options Options for the parser\n * @returns The created Prosemirror node or fragment\n */\nexport function createNodeFromContent(\n  content: Content | ProseMirrorNode | Fragment,\n  schema: Schema,\n  options?: CreateNodeFromContentOptions,\n): ProseMirrorNode | Fragment {\n  if (content instanceof ProseMirrorNode || content instanceof Fragment) {\n    return content\n  }\n  options = {\n    slice: true,\n    parseOptions: {},\n    ...options,\n  }\n\n  const isJSONContent = typeof content === 'object' && content !== null\n  const isTextContent = typeof content === 'string'\n\n  if (isJSONContent) {\n    try {\n      const isArrayContent = Array.isArray(content) && content.length > 0\n\n      // if the JSON Content is an array of nodes, create a fragment for each node\n      if (isArrayContent) {\n        return Fragment.fromArray(content.map(item => schema.nodeFromJSON(item)))\n      }\n\n      const node = schema.nodeFromJSON(content)\n\n      if (options.errorOnInvalidContent) {\n        node.check()\n      }\n\n      return node\n    } catch (error) {\n      if (options.errorOnInvalidContent) {\n        throw new Error('[tiptap error]: Invalid JSON content', { cause: error as Error })\n      }\n\n      console.warn('[tiptap warn]: Invalid content.', 'Passed value:', content, 'Error:', error)\n\n      return createNodeFromContent('', schema, options)\n    }\n  }\n\n  if (isTextContent) {\n\n    // Check for invalid content\n    if (options.errorOnInvalidContent) {\n      let hasInvalidContent = false\n      let invalidContent = ''\n\n      // A copy of the current schema with a catch-all node at the end\n      const contentCheckSchema = new Schema({\n        topNode: schema.spec.topNode,\n        marks: schema.spec.marks,\n        // Prosemirror's schemas are executed such that: the last to execute, matches last\n        // This means that we can add a catch-all node at the end of the schema to catch any content that we don't know how to handle\n        nodes: schema.spec.nodes.append({\n          __tiptap__private__unknown__catch__all__node: {\n            content: 'inline*',\n            group: 'block',\n            parseDOM: [\n              {\n                tag: '*',\n                getAttrs: e => {\n                  // If this is ever called, we know that the content has something that we don't know how to handle in the schema\n                  hasInvalidContent = true\n                  // Try to stringify the element for a more helpful error message\n                  invalidContent = typeof e === 'string' ? e : e.outerHTML\n                  return null\n                },\n              },\n            ],\n          },\n        }),\n      })\n\n      if (options.slice) {\n        DOMParser.fromSchema(contentCheckSchema).parseSlice(elementFromString(content), options.parseOptions)\n      } else {\n        DOMParser.fromSchema(contentCheckSchema).parse(elementFromString(content), options.parseOptions)\n      }\n\n      if (options.errorOnInvalidContent && hasInvalidContent) {\n        throw new Error('[tiptap error]: Invalid HTML content', { cause: new Error(`Invalid element found: ${invalidContent}`) })\n      }\n    }\n\n    const parser = DOMParser.fromSchema(schema)\n\n    if (options.slice) {\n      return parser.parseSlice(elementFromString(content), options.parseOptions).content\n    }\n\n    return parser.parse(elementFromString(content), options.parseOptions)\n\n  }\n\n  return createNodeFromContent('', schema, options)\n}\n", "import { Selection, Transaction } from '@tiptap/pm/state'\nimport { ReplaceAroundStep, ReplaceStep } from '@tiptap/pm/transform'\n\n// source: https://github.com/ProseMirror/prosemirror-state/blob/master/src/selection.js#L466\nexport function selectionToInsertionEnd(tr: Transaction, startLen: number, bias: number) {\n  const last = tr.steps.length - 1\n\n  if (last < startLen) {\n    return\n  }\n\n  const step = tr.steps[last]\n\n  if (!(step instanceof ReplaceStep || step instanceof ReplaceAroundStep)) {\n    return\n  }\n\n  const map = tr.mapping.maps[last]\n  let end = 0\n\n  map.forEach((_from, _to, _newFrom, newTo) => {\n    if (end === 0) {\n      end = newTo\n    }\n  })\n\n  tr.setSelection(Selection.near(tr.doc.resolve(end), bias))\n}\n", "import { Fragment, Node as ProseMirrorNode, ParseOptions } from '@tiptap/pm/model'\n\nimport { createNodeFromContent } from '../helpers/createNodeFromContent.js'\nimport { selectionToInsertionEnd } from '../helpers/selectionToInsertionEnd.js'\nimport { Content, Range, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    insertContentAt: {\n      /**\n       * Insert a node or string of HTML at a specific position.\n       * @example editor.commands.insertContentAt(0, '<h1>Example</h1>')\n       */\n      insertContentAt: (\n        /**\n         * The position to insert the content at.\n         */\n        position: number | Range,\n\n        /**\n         * The ProseMirror content to insert.\n         */\n        value: Content | ProseMirrorNode | Fragment,\n\n        /**\n         * Optional options\n         */\n        options?: {\n          /**\n           * Options for parsing the content.\n           */\n          parseOptions?: ParseOptions\n\n          /**\n           * Whether to update the selection after inserting the content.\n           */\n          updateSelection?: boolean\n\n          /**\n           * Whether to apply input rules after inserting the content.\n           */\n          applyInputRules?: boolean\n\n          /**\n           * Whether to apply paste rules after inserting the content.\n           */\n          applyPasteRules?: boolean\n\n          /**\n           * Whether to throw an error if the content is invalid.\n           */\n          errorOnInvalidContent?: boolean\n        },\n      ) => ReturnType\n    }\n  }\n}\n\nconst isFragment = (nodeOrFragment: ProseMirrorNode | Fragment): nodeOrFragment is Fragment => {\n  return !('type' in nodeOrFragment)\n}\n\nexport const insertContentAt: RawCommands['insertContentAt'] = (position, value, options) => ({ tr, dispatch, editor }) => {\n  if (dispatch) {\n    options = {\n      parseOptions: editor.options.parseOptions,\n      updateSelection: true,\n      applyInputRules: false,\n      applyPasteRules: false,\n      ...options,\n    }\n\n    let content: Fragment | ProseMirrorNode\n\n    const emitContentError = (error: Error) => {\n      editor.emit('contentError', {\n        editor,\n        error,\n        disableCollaboration: () => {\n          if (editor.storage.collaboration) {\n            editor.storage.collaboration.isDisabled = true\n          }\n        },\n      })\n    }\n\n    const parseOptions: ParseOptions = {\n      preserveWhitespace: 'full',\n      ...options.parseOptions,\n    }\n\n    // If `emitContentError` is enabled, we want to check the content for errors\n    // but ignore them (do not remove the invalid content from the document)\n    if (!options.errorOnInvalidContent && !editor.options.enableContentCheck && editor.options.emitContentError) {\n      try {\n        createNodeFromContent(value, editor.schema, {\n          parseOptions,\n          errorOnInvalidContent: true,\n        })\n      } catch (e) {\n        emitContentError(e as Error)\n      }\n    }\n\n    try {\n      content = createNodeFromContent(value, editor.schema, {\n        parseOptions,\n        errorOnInvalidContent: options.errorOnInvalidContent ?? editor.options.enableContentCheck,\n      })\n    } catch (e) {\n      emitContentError(e as Error)\n      return false\n    }\n\n    let { from, to } = typeof position === 'number' ? { from: position, to: position } : { from: position.from, to: position.to }\n\n    let isOnlyTextContent = true\n    let isOnlyBlockContent = true\n    const nodes = isFragment(content) ? content : [content]\n\n    nodes.forEach(node => {\n      // check if added node is valid\n      node.check()\n\n      isOnlyTextContent = isOnlyTextContent ? node.isText && node.marks.length === 0 : false\n\n      isOnlyBlockContent = isOnlyBlockContent ? node.isBlock : false\n    })\n\n    // check if we can replace the wrapping node by\n    // the newly inserted content\n    // example:\n    // replace an empty paragraph by an inserted image\n    // instead of inserting the image below the paragraph\n    if (from === to && isOnlyBlockContent) {\n      const { parent } = tr.doc.resolve(from)\n      const isEmptyTextBlock = parent.isTextblock && !parent.type.spec.code && !parent.childCount\n\n      if (isEmptyTextBlock) {\n        from -= 1\n        to += 1\n      }\n    }\n\n    let newContent\n\n    // if there is only plain text we have to use `insertText`\n    // because this will keep the current marks\n    if (isOnlyTextContent) {\n      // if value is string, we can use it directly\n      // otherwise if it is an array, we have to join it\n      if (Array.isArray(value)) {\n        newContent = value.map(v => v.text || '').join('')\n      } else if (value instanceof Fragment) {\n        let text = ''\n\n        value.forEach(node => {\n          if (node.text) {\n            text += node.text\n          }\n        })\n\n        newContent = text\n      } else if (typeof value === 'object' && !!value && !!value.text) {\n        newContent = value.text\n      } else {\n        newContent = value as string\n      }\n\n      tr.insertText(newContent, from, to)\n    } else {\n      newContent = content\n\n      tr.replaceWith(from, to, newContent)\n    }\n\n    // set cursor at end of inserted content\n    if (options.updateSelection) {\n      selectionToInsertionEnd(tr, tr.steps.length - 1, -1)\n    }\n\n    if (options.applyInputRules) {\n      tr.setMeta('applyInputRules', { from, text: newContent })\n    }\n\n    if (options.applyPasteRules) {\n      tr.setMeta('applyPasteRules', { from, text: newContent })\n    }\n  }\n\n  return true\n}\n", "import {\n  joinBackward as original<PERSON>oi<PERSON><PERSON><PERSON><PERSON>,\n  joinDown as original<PERSON>oinDown,\n  joinForward as original<PERSON>oin<PERSON><PERSON><PERSON>,\n  joinUp as original<PERSON>oinUp,\n} from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    joinUp: {\n      /**\n       * Join the selected block or, if there is a text selection, the closest ancestor block of the selection that can be joined, with the sibling above it.\n       * @example editor.commands.joinUp()\n       */\n      joinUp: () => ReturnType\n    }\n    joinDown: {\n      /**\n       * Join the selected block, or the closest ancestor of the selection that can be joined, with the sibling after it.\n       * @example editor.commands.joinDown()\n       */\n      joinDown: () => ReturnType\n    }\n    joinBackward: {\n      /**\n       * If the selection is empty and at the start of a textblock, try to reduce the distance between that block and the one before it—if there's a block directly before it that can be joined, join them.\n       * If not, try to move the selected block closer to the next one in the document structure by lifting it out of its\n       * parent or moving it into a parent of the previous block. Will use the view for accurate (bidi-aware) start-of-textblock detection if given.\n       * @example editor.commands.joinBackward()\n       */\n      joinBackward: () => ReturnType\n    }\n    joinForward: {\n      /**\n       * If the selection is empty and the cursor is at the end of a textblock, try to reduce or remove the boundary between that block and the one after it,\n       * either by joining them or by moving the other block closer to this one in the tree structure.\n       * Will use the view for accurate start-of-textblock detection if given.\n       * @example editor.commands.joinForward()\n       */\n      joinForward: () => ReturnType\n    }\n  }\n}\n\nexport const joinUp: RawCommands['joinUp'] = () => ({ state, dispatch }) => {\n  return originalJoinUp(state, dispatch)\n}\n\nexport const joinDown: RawCommands['joinDown'] = () => ({ state, dispatch }) => {\n  return originalJoinDown(state, dispatch)\n}\n\nexport const joinBackward: RawCommands['joinBackward'] = () => ({ state, dispatch }) => {\n  return originalJoinBackward(state, dispatch)\n}\n\nexport const joinForward: RawCommands['joinForward'] = () => ({ state, dispatch }) => {\n  return originalJoinForward(state, dispatch)\n}\n", "import { joinPoint } from '@tiptap/pm/transform'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    joinItemBackward: {\n      /**\n       * Join two items backward.\n       * @example editor.commands.joinItemBackward()\n       */\n      joinItemBackward: () => ReturnType\n    }\n  }\n}\n\nexport const joinItemBackward: RawCommands['joinItemBackward'] = () => ({\n  state,\n  dispatch,\n  tr,\n}) => {\n  try {\n    const point = joinPoint(state.doc, state.selection.$from.pos, -1)\n\n    if (point === null || point === undefined) {\n      return false\n    }\n\n    tr.join(point, 2)\n\n    if (dispatch) {\n      dispatch(tr)\n    }\n\n    return true\n  } catch {\n    return false\n  }\n}\n", "import { joinPoint } from '@tiptap/pm/transform'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    joinItemForward: {\n      /**\n       * Join two items Forwards.\n       * @example editor.commands.joinItemForward()\n       */\n      joinItemForward: () => ReturnType\n    }\n  }\n}\n\nexport const joinItemForward: RawCommands['joinItemForward'] = () => ({\n  state,\n  dispatch,\n  tr,\n}) => {\n  try {\n    const point = joinPoint(state.doc, state.selection.$from.pos, +1)\n\n    if (point === null || point === undefined) {\n      return false\n    }\n\n    tr.join(point, 2)\n\n    if (dispatch) {\n      dispatch(tr)\n    }\n\n    return true\n  } catch {\n    return false\n  }\n}\n", "import { joinTextblockBackward as originalCommand } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    joinTextblockBackward: {\n      /**\n       * A more limited form of joinBackward that only tries to join the current textblock to the one before it, if the cursor is at the start of a textblock.\n       */\n      joinTextblockBackward: () => ReturnType\n    }\n  }\n}\n\nexport const joinTextblockBackward: RawCommands['joinTextblockBackward'] = () => ({ state, dispatch }) => {\n  return originalCommand(state, dispatch)\n}\n", "import { joinTextblockForward as originalCommand } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    joinTextblockForward: {\n      /**\n       * A more limited form of joinForward that only tries to join the current textblock to the one after it, if the cursor is at the end of a textblock.\n       */\n      joinTextblockForward: () => ReturnType\n    }\n  }\n}\n\nexport const joinTextblockForward: RawCommands['joinTextblockForward'] = () => ({ state, dispatch }) => {\n  return originalCommand(state, dispatch)\n}\n", "export function isMacOS(): boolean {\n  return typeof navigator !== 'undefined'\n    ? /Mac/.test(navigator.platform)\n    : false\n}\n", "import { RawCommands } from '../types.js'\nimport { isiOS } from '../utilities/isiOS.js'\nimport { isMacOS } from '../utilities/isMacOS.js'\n\nfunction normalizeKeyName(name: string) {\n  const parts = name.split(/-(?!$)/)\n  let result = parts[parts.length - 1]\n\n  if (result === 'Space') {\n    result = ' '\n  }\n\n  let alt\n  let ctrl\n  let shift\n  let meta\n\n  for (let i = 0; i < parts.length - 1; i += 1) {\n    const mod = parts[i]\n\n    if (/^(cmd|meta|m)$/i.test(mod)) {\n      meta = true\n    } else if (/^a(lt)?$/i.test(mod)) {\n      alt = true\n    } else if (/^(c|ctrl|control)$/i.test(mod)) {\n      ctrl = true\n    } else if (/^s(hift)?$/i.test(mod)) {\n      shift = true\n    } else if (/^mod$/i.test(mod)) {\n      if (isiOS() || isMacOS()) {\n        meta = true\n      } else {\n        ctrl = true\n      }\n    } else {\n      throw new Error(`Unrecognized modifier name: ${mod}`)\n    }\n  }\n\n  if (alt) {\n    result = `Alt-${result}`\n  }\n\n  if (ctrl) {\n    result = `Ctrl-${result}`\n  }\n\n  if (meta) {\n    result = `Meta-${result}`\n  }\n\n  if (shift) {\n    result = `Shift-${result}`\n  }\n\n  return result\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    keyboardShortcut: {\n      /**\n       * Trigger a keyboard shortcut.\n       * @param name The name of the keyboard shortcut.\n       * @example editor.commands.keyboardShortcut('Mod-b')\n       */\n      keyboardShortcut: (name: string) => ReturnType,\n    }\n  }\n}\n\nexport const keyboardShortcut: RawCommands['keyboardShortcut'] = name => ({\n  editor,\n  view,\n  tr,\n  dispatch,\n}) => {\n  const keys = normalizeKeyName(name).split(/-(?!$)/)\n  const key = keys.find(item => !['Alt', 'Ctrl', 'Meta', 'Shift'].includes(item))\n  const event = new KeyboardEvent('keydown', {\n    key: key === 'Space'\n      ? ' '\n      : key,\n    altKey: keys.includes('Alt'),\n    ctrlKey: keys.includes('Ctrl'),\n    metaKey: keys.includes('Meta'),\n    shiftKey: keys.includes('Shift'),\n    bubbles: true,\n    cancelable: true,\n  })\n\n  const capturedTransaction = editor.captureTransaction(() => {\n    view.someProp('handleKeyDown', f => f(view, event))\n  })\n\n  capturedTransaction?.steps.forEach(step => {\n    const newStep = step.map(tr.mapping)\n\n    if (newStep && dispatch) {\n      tr.maybeStep(newStep)\n    }\n  })\n\n  return true\n}\n", "import { NodeType } from '@tiptap/pm/model'\nimport { EditorState } from '@tiptap/pm/state'\n\nimport { NodeRange } from '../types.js'\nimport { objectIncludes } from '../utilities/objectIncludes.js'\nimport { getNodeType } from './getNodeType.js'\n\nexport function isNodeActive(\n  state: EditorState,\n  typeOrName: NodeType | string | null,\n  attributes: Record<string, any> = {},\n): boolean {\n  const { from, to, empty } = state.selection\n  const type = typeOrName ? getNodeType(typeOrName, state.schema) : null\n\n  const nodeRanges: NodeRange[] = []\n\n  state.doc.nodesBetween(from, to, (node, pos) => {\n    if (node.isText) {\n      return\n    }\n\n    const relativeFrom = Math.max(from, pos)\n    const relativeTo = Math.min(to, pos + node.nodeSize)\n\n    nodeRanges.push({\n      node,\n      from: relativeFrom,\n      to: relativeTo,\n    })\n  })\n\n  const selectionRange = to - from\n  const matchedNodeRanges = nodeRanges\n    .filter(nodeRange => {\n      if (!type) {\n        return true\n      }\n\n      return type.name === nodeRange.node.type.name\n    })\n    .filter(nodeRange => objectIncludes(nodeRange.node.attrs, attributes, { strict: false }))\n\n  if (empty) {\n    return !!matchedNodeRanges.length\n  }\n\n  const range = matchedNodeRanges.reduce((sum, nodeRange) => sum + nodeRange.to - nodeRange.from, 0)\n\n  return range >= selectionRange\n}\n", "import { lift as originalLift } from '@tiptap/pm/commands'\nimport { NodeType } from '@tiptap/pm/model'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { isNodeActive } from '../helpers/isNodeActive.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    lift: {\n      /**\n       * Removes an existing wrap if possible lifting the node out of it\n       * @param typeOrName The type or name of the node.\n       * @param attributes The attributes of the node.\n       * @example editor.commands.lift('paragraph')\n       * @example editor.commands.lift('heading', { level: 1 })\n       */\n      lift: (typeOrName: string | NodeType, attributes?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nexport const lift: RawCommands['lift'] = (typeOrName, attributes = {}) => ({ state, dispatch }) => {\n  const type = getNodeType(typeOrName, state.schema)\n  const isActive = isNodeActive(state, type, attributes)\n\n  if (!isActive) {\n    return false\n  }\n\n  return originalLift(state, dispatch)\n}\n", "import { liftEmptyBlock as originalLiftEmptyBlock } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    liftEmptyBlock: {\n      /**\n       * If the cursor is in an empty textblock that can be lifted, lift the block.\n       * @example editor.commands.liftEmptyBlock()\n       */\n      liftEmptyBlock: () => ReturnType,\n    }\n  }\n}\n\nexport const liftEmptyBlock: RawCommands['liftEmptyBlock'] = () => ({ state, dispatch }) => {\n  return originalLiftEmptyBlock(state, dispatch)\n}\n", "import { NodeType } from '@tiptap/pm/model'\nimport { liftListItem as originalLiftListItem } from '@tiptap/pm/schema-list'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    liftListItem: {\n      /**\n       * Create a command to lift the list item around the selection up into a wrapping list.\n       * @param typeOrName The type or name of the node.\n       * @example editor.commands.liftListItem('listItem')\n       */\n      liftListItem: (typeOrName: string | NodeType) => ReturnType\n    }\n  }\n}\n\nexport const liftListItem: RawCommands['liftListItem'] = typeOrName => ({ state, dispatch }) => {\n  const type = getNodeType(typeOrName, state.schema)\n\n  return originalLiftListItem(type)(state, dispatch)\n}\n", "import { newlineInCode as originalNewlineInCode } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    newlineInCode: {\n      /**\n       * Add a newline character in code.\n       * @example editor.commands.newlineInCode()\n       */\n      newlineInCode: () => ReturnType\n    }\n  }\n}\n\nexport const newlineInCode: RawCommands['newlineInCode'] = () => ({ state, dispatch }) => {\n  return originalNewlineInCode(state, dispatch)\n}\n", "import { Schema } from '@tiptap/pm/model'\n\n/**\n * Get the type of a schema item by its name.\n * @param name The name of the schema item\n * @param schema The Prosemiror schema to search in\n * @returns The type of the schema item (`node` or `mark`), or null if it doesn't exist\n */\nexport function getSchemaTypeNameByName(name: string, schema: Schema): 'node' | 'mark' | null {\n  if (schema.nodes[name]) {\n    return 'node'\n  }\n\n  if (schema.marks[name]) {\n    return 'mark'\n  }\n\n  return null\n}\n", "/**\n * Remove a property or an array of properties from an object\n * @param obj Object\n * @param key Key to remove\n */\nexport function deleteProps(obj: Record<string, any>, propOrProps: string | string[]): Record<string, any> {\n  const props = typeof propOrProps === 'string'\n    ? [propOrProps]\n    : propOrProps\n\n  return Object\n    .keys(obj)\n    .reduce((newObj: Record<string, any>, prop) => {\n      if (!props.includes(prop)) {\n        newObj[prop] = obj[prop]\n      }\n\n      return newObj\n    }, {})\n}\n", "import { MarkType, NodeType } from '@tiptap/pm/model'\n\nimport { getMarkType } from '../helpers/getMarkType.js'\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { getSchemaTypeNameByName } from '../helpers/getSchemaTypeNameByName.js'\nimport { RawCommands } from '../types.js'\nimport { deleteProps } from '../utilities/deleteProps.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    resetAttributes: {\n      /**\n       * Resets some node attributes to the default value.\n       * @param typeOrName The type or name of the node.\n       * @param attributes The attributes of the node to reset.\n       * @example editor.commands.resetAttributes('heading', 'level')\n       */\n      resetAttributes: (\n        typeOrName: string | NodeType | MarkType,\n        attributes: string | string[],\n      ) => ReturnType\n    }\n  }\n}\n\nexport const resetAttributes: RawCommands['resetAttributes'] = (typeOrName, attributes) => ({ tr, state, dispatch }) => {\n  let nodeType: NodeType | null = null\n  let markType: MarkType | null = null\n\n  const schemaType = getSchemaTypeNameByName(\n    typeof typeOrName === 'string' ? typeOrName : typeOrName.name,\n    state.schema,\n  )\n\n  if (!schemaType) {\n    return false\n  }\n\n  if (schemaType === 'node') {\n    nodeType = getNodeType(typeOrName as NodeType, state.schema)\n  }\n\n  if (schemaType === 'mark') {\n    markType = getMarkType(typeOrName as MarkType, state.schema)\n  }\n\n  if (dispatch) {\n    tr.selection.ranges.forEach(range => {\n      state.doc.nodesBetween(range.$from.pos, range.$to.pos, (node, pos) => {\n        if (nodeType && nodeType === node.type) {\n          tr.setNodeMarkup(pos, undefined, deleteProps(node.attrs, attributes))\n        }\n\n        if (markType && node.marks.length) {\n          node.marks.forEach(mark => {\n            if (markType === mark.type) {\n              tr.addMark(\n                pos,\n                pos + node.nodeSize,\n                markType.create(deleteProps(mark.attrs, attributes)),\n              )\n            }\n          })\n        }\n      })\n    })\n  }\n\n  return true\n}\n", "import { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    scrollIntoView: {\n      /**\n       * Scroll the selection into view.\n       * @example editor.commands.scrollIntoView()\n       */\n      scrollIntoView: () => ReturnType,\n    }\n  }\n}\n\nexport const scrollIntoView: RawCommands['scrollIntoView'] = () => ({ tr, dispatch }) => {\n  if (dispatch) {\n    tr.scrollIntoView()\n  }\n\n  return true\n}\n", "import { AllSelection } from '@tiptap/pm/state'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    selectAll: {\n      /**\n       * Select the whole document.\n       * @example editor.commands.selectAll()\n       */\n      selectAll: () => ReturnType,\n    }\n  }\n}\n\nexport const selectAll: RawCommands['selectAll'] = () => ({ tr, dispatch }) => {\n  if (dispatch) {\n    const selection = new AllSelection(tr.doc)\n\n    tr.setSelection(selection)\n  }\n\n  return true\n}\n", "import { selectNodeBackward as originalSelectNodeBackward } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    selectNodeBackward: {\n      /**\n       * Select a node backward.\n       * @example editor.commands.selectNodeBackward()\n       */\n      selectNodeBackward: () => ReturnType\n    }\n  }\n}\n\nexport const selectNodeBackward: RawCommands['selectNodeBackward'] = () => ({ state, dispatch }) => {\n  return originalSelectNodeBackward(state, dispatch)\n}\n", "import { selectNodeForward as originalSelectNodeForward } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    selectNodeForward: {\n      /**\n       * Select a node forward.\n       * @example editor.commands.selectNodeForward()\n       */\n      selectNodeForward: () => ReturnType\n    }\n  }\n}\n\nexport const selectNodeForward: RawCommands['selectNodeForward'] = () => ({ state, dispatch }) => {\n  return originalSelectNodeForward(state, dispatch)\n}\n", "import { selectParentNode as originalSelectParentNode } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    selectParentNode: {\n      /**\n       * Select the parent node.\n       * @example editor.commands.selectParentNode()\n       */\n      selectParentNode: () => ReturnType\n    }\n  }\n}\n\nexport const selectParentNode: RawCommands['selectParentNode'] = () => ({ state, dispatch }) => {\n  return originalSelectParentNode(state, dispatch)\n}\n", "// @ts-ignore\n// TODO: add types to @types/prosemirror-commands\nimport { selectTextblockEnd as originalSelectTextblockEnd } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    selectTextblockEnd: {\n      /**\n       * Moves the cursor to the end of current text block.\n       * @example editor.commands.selectTextblockEnd()\n       */\n      selectTextblockEnd: () => ReturnType\n    }\n  }\n}\n\nexport const selectTextblockEnd: RawCommands['selectTextblockEnd'] = () => ({ state, dispatch }) => {\n  return originalSelectTextblockEnd(state, dispatch)\n}\n", "// @ts-ignore\n// TODO: add types to @types/prosemirror-commands\nimport { selectTextblockStart as originalSelectTextblockStart } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    selectTextblockStart: {\n      /**\n       * Moves the cursor to the start of current text block.\n       * @example editor.commands.selectTextblockStart()\n       */\n      selectTextblockStart: () => ReturnType\n    }\n  }\n}\n\nexport const selectTextblockStart: RawCommands['selectTextblockStart'] = () => ({ state, dispatch }) => {\n  return originalSelectTextblockStart(state, dispatch)\n}\n", "import {\n  Fragment, Node as ProseMirrorNode, ParseOptions, Schema,\n} from '@tiptap/pm/model'\n\nimport { Content } from '../types.js'\nimport { createNodeFromContent } from './createNodeFromContent.js'\n\n/**\n * Create a new Prosemirror document node from content.\n * @param content The JSON or HTML content to create the document from\n * @param schema The Prosemirror schema to use for the document\n * @param parseOptions Options for the parser\n * @returns The created Prosemirror document node\n */\nexport function createDocument(\n  content: Content | ProseMirrorNode | Fragment,\n  schema: Schema,\n  parseOptions: ParseOptions = {},\n  options: { errorOnInvalidContent?: boolean } = {},\n): ProseMirrorNode {\n  return createNodeFromContent(content, schema, {\n    slice: false,\n    parseOptions,\n    errorOnInvalidContent: options.errorOnInvalidContent,\n  }) as ProseMirrorNode\n}\n", "import { Fragment, Node as ProseMirrorNode, ParseOptions } from '@tiptap/pm/model'\n\nimport { createDocument } from '../helpers/createDocument.js'\nimport { Content, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    setContent: {\n      /**\n       * Replace the whole document with new content.\n       * @param content The new content.\n       * @param emitUpdate Whether to emit an update event.\n       * @param parseOptions Options for parsing the content.\n       * @example editor.commands.setContent('<p>Example text</p>')\n       */\n      setContent: (\n        /**\n         * The new content.\n         */\n        content: Content | Fragment | ProseMirrorNode,\n\n        /**\n         * Whether to emit an update event.\n         * @default false\n         */\n        emitUpdate?: boolean,\n\n        /**\n         * Options for parsing the content.\n         * @default {}\n         */\n        parseOptions?: ParseOptions,\n        /**\n         * Options for `setContent`.\n         */\n        options?: {\n          /**\n           * Whether to throw an error if the content is invalid.\n           */\n          errorOnInvalidContent?: boolean;\n        }\n      ) => ReturnType;\n    };\n  }\n}\n\nexport const setContent: RawCommands['setContent'] = (content, emitUpdate = false, parseOptions = {}, options = {}) => ({\n  editor, tr, dispatch, commands,\n}) => {\n  const { doc } = tr\n\n  // This is to keep backward compatibility with the previous behavior\n  // TODO remove this in the next major version\n  if (parseOptions.preserveWhitespace !== 'full') {\n    const document = createDocument(content, editor.schema, parseOptions, {\n      errorOnInvalidContent: options.errorOnInvalidContent ?? editor.options.enableContentCheck,\n    })\n\n    if (dispatch) {\n      tr.replaceWith(0, doc.content.size, document).setMeta('preventUpdate', !emitUpdate)\n    }\n    return true\n  }\n\n  if (dispatch) {\n    tr.setMeta('preventUpdate', !emitUpdate)\n  }\n\n  return commands.insertContentAt({ from: 0, to: doc.content.size }, content, {\n    parseOptions,\n    errorOnInvalidContent: options.errorOnInvalidContent ?? editor.options.enableContentCheck,\n  })\n}\n", "import { Mark, MarkType } from '@tiptap/pm/model'\nimport { EditorState } from '@tiptap/pm/state'\n\nimport { getMarkType } from './getMarkType.js'\n\nexport function getMarkAttributes(\n  state: EditorState,\n  typeOrName: string | MarkType,\n): Record<string, any> {\n  const type = getMarkType(typeOrName, state.schema)\n  const { from, to, empty } = state.selection\n  const marks: Mark[] = []\n\n  if (empty) {\n    if (state.storedMarks) {\n      marks.push(...state.storedMarks)\n    }\n\n    marks.push(...state.selection.$head.marks())\n  } else {\n    state.doc.nodesBetween(from, to, node => {\n      marks.push(...node.marks)\n    })\n  }\n\n  const mark = marks.find(markItem => markItem.type.name === type.name)\n\n  if (!mark) {\n    return {}\n  }\n\n  return { ...mark.attrs }\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { Transaction } from '@tiptap/pm/state'\nimport { Transform } from '@tiptap/pm/transform'\n\n/**\n * Returns a new `Transform` based on all steps of the passed transactions.\n * @param oldDoc The Prosemirror node to start from\n * @param transactions The transactions to combine\n * @returns A new `Transform` with all steps of the passed transactions\n */\nexport function combineTransactionSteps(\n  oldDoc: ProseMirrorNode,\n  transactions: Transaction[],\n): Transform {\n  const transform = new Transform(oldDoc)\n\n  transactions.forEach(transaction => {\n    transaction.steps.forEach(step => {\n      transform.step(step)\n    })\n  })\n\n  return transform\n}\n", "import { ContentMatch, NodeType } from '@tiptap/pm/model'\n\n/**\n * Gets the default block type at a given match\n * @param match The content match to get the default block type from\n * @returns The default block type or null\n */\nexport function defaultBlockAt(match: ContentMatch): NodeType | null {\n  for (let i = 0; i < match.edgeCount; i += 1) {\n    const { type } = match.edge(i)\n\n    if (type.isTextblock && !type.hasRequiredAttrs()) {\n      return type\n    }\n  }\n\n  return null\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport { NodeWithPos, Predicate } from '../types.js'\n\n/**\n * Find children inside a Prosemirror node that match a predicate.\n * @param node The Prosemirror node to search in\n * @param predicate The predicate to match\n * @returns An array of nodes with their positions\n */\nexport function findChildren(node: ProseMirrorNode, predicate: Predicate): NodeWithPos[] {\n  const nodesWithPos: NodeWithPos[] = []\n\n  node.descendants((child, pos) => {\n    if (predicate(child)) {\n      nodesWithPos.push({\n        node: child,\n        pos,\n      })\n    }\n  })\n\n  return nodesWithPos\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport { NodeWithPos, Predicate, Range } from '../types.js'\n\n/**\n * Same as `find<PERSON><PERSON>dren` but searches only within a `range`.\n * @param node The Prosemirror node to search in\n * @param range The range to search in\n * @param predicate The predicate to match\n * @returns An array of nodes with their positions\n */\nexport function findChildrenInRange(\n  node: ProseMirrorNode,\n  range: Range,\n  predicate: Predicate,\n): NodeWithPos[] {\n  const nodesWithPos: NodeWithPos[] = []\n\n  // if (range.from === range.to) {\n  //   const nodeAt = node.nodeAt(range.from)\n\n  //   if (nodeAt) {\n  //     nodesWithPos.push({\n  //       node: nodeAt,\n  //       pos: range.from,\n  //     })\n  //   }\n  // }\n\n  node.nodesBetween(range.from, range.to, (child, pos) => {\n    if (predicate(child)) {\n      nodesWithPos.push({\n        node: child,\n        pos,\n      })\n    }\n  })\n\n  return nodesWithPos\n}\n", "import { Node as ProseMirrorNode, ResolvedPos } from '@tiptap/pm/model'\n\nimport { Predicate } from '../types.js'\n\n/**\n * Finds the closest parent node to a resolved position that matches a predicate.\n * @param $pos The resolved position to search from\n * @param predicate The predicate to match\n * @returns The closest parent node to the resolved position that matches the predicate\n * @example ```js\n * findParentNodeClosestToPos($from, node => node.type.name === 'paragraph')\n * ```\n */\nexport function findParentNodeClosestToPos(\n  $pos: ResolvedPos,\n  predicate: Predicate,\n):\n  | {\n      pos: number\n      start: number\n      depth: number\n      node: ProseMirrorNode\n    }\n  | undefined {\n  for (let i = $pos.depth; i > 0; i -= 1) {\n    const node = $pos.node(i)\n\n    if (predicate(node)) {\n      return {\n        pos: i > 0 ? $pos.before(i) : 0,\n        start: $pos.start(i),\n        depth: i,\n        node,\n      }\n    }\n  }\n}\n", "import { Selection } from '@tiptap/pm/state'\n\nimport { Predicate } from '../types.js'\nimport { findParentNodeClosestToPos } from './findParentNodeClosestToPos.js'\n\n/**\n * Finds the closest parent node to the current selection that matches a predicate.\n * @param predicate The predicate to match\n * @returns A command that finds the closest parent node to the current selection that matches the predicate\n * @example ```js\n * findParentNode(node => node.type.name === 'paragraph')\n * ```\n */\nexport function findParentNode(predicate: Predicate) {\n  return (selection: Selection) => findParentNodeClosestToPos(selection.$from, predicate)\n}\n", "import { Schema } from '@tiptap/pm/model'\n\nimport { Editor } from '../Editor.js'\nimport { ExtensionManager } from '../ExtensionManager.js'\nimport { Extensions } from '../types.js'\nimport { getSchemaByResolvedExtensions } from './getSchemaByResolvedExtensions.js'\n\nexport function getSchema(extensions: Extensions, editor?: Editor): Schema {\n  const resolvedExtensions = ExtensionManager.resolve(extensions)\n\n  return getSchemaByResolvedExtensions(resolvedExtensions, editor)\n}\n", "import { Node } from '@tiptap/pm/model'\n\nimport { Extensions, JSONContent } from '../types.js'\nimport { getHTMLFromFragment } from './getHTMLFromFragment.js'\nimport { getSchema } from './getSchema.js'\n\n/**\n * Generate HTML from a JSONContent\n * @param doc The JSONContent to generate HTML from\n * @param extensions The extensions to use for the schema\n * @returns The generated HTML\n */\nexport function generateHTML(doc: JSONContent, extensions: Extensions): string {\n  const schema = getSchema(extensions)\n  const contentNode = Node.fromJSON(schema, doc)\n\n  return getHTMLFromFragment(contentNode.content, schema)\n}\n", "import { DOMParser } from '@tiptap/pm/model'\n\nimport { Extensions } from '../types.js'\nimport { elementFromString } from '../utilities/elementFromString.js'\nimport { getSchema } from './getSchema.js'\n\n/**\n * Generate JSONContent from HTML\n * @param html The HTML to generate J<PERSON><PERSON>ontent from\n * @param extensions The extensions to use for the schema\n * @returns The generated JSONContent\n */\nexport function generateJSON(html: string, extensions: Extensions): Record<string, any> {\n  const schema = getSchema(extensions)\n  const dom = elementFromString(html)\n\n  return DOMParser.fromSchema(schema).parse(dom).toJSON()\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport { TextSerializer } from '../types.js'\nimport { getTextBetween } from './getTextBetween.js'\n\n/**\n * Gets the text of a Prosemirror node\n * @param node The Prosemirror node\n * @param options Options for the text serializer & block separator\n * @returns The text of the node\n * @example ```js\n * const text = getText(node, { blockSeparator: '\\n' })\n * ```\n */\nexport function getText(\n  node: ProseMirrorNode,\n  options?: {\n    blockSeparator?: string\n    textSerializers?: Record<string, TextSerializer>\n  },\n) {\n  const range = {\n    from: 0,\n    to: node.content.size,\n  }\n\n  return getTextBetween(node, range, options)\n}\n", "import { Node } from '@tiptap/pm/model'\n\nimport { Extensions, JSONContent, TextSerializer } from '../types.js'\nimport { getSchema } from './getSchema.js'\nimport { getText } from './getText.js'\nimport { getTextSerializersFromSchema } from './getTextSerializersFromSchema.js'\n\n/**\n * Generate raw text from a JSONContent\n * @param doc The JSONContent to generate text from\n * @param extensions The extensions to use for the schema\n * @param options Options for the text generation f.e. blockSeparator or textSerializers\n * @returns The generated text\n */\nexport function generateText(\n  doc: JSONContent,\n  extensions: Extensions,\n  options?: {\n    blockSeparator?: string\n    textSerializers?: Record<string, TextSerializer>\n  },\n): string {\n  const { blockSeparator = '\\n\\n', textSerializers = {} } = options || {}\n  const schema = getSchema(extensions)\n  const contentNode = Node.fromJSON(schema, doc)\n\n  return getText(contentNode, {\n    blockSeparator,\n    textSerializers: {\n      ...getTextSerializersFromSchema(schema),\n      ...textSerializers,\n    },\n  })\n}\n", "import { Node, NodeType } from '@tiptap/pm/model'\nimport { EditorState } from '@tiptap/pm/state'\n\nimport { getNodeType } from './getNodeType.js'\n\nexport function getNodeAttributes(\n  state: EditorState,\n  typeOrName: string | NodeType,\n): Record<string, any> {\n  const type = getNodeType(typeOrName, state.schema)\n  const { from, to } = state.selection\n  const nodes: Node[] = []\n\n  state.doc.nodesBetween(from, to, node => {\n    nodes.push(node)\n  })\n\n  const node = nodes.reverse().find(nodeItem => nodeItem.type.name === type.name)\n\n  if (!node) {\n    return {}\n  }\n\n  return { ...node.attrs }\n}\n", "import { MarkType, NodeType } from '@tiptap/pm/model'\nimport { EditorState } from '@tiptap/pm/state'\n\nimport { getMarkAttributes } from './getMarkAttributes.js'\nimport { getNodeAttributes } from './getNodeAttributes.js'\nimport { getSchemaTypeNameByName } from './getSchemaTypeNameByName.js'\n\n/**\n * Get node or mark attributes by type or name on the current editor state\n * @param state The current editor state\n * @param typeOrName The node or mark type or name\n * @returns The attributes of the node or mark or an empty object\n */\nexport function getAttributes(\n  state: EditorState,\n  typeOrName: string | NodeType | MarkType,\n): Record<string, any> {\n  const schemaType = getSchemaTypeNameByName(\n    typeof typeOrName === 'string' ? typeOrName : typeOrName.name,\n    state.schema,\n  )\n\n  if (schemaType === 'node') {\n    return getNodeAttributes(state, typeOrName as NodeType)\n  }\n\n  if (schemaType === 'mark') {\n    return getMarkAttributes(state, typeOrName as MarkType)\n  }\n\n  return {}\n}\n", "/**\n * Removes duplicated values within an array.\n * Supports numbers, strings and objects.\n */\nexport function removeDuplicates<T>(array: T[], by = JSON.stringify): T[] {\n  const seen: Record<any, any> = {}\n\n  return array.filter(item => {\n    const key = by(item)\n\n    return Object.prototype.hasOwnProperty.call(seen, key)\n      ? false\n      : (seen[key] = true)\n  })\n}\n", "import { Step, Transform } from '@tiptap/pm/transform'\n\nimport { Range } from '../types.js'\nimport { removeDuplicates } from '../utilities/removeDuplicates.js'\n\nexport type ChangedRange = {\n  oldRange: Range,\n  newRange: Range,\n}\n\n/**\n * Removes duplicated ranges and ranges that are\n * fully captured by other ranges.\n */\nfunction simplifyChangedRanges(changes: ChangedRange[]): ChangedRange[] {\n  const uniqueChanges = removeDuplicates(changes)\n\n  return uniqueChanges.length === 1\n    ? uniqueChanges\n    : uniqueChanges.filter((change, index) => {\n      const rest = uniqueChanges.filter((_, i) => i !== index)\n\n      return !rest.some(otherChange => {\n        return change.oldRange.from >= otherChange.oldRange.from\n          && change.oldRange.to <= otherChange.oldRange.to\n          && change.newRange.from >= otherChange.newRange.from\n          && change.newRange.to <= otherChange.newRange.to\n      })\n    })\n}\n\n/**\n * Returns a list of changed ranges\n * based on the first and last state of all steps.\n */\nexport function getChangedRanges(transform: Transform): ChangedRange[] {\n  const { mapping, steps } = transform\n  const changes: ChangedRange[] = []\n\n  mapping.maps.forEach((stepMap, index) => {\n    const ranges: Range[] = []\n\n    // This accounts for step changes where no range was actually altered\n    // e.g. when setting a mark, node attribute, etc.\n    // @ts-ignore\n    if (!stepMap.ranges.length) {\n      const { from, to } = steps[index] as Step & {\n        from?: number,\n        to?: number,\n      }\n\n      if (from === undefined || to === undefined) {\n        return\n      }\n\n      ranges.push({ from, to })\n    } else {\n      stepMap.forEach((from, to) => {\n        ranges.push({ from, to })\n      })\n    }\n\n    ranges.forEach(({ from, to }) => {\n      const newStart = mapping.slice(index).map(from, -1)\n      const newEnd = mapping.slice(index).map(to)\n      const oldStart = mapping.invert().map(newStart, -1)\n      const oldEnd = mapping.invert().map(newEnd)\n\n      changes.push({\n        oldRange: {\n          from: oldStart,\n          to: oldEnd,\n        },\n        newRange: {\n          from: newStart,\n          to: newEnd,\n        },\n      })\n    })\n  })\n\n  return simplifyChangedRanges(changes)\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport { JSONContent } from '../types.js'\n\ninterface DebugJSONContent extends JSO<PERSON>ontent {\n  from: number\n  to: number\n}\n\nexport function getDebugJSON(node: ProseMirrorNode, startOffset = 0): DebugJSONContent {\n  const isTopNode = node.type === node.type.schema.topNodeType\n  const increment = isTopNode ? 0 : 1\n  const from = startOffset\n  const to = from + node.nodeSize\n  const marks = node.marks.map(mark => {\n    const output: { type: string; attrs?: Record<string, any> } = {\n      type: mark.type.name,\n    }\n\n    if (Object.keys(mark.attrs).length) {\n      output.attrs = { ...mark.attrs }\n    }\n\n    return output\n  })\n  const attrs = { ...node.attrs }\n  const output: DebugJSONContent = {\n    type: node.type.name,\n    from,\n    to,\n  }\n\n  if (Object.keys(attrs).length) {\n    output.attrs = attrs\n  }\n\n  if (marks.length) {\n    output.marks = marks\n  }\n\n  if (node.content.childCount) {\n    output.content = []\n\n    node.forEach((child, offset) => {\n      output.content?.push(getDebugJSON(child, startOffset + offset + increment))\n    })\n  }\n\n  if (node.text) {\n    output.text = node.text\n  }\n\n  return output\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport { MarkRange } from '../types.js'\nimport { getMarkRange } from './getMarkRange.js'\n\nexport function getMarksBetween(from: number, to: number, doc: ProseMirrorNode): MarkRange[] {\n  const marks: MarkRange[] = []\n\n  // get all inclusive marks on empty selection\n  if (from === to) {\n    doc\n      .resolve(from)\n      .marks()\n      .forEach(mark => {\n        const $pos = doc.resolve(from)\n        const range = getMarkRange($pos, mark.type)\n\n        if (!range) {\n          return\n        }\n\n        marks.push({\n          mark,\n          ...range,\n        })\n      })\n  } else {\n    doc.nodesBetween(from, to, (node, pos) => {\n      if (!node || node?.nodeSize === undefined) {\n        return\n      }\n\n      marks.push(\n        ...node.marks.map(mark => ({\n          from: pos,\n          to: pos + node.nodeSize,\n          mark,\n        })),\n      )\n    })\n  }\n\n  return marks\n}\n", "import { Node, NodeType } from '@tiptap/pm/model'\nimport { EditorState } from '@tiptap/pm/state'\n\n/**\n * Finds the first node of a given type or name in the current selection.\n * @param state The editor state.\n * @param typeOrName The node type or name.\n * @param pos The position to start searching from.\n * @param maxDepth The maximum depth to search.\n * @returns The node and the depth as an array.\n */\nexport const getNodeAtPosition = (state: EditorState, typeOrName: string | NodeType, pos: number, maxDepth = 20) => {\n  const $pos = state.doc.resolve(pos)\n\n  let currentDepth = maxDepth\n  let node: Node | null = null\n\n  while (currentDepth > 0 && node === null) {\n    const currentNode = $pos.node(currentDepth)\n\n    if (currentNode?.type.name === typeOrName) {\n      node = currentNode\n    } else {\n      currentDepth -= 1\n    }\n  }\n\n  return [node, currentDepth] as [Node | null, number]\n}\n", "import { ExtensionAttribute } from '../types.js'\n\n/**\n * Return attributes of an extension that should be splitted by keepOnSplit flag\n * @param extensionAttributes Array of extension attributes\n * @param typeName The type of the extension\n * @param attributes The attributes of the extension\n * @returns The splitted attributes\n */\nexport function getSplittedAttributes(\n  extensionAttributes: ExtensionAttribute[],\n  typeName: string,\n  attributes: Record<string, any>,\n): Record<string, any> {\n  return Object.fromEntries(Object\n    .entries(attributes)\n    .filter(([name]) => {\n      const extensionAttribute = extensionAttributes.find(item => {\n        return item.type === typeName && item.name === name\n      })\n\n      if (!extensionAttribute) {\n        return false\n      }\n\n      return extensionAttribute.attribute.keepOnSplit\n    }))\n}\n", "import { MarkType } from '@tiptap/pm/model'\nimport { EditorState } from '@tiptap/pm/state'\n\nimport { MarkRange } from '../types.js'\nimport { objectIncludes } from '../utilities/objectIncludes.js'\nimport { getMarkType } from './getMarkType.js'\n\nexport function isMarkActive(\n  state: EditorState,\n  typeOrName: MarkType | string | null,\n  attributes: Record<string, any> = {},\n): boolean {\n  const { empty, ranges } = state.selection\n  const type = typeOrName ? getMarkType(typeOrName, state.schema) : null\n\n  if (empty) {\n    return !!(state.storedMarks || state.selection.$from.marks())\n      .filter(mark => {\n        if (!type) {\n          return true\n        }\n\n        return type.name === mark.type.name\n      })\n      .find(mark => objectIncludes(mark.attrs, attributes, { strict: false }))\n  }\n\n  let selectionRange = 0\n  const markRanges: MarkRange[] = []\n\n  ranges.forEach(({ $from, $to }) => {\n    const from = $from.pos\n    const to = $to.pos\n\n    state.doc.nodesBetween(from, to, (node, pos) => {\n      if (!node.isText && !node.marks.length) {\n        return\n      }\n\n      const relativeFrom = Math.max(from, pos)\n      const relativeTo = Math.min(to, pos + node.nodeSize)\n      const range = relativeTo - relativeFrom\n\n      selectionRange += range\n\n      markRanges.push(\n        ...node.marks.map(mark => ({\n          mark,\n          from: relativeFrom,\n          to: relativeTo,\n        })),\n      )\n    })\n  })\n\n  if (selectionRange === 0) {\n    return false\n  }\n\n  // calculate range of matched mark\n  const matchedRange = markRanges\n    .filter(markRange => {\n      if (!type) {\n        return true\n      }\n\n      return type.name === markRange.mark.type.name\n    })\n    .filter(markRange => objectIncludes(markRange.mark.attrs, attributes, { strict: false }))\n    .reduce((sum, markRange) => sum + markRange.to - markRange.from, 0)\n\n  // calculate range of marks that excludes the searched mark\n  // for example `code` doesn’t allow any other marks\n  const excludedRange = markRanges\n    .filter(markRange => {\n      if (!type) {\n        return true\n      }\n\n      return markRange.mark.type !== type && markRange.mark.type.excludes(type)\n    })\n    .reduce((sum, markRange) => sum + markRange.to - markRange.from, 0)\n\n  // we only include the result of `excludedRange`\n  // if there is a match at all\n  const range = matchedRange > 0 ? matchedRange + excludedRange : matchedRange\n\n  return range >= selectionRange\n}\n", "import { EditorState } from '@tiptap/pm/state'\n\nimport { getSchemaTypeNameByName } from './getSchemaTypeNameByName.js'\nimport { isMarkActive } from './isMarkActive.js'\nimport { isNodeActive } from './isNodeActive.js'\n\nexport function isActive(\n  state: EditorState,\n  name: string | null,\n  attributes: Record<string, any> = {},\n): boolean {\n  if (!name) {\n    return isNodeActive(state, null, attributes) || isMarkActive(state, null, attributes)\n  }\n\n  const schemaType = getSchemaTypeNameByName(name, state.schema)\n\n  if (schemaType === 'node') {\n    return isNodeActive(state, name, attributes)\n  }\n\n  if (schemaType === 'mark') {\n    return isMarkActive(state, name, attributes)\n  }\n\n  return false\n}\n", "import { EditorState } from '@tiptap/pm/state'\n\nimport { findParentNode } from './findParentNode.js'\n\nexport const isAtEndOfNode = (state: EditorState, nodeType?: string) => {\n  const { $from, $to, $anchor } = state.selection\n\n  if (nodeType) {\n    const parentNode = findParentNode(node => node.type.name === nodeType)(state.selection)\n\n    if (!parentNode) {\n      return false\n    }\n\n    const $parentPos = state.doc.resolve(parentNode.pos + 1)\n\n    if ($anchor.pos + 1 === $parentPos.end()) {\n      return true\n    }\n\n    return false\n  }\n\n  if ($to.parentOffset < $to.parent.nodeSize - 2 || $from.pos !== $to.pos) {\n    return false\n  }\n\n  return true\n}\n", "import { EditorState } from '@tiptap/pm/state'\n\nexport const isAtStartOfNode = (state: EditorState) => {\n  const { $from, $to } = state.selection\n\n  if ($from.parentOffset > 0 || $from.pos !== $to.pos) {\n    return false\n  }\n\n  return true\n}\n", "import { getExtensionField } from '../helpers/getExtensionField.js'\nimport { NodeConfig } from '../index.js'\nimport { Extensions } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\nimport { splitExtensions } from './splitExtensions.js'\n\nexport function isList(name: string, extensions: Extensions): boolean {\n  const { nodeExtensions } = splitExtensions(extensions)\n  const extension = nodeExtensions.find(item => item.name === name)\n\n  if (!extension) {\n    return false\n  }\n\n  const context = {\n    name: extension.name,\n    options: extension.options,\n    storage: extension.storage,\n  }\n  const group = callOrReturn(getExtensionField<NodeConfig['group']>(extension, 'group', context))\n\n  if (typeof group !== 'string') {\n    return false\n  }\n\n  return group.split(' ').includes('list')\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\n/**\n * Returns true if the given prosemirror node is empty.\n */\nexport function isNodeEmpty(\n  node: ProseMirrorNode,\n  {\n    checkChildren = true,\n    ignoreWhitespace = false,\n  }: {\n    /**\n     * When true (default), it will also check if all children are empty.\n     */\n    checkChildren?: boolean;\n    /**\n     * When true, it will ignore whitespace when checking for emptiness.\n     */\n    ignoreWhitespace?: boolean;\n  } = {},\n): boolean {\n  if (ignoreWhitespace) {\n    if (node.type.name === 'hardBreak') {\n      // Hard breaks are considered empty\n      return true\n    }\n    if (node.isText) {\n      return /^\\s*$/m.test(node.text ?? '')\n    }\n  }\n\n  if (node.isText) {\n    return !node.text\n  }\n\n  if (node.isAtom || node.isLeaf) {\n    return false\n  }\n\n  if (node.content.childCount === 0) {\n    return true\n  }\n\n  if (checkChildren) {\n    let isContentEmpty = true\n\n    node.content.forEach(childNode => {\n      if (isContentEmpty === false) {\n        // Exit early for perf\n        return\n      }\n\n      if (!isNodeEmpty(childNode, { ignoreWhitespace, checkChildren })) {\n        isContentEmpty = false\n      }\n    })\n\n    return isContentEmpty\n  }\n\n  return false\n}\n", "import { NodeSelection } from '@tiptap/pm/state'\n\nexport function isNodeSelection(value: unknown): value is NodeSelection {\n  return value instanceof NodeSelection\n}\n", "import { EditorView } from '@tiptap/pm/view'\n\nimport { minMax } from '../utilities/minMax.js'\n\nexport function posToDOMRect(view: EditorView, from: number, to: number): DOMRect {\n  const minPos = 0\n  const maxPos = view.state.doc.content.size\n  const resolvedFrom = minMax(from, minPos, maxPos)\n  const resolvedEnd = minMax(to, minPos, maxPos)\n  const start = view.coordsAtPos(resolvedFrom)\n  const end = view.coordsAtPos(resolvedEnd, -1)\n  const top = Math.min(start.top, end.top)\n  const bottom = Math.max(start.bottom, end.bottom)\n  const left = Math.min(start.left, end.left)\n  const right = Math.max(start.right, end.right)\n  const width = right - left\n  const height = bottom - top\n  const x = left\n  const y = top\n  const data = {\n    top,\n    bottom,\n    left,\n    right,\n    width,\n    height,\n    x,\n    y,\n  }\n\n  return {\n    ...data,\n    toJSON: () => data,\n  }\n}\n", "import type { Schema } from '@tiptap/pm/model'\n\nimport type { JSONContent } from '../types.js'\n\ntype RewriteUnknownContentOptions = {\n  /**\n   * If true, unknown nodes will be treated as paragraphs\n   * @default true\n   */\n  fallbackToParagraph?: boolean;\n};\n\ntype RewrittenContent = {\n  /**\n   * The original JSON content that was rewritten\n   */\n  original: JSONContent;\n  /**\n   * The name of the node or mark that was unsupported\n   */\n  unsupported: string;\n}[];\n\n/**\n * The actual implementation of the rewriteUnknownContent function\n */\nfunction rewriteUnknownContentInner({\n  json,\n  validMarks,\n  validNodes,\n  options,\n  rewrittenContent = [],\n}: {\n  json: JSONContent;\n  validMarks: Set<string>;\n  validNodes: Set<string>;\n  options?: RewriteUnknownContentOptions;\n  rewrittenContent?: RewrittenContent;\n}): {\n  /**\n   * The cleaned JSON content\n   */\n  json: JSONContent | null;\n  /**\n   * The array of nodes and marks that were rewritten\n   */\n  rewrittenContent: RewrittenContent;\n} {\n  if (json.marks && Array.isArray(json.marks)) {\n    json.marks = json.marks.filter(mark => {\n      const name = typeof mark === 'string' ? mark : mark.type\n\n      if (validMarks.has(name)) {\n        return true\n      }\n\n      rewrittenContent.push({\n        original: JSON.parse(JSON.stringify(mark)),\n        unsupported: name,\n      })\n      // Just ignore any unknown marks\n      return false\n    })\n  }\n\n  if (json.content && Array.isArray(json.content)) {\n    json.content = json.content\n      .map(\n        value => rewriteUnknownContentInner({\n          json: value,\n          validMarks,\n          validNodes,\n          options,\n          rewrittenContent,\n        }).json,\n      )\n      .filter(a => a !== null && a !== undefined)\n  }\n\n  if (json.type && !validNodes.has(json.type)) {\n    rewrittenContent.push({\n      original: JSON.parse(JSON.stringify(json)),\n      unsupported: json.type,\n    })\n\n    if (json.content && Array.isArray(json.content) && (options?.fallbackToParagraph !== false)) {\n      // Just treat it like a paragraph and hope for the best\n      json.type = 'paragraph'\n\n      return {\n        json,\n        rewrittenContent,\n      }\n    }\n\n    // or just omit it entirely\n    return {\n      json: null,\n      rewrittenContent,\n    }\n  }\n\n  return { json, rewrittenContent }\n}\n\n/**\n * Rewrite unknown nodes and marks within JSON content\n * Allowing for user within the editor\n */\nexport function rewriteUnknownContent(\n  /**\n   * The JSON content to clean of unknown nodes and marks\n   */\n  json: JSONContent,\n  /**\n   * The schema to use for validation\n   */\n  schema: Schema,\n  /**\n   * Options for the cleaning process\n   */\n  options?: RewriteUnknownContentOptions,\n): {\n  /**\n   * The cleaned JSON content\n   */\n  json: JSONContent | null;\n  /**\n   * The array of nodes and marks that were rewritten\n   */\n  rewrittenContent: {\n    /**\n     * The original JSON content that was rewritten\n     */\n    original: JSONContent;\n    /**\n     * The name of the node or mark that was unsupported\n     */\n    unsupported: string;\n  }[];\n} {\n  return rewriteUnknownContentInner({\n    json,\n    validNodes: new Set(Object.keys(schema.nodes)),\n    validMarks: new Set(Object.keys(schema.marks)),\n    options,\n  })\n}\n", "import { MarkType, ResolvedPos } from '@tiptap/pm/model'\nimport { EditorState, Transaction } from '@tiptap/pm/state'\n\nimport { getMarkAttributes } from '../helpers/getMarkAttributes.js'\nimport { getMarkType } from '../helpers/getMarkType.js'\nimport { isTextSelection } from '../helpers/index.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    setMark: {\n      /**\n       * Add a mark with new attributes.\n       * @param typeOrName The mark type or name.\n       * @example editor.commands.setMark('bold', { level: 1 })\n       */\n      setMark: (typeOrName: string | MarkType, attributes?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nfunction canSetMark(state: EditorState, tr: Transaction, newMarkType: MarkType) {\n  const { selection } = tr\n  let cursor: ResolvedPos | null = null\n\n  if (isTextSelection(selection)) {\n    cursor = selection.$cursor\n  }\n\n  if (cursor) {\n    const currentMarks = state.storedMarks ?? cursor.marks()\n\n    // There can be no current marks that exclude the new mark\n    return (\n      !!newMarkType.isInSet(currentMarks)\n      || !currentMarks.some(mark => mark.type.excludes(newMarkType))\n    )\n  }\n\n  const { ranges } = selection\n\n  return ranges.some(({ $from, $to }) => {\n    let someNodeSupportsMark = $from.depth === 0\n      ? state.doc.inlineContent && state.doc.type.allowsMarkType(newMarkType)\n      : false\n\n    state.doc.nodesBetween($from.pos, $to.pos, (node, _pos, parent) => {\n      // If we already found a mark that we can enable, return false to bypass the remaining search\n      if (someNodeSupportsMark) {\n        return false\n      }\n\n      if (node.isInline) {\n        const parentAllowsMarkType = !parent || parent.type.allowsMarkType(newMarkType)\n        const currentMarksAllowMarkType = !!newMarkType.isInSet(node.marks)\n          || !node.marks.some(otherMark => otherMark.type.excludes(newMarkType))\n\n        someNodeSupportsMark = parentAllowsMarkType && currentMarksAllowMarkType\n      }\n      return !someNodeSupportsMark\n    })\n\n    return someNodeSupportsMark\n  })\n}\nexport const setMark: RawCommands['setMark'] = (typeOrName, attributes = {}) => ({ tr, state, dispatch }) => {\n  const { selection } = tr\n  const { empty, ranges } = selection\n  const type = getMarkType(typeOrName, state.schema)\n\n  if (dispatch) {\n    if (empty) {\n      const oldAttributes = getMarkAttributes(state, type)\n\n      tr.addStoredMark(\n        type.create({\n          ...oldAttributes,\n          ...attributes,\n        }),\n      )\n    } else {\n      ranges.forEach(range => {\n        const from = range.$from.pos\n        const to = range.$to.pos\n\n        state.doc.nodesBetween(from, to, (node, pos) => {\n          const trimmedFrom = Math.max(pos, from)\n          const trimmedTo = Math.min(pos + node.nodeSize, to)\n          const someHasMark = node.marks.find(mark => mark.type === type)\n\n          // if there is already a mark of this type\n          // we know that we have to merge its attributes\n          // otherwise we add a fresh new mark\n          if (someHasMark) {\n            node.marks.forEach(mark => {\n              if (type === mark.type) {\n                tr.addMark(\n                  trimmedFrom,\n                  trimmedTo,\n                  type.create({\n                    ...mark.attrs,\n                    ...attributes,\n                  }),\n                )\n              }\n            })\n          } else {\n            tr.addMark(trimmedFrom, trimmedTo, type.create(attributes))\n          }\n        })\n      })\n    }\n  }\n\n  return canSetMark(state, tr, type)\n}\n", "import type { Plug<PERSON>, Plugin<PERSON><PERSON> } from '@tiptap/pm/state'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    setMeta: {\n      /**\n       * Store a metadata property in the current transaction.\n       * @param key The key of the metadata property.\n       * @param value The value to store.\n       * @example editor.commands.setMeta('foo', 'bar')\n       */\n      setMeta: (key: string | Plugin | PluginKey, value: any) => ReturnType,\n    }\n  }\n}\n\nexport const setMeta: RawCommands['setMeta'] = (key, value) => ({ tr }) => {\n  tr.setMeta(key, value)\n\n  return true\n}\n", "import { setBlockType } from '@tiptap/pm/commands'\nimport { NodeType } from '@tiptap/pm/model'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    setNode: {\n      /**\n       * Replace a given range with a node.\n       * @param typeOrName The type or name of the node\n       * @param attributes The attributes of the node\n       * @example editor.commands.setNode('paragraph')\n       */\n      setNode: (typeOrName: string | NodeType, attributes?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nexport const setNode: RawCommands['setNode'] = (typeOrName, attributes = {}) => ({ state, dispatch, chain }) => {\n  const type = getNodeType(typeOrName, state.schema)\n\n  let attributesToCopy: Record<string, any> | undefined\n\n  if (state.selection.$anchor.sameParent(state.selection.$head)) {\n    // only copy attributes if the selection is pointing to a node of the same type\n    attributesToCopy = state.selection.$anchor.parent.attrs\n  }\n\n  // TODO: use a fallback like insertContent?\n  if (!type.isTextblock) {\n    console.warn('[tiptap warn]: Currently \"setNode()\" only supports text block nodes.')\n\n    return false\n  }\n\n  return (\n    chain()\n    // try to convert node to default node if needed\n      .command(({ commands }) => {\n        const canSetBlock = setBlockType(type, { ...attributesToCopy, ...attributes })(state)\n\n        if (canSetBlock) {\n          return true\n        }\n\n        return commands.clearNodes()\n      })\n      .command(({ state: updatedState }) => {\n        return setBlockType(type, { ...attributesToCopy, ...attributes })(updatedState, dispatch)\n      })\n      .run()\n  )\n}\n", "import { NodeSelection } from '@tiptap/pm/state'\n\nimport { RawCommands } from '../types.js'\nimport { minMax } from '../utilities/minMax.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    setNodeSelection: {\n      /**\n       * Creates a NodeSelection.\n       * @param position - Position of the node.\n       * @example editor.commands.setNodeSelection(10)\n       */\n      setNodeSelection: (position: number) => ReturnType\n    }\n  }\n}\n\nexport const setNodeSelection: RawCommands['setNodeSelection'] = position => ({ tr, dispatch }) => {\n  if (dispatch) {\n    const { doc } = tr\n    const from = minMax(position, 0, doc.content.size)\n    const selection = NodeSelection.create(doc, from)\n\n    tr.setSelection(selection)\n  }\n\n  return true\n}\n", "import { TextSelection } from '@tiptap/pm/state'\n\nimport { Range, RawCommands } from '../types.js'\nimport { minMax } from '../utilities/minMax.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    setTextSelection: {\n      /**\n       * Creates a TextSelection.\n       * @param position The position of the selection.\n       * @example editor.commands.setTextSelection(10)\n       */\n      setTextSelection: (position: number | Range) => ReturnType\n    }\n  }\n}\n\nexport const setTextSelection: RawCommands['setTextSelection'] = position => ({ tr, dispatch }) => {\n  if (dispatch) {\n    const { doc } = tr\n    const { from, to } = typeof position === 'number' ? { from: position, to: position } : position\n    const minPos = TextSelection.atStart(doc).from\n    const maxPos = TextSelection.atEnd(doc).to\n    const resolvedFrom = minMax(from, minPos, maxPos)\n    const resolvedEnd = minMax(to, minPos, maxPos)\n    const selection = TextSelection.create(doc, resolvedFrom, resolvedEnd)\n\n    tr.setSelection(selection)\n  }\n\n  return true\n}\n", "import { NodeType } from '@tiptap/pm/model'\nimport { sinkListItem as originalSinkListItem } from '@tiptap/pm/schema-list'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    sinkListItem: {\n      /**\n       * Sink the list item down into an inner list.\n       * @param typeOrName The type or name of the node.\n       * @example editor.commands.sinkListItem('listItem')\n       */\n      sinkListItem: (typeOrName: string | NodeType) => ReturnType\n    }\n  }\n}\n\nexport const sinkListItem: RawCommands['sinkListItem'] = typeOrName => ({ state, dispatch }) => {\n  const type = getNodeType(typeOrName, state.schema)\n\n  return originalSinkListItem(type)(state, dispatch)\n}\n", "import { EditorState, NodeSelection, TextSelection } from '@tiptap/pm/state'\nimport { canSplit } from '@tiptap/pm/transform'\n\nimport { defaultBlockAt } from '../helpers/defaultBlockAt.js'\nimport { getSplittedAttributes } from '../helpers/getSplittedAttributes.js'\nimport { RawCommands } from '../types.js'\n\nfunction ensureMarks(state: EditorState, splittableMarks?: string[]) {\n  const marks = state.storedMarks || (state.selection.$to.parentOffset && state.selection.$from.marks())\n\n  if (marks) {\n    const filteredMarks = marks.filter(mark => splittableMarks?.includes(mark.type.name))\n\n    state.tr.ensureMarks(filteredMarks)\n  }\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    splitBlock: {\n      /**\n       * Forks a new node from an existing node.\n       * @param options.keepMarks Keep marks from the previous node.\n       * @example editor.commands.splitBlock()\n       * @example editor.commands.splitBlock({ keepMarks: true })\n       */\n      splitBlock: (options?: { keepMarks?: boolean }) => ReturnType\n    }\n  }\n}\n\nexport const splitBlock: RawCommands['splitBlock'] = ({ keepMarks = true } = {}) => ({\n  tr, state, dispatch, editor,\n}) => {\n  const { selection, doc } = tr\n  const { $from, $to } = selection\n  const extensionAttributes = editor.extensionManager.attributes\n  const newAttributes = getSplittedAttributes(\n    extensionAttributes,\n    $from.node().type.name,\n    $from.node().attrs,\n  )\n\n  if (selection instanceof NodeSelection && selection.node.isBlock) {\n    if (!$from.parentOffset || !canSplit(doc, $from.pos)) {\n      return false\n    }\n\n    if (dispatch) {\n      if (keepMarks) {\n        ensureMarks(state, editor.extensionManager.splittableMarks)\n      }\n\n      tr.split($from.pos).scrollIntoView()\n    }\n\n    return true\n  }\n\n  if (!$from.parent.isBlock) {\n    return false\n  }\n\n  const atEnd = $to.parentOffset === $to.parent.content.size\n\n  const deflt = $from.depth === 0\n    ? undefined\n    : defaultBlockAt($from.node(-1).contentMatchAt($from.indexAfter(-1)))\n\n  let types = atEnd && deflt\n    ? [\n      {\n        type: deflt,\n        attrs: newAttributes,\n      },\n    ]\n    : undefined\n\n  let can = canSplit(tr.doc, tr.mapping.map($from.pos), 1, types)\n\n  if (\n    !types\n      && !can\n      && canSplit(tr.doc, tr.mapping.map($from.pos), 1, deflt ? [{ type: deflt }] : undefined)\n  ) {\n    can = true\n    types = deflt\n      ? [\n        {\n          type: deflt,\n          attrs: newAttributes,\n        },\n      ]\n      : undefined\n  }\n\n  if (dispatch) {\n    if (can) {\n      if (selection instanceof TextSelection) {\n        tr.deleteSelection()\n      }\n\n      tr.split(tr.mapping.map($from.pos), 1, types)\n\n      if (deflt && !atEnd && !$from.parentOffset && $from.parent.type !== deflt) {\n        const first = tr.mapping.map($from.before())\n        const $first = tr.doc.resolve(first)\n\n        if ($from.node(-1).canReplaceWith($first.index(), $first.index() + 1, deflt)) {\n          tr.setNodeMarkup(tr.mapping.map($from.before()), deflt)\n        }\n      }\n    }\n\n    if (keepMarks) {\n      ensureMarks(state, editor.extensionManager.splittableMarks)\n    }\n\n    tr.scrollIntoView()\n  }\n\n  return can\n}\n", "import {\n  Fragment, Node as ProseMirrorNode, NodeType, Slice,\n} from '@tiptap/pm/model'\nimport { TextSelection } from '@tiptap/pm/state'\nimport { canSplit } from '@tiptap/pm/transform'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { getSplittedAttributes } from '../helpers/getSplittedAttributes.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    splitListItem: {\n      /**\n       * Splits one list item into two list items.\n       * @param typeOrName The type or name of the node.\n       * @param overrideAttrs The attributes to ensure on the new node.\n       * @example editor.commands.splitListItem('listItem')\n       */\n      splitListItem: (typeOrName: string | NodeType, overrideAttrs?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nexport const splitListItem: RawCommands['splitListItem'] = (typeOrName, overrideAttrs = {}) => ({\n  tr, state, dispatch, editor,\n}) => {\n  const type = getNodeType(typeOrName, state.schema)\n  const { $from, $to } = state.selection\n\n  // @ts-ignore\n  // eslint-disable-next-line\n    const node: ProseMirrorNode = state.selection.node\n\n  if ((node && node.isBlock) || $from.depth < 2 || !$from.sameParent($to)) {\n    return false\n  }\n\n  const grandParent = $from.node(-1)\n\n  if (grandParent.type !== type) {\n    return false\n  }\n\n  const extensionAttributes = editor.extensionManager.attributes\n\n  if ($from.parent.content.size === 0 && $from.node(-1).childCount === $from.indexAfter(-1)) {\n    // In an empty block. If this is a nested list, the wrapping\n    // list item should be split. Otherwise, bail out and let next\n    // command handle lifting.\n    if (\n      $from.depth === 2\n        || $from.node(-3).type !== type\n        || $from.index(-2) !== $from.node(-2).childCount - 1\n    ) {\n      return false\n    }\n\n    if (dispatch) {\n      let wrap = Fragment.empty\n      // eslint-disable-next-line\n        const depthBefore = $from.index(-1) ? 1 : $from.index(-2) ? 2 : 3\n\n      // Build a fragment containing empty versions of the structure\n      // from the outer list item to the parent node of the cursor\n      for (let d = $from.depth - depthBefore; d >= $from.depth - 3; d -= 1) {\n        wrap = Fragment.from($from.node(d).copy(wrap))\n      }\n\n      // eslint-disable-next-line\n        const depthAfter = $from.indexAfter(-1) < $from.node(-2).childCount ? 1 : $from.indexAfter(-2) < $from.node(-3).childCount ? 2 : 3\n\n      // Add a second list item with an empty default start node\n      const newNextTypeAttributes = {\n        ...getSplittedAttributes(\n          extensionAttributes,\n          $from.node().type.name,\n          $from.node().attrs,\n        ),\n        ...overrideAttrs,\n      }\n      const nextType = type.contentMatch.defaultType?.createAndFill(newNextTypeAttributes) || undefined\n\n      wrap = wrap.append(Fragment.from(type.createAndFill(null, nextType) || undefined))\n\n      const start = $from.before($from.depth - (depthBefore - 1))\n\n      tr.replace(start, $from.after(-depthAfter), new Slice(wrap, 4 - depthBefore, 0))\n\n      let sel = -1\n\n      tr.doc.nodesBetween(start, tr.doc.content.size, (n, pos) => {\n        if (sel > -1) {\n          return false\n        }\n\n        if (n.isTextblock && n.content.size === 0) {\n          sel = pos + 1\n        }\n      })\n\n      if (sel > -1) {\n        tr.setSelection(TextSelection.near(tr.doc.resolve(sel)))\n      }\n\n      tr.scrollIntoView()\n    }\n\n    return true\n  }\n\n  const nextType = $to.pos === $from.end() ? grandParent.contentMatchAt(0).defaultType : null\n\n  const newTypeAttributes = {\n    ...getSplittedAttributes(\n      extensionAttributes,\n      grandParent.type.name,\n      grandParent.attrs,\n    ),\n    ...overrideAttrs,\n  }\n  const newNextTypeAttributes = {\n    ...getSplittedAttributes(\n      extensionAttributes,\n      $from.node().type.name,\n      $from.node().attrs,\n    ),\n    ...overrideAttrs,\n  }\n\n  tr.delete($from.pos, $to.pos)\n\n  const types = nextType\n    ? [\n      { type, attrs: newTypeAttributes },\n      { type: nextType, attrs: newNextTypeAttributes },\n    ]\n    : [{ type, attrs: newTypeAttributes }]\n\n  if (!canSplit(tr.doc, $from.pos, 2)) {\n    return false\n  }\n\n  if (dispatch) {\n    const { selection, storedMarks } = state\n    const { splittableMarks } = editor.extensionManager\n    const marks = storedMarks || (selection.$to.parentOffset && selection.$from.marks())\n\n    tr.split($from.pos, 2, types).scrollIntoView()\n\n    if (!marks || !dispatch) {\n      return true\n    }\n\n    const filteredMarks = marks.filter(mark => splittableMarks.includes(mark.type.name))\n\n    tr.ensureMarks(filteredMarks)\n  }\n\n  return true\n}\n", "import { NodeType } from '@tiptap/pm/model'\nimport { Transaction } from '@tiptap/pm/state'\nimport { canJoin } from '@tiptap/pm/transform'\n\nimport { findParentNode } from '../helpers/findParentNode.js'\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { isList } from '../helpers/isList.js'\nimport { RawCommands } from '../types.js'\n\nconst joinListBackwards = (tr: Transaction, listType: NodeType): boolean => {\n  const list = findParentNode(node => node.type === listType)(tr.selection)\n\n  if (!list) {\n    return true\n  }\n\n  const before = tr.doc.resolve(Math.max(0, list.pos - 1)).before(list.depth)\n\n  if (before === undefined) {\n    return true\n  }\n\n  const nodeBefore = tr.doc.nodeAt(before)\n  const canJoinBackwards = list.node.type === nodeBefore?.type && canJoin(tr.doc, list.pos)\n\n  if (!canJoinBackwards) {\n    return true\n  }\n\n  tr.join(list.pos)\n\n  return true\n}\n\nconst joinListForwards = (tr: Transaction, listType: NodeType): boolean => {\n  const list = findParentNode(node => node.type === listType)(tr.selection)\n\n  if (!list) {\n    return true\n  }\n\n  const after = tr.doc.resolve(list.start).after(list.depth)\n\n  if (after === undefined) {\n    return true\n  }\n\n  const nodeAfter = tr.doc.nodeAt(after)\n  const canJoinForwards = list.node.type === nodeAfter?.type && canJoin(tr.doc, after)\n\n  if (!canJoinForwards) {\n    return true\n  }\n\n  tr.join(after)\n\n  return true\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    toggleList: {\n      /**\n       * Toggle between different list types.\n       * @param listTypeOrName The type or name of the list.\n       * @param itemTypeOrName The type or name of the list item.\n       * @param keepMarks Keep marks when toggling.\n       * @param attributes Attributes for the new list.\n       * @example editor.commands.toggleList('bulletList', 'listItem')\n       */\n      toggleList: (listTypeOrName: string | NodeType, itemTypeOrName: string | NodeType, keepMarks?: boolean, attributes?: Record<string, any>) => ReturnType;\n    }\n  }\n}\n\nexport const toggleList: RawCommands['toggleList'] = (listTypeOrName, itemTypeOrName, keepMarks, attributes = {}) => ({\n  editor, tr, state, dispatch, chain, commands, can,\n}) => {\n  const { extensions, splittableMarks } = editor.extensionManager\n  const listType = getNodeType(listTypeOrName, state.schema)\n  const itemType = getNodeType(itemTypeOrName, state.schema)\n  const { selection, storedMarks } = state\n  const { $from, $to } = selection\n  const range = $from.blockRange($to)\n\n  const marks = storedMarks || (selection.$to.parentOffset && selection.$from.marks())\n\n  if (!range) {\n    return false\n  }\n\n  const parentList = findParentNode(node => isList(node.type.name, extensions))(selection)\n\n  if (range.depth >= 1 && parentList && range.depth - parentList.depth <= 1) {\n    // remove list\n    if (parentList.node.type === listType) {\n      return commands.liftListItem(itemType)\n    }\n\n    // change list type\n    if (\n      isList(parentList.node.type.name, extensions)\n        && listType.validContent(parentList.node.content)\n        && dispatch\n    ) {\n      return chain()\n        .command(() => {\n          tr.setNodeMarkup(parentList.pos, listType)\n\n          return true\n        })\n        .command(() => joinListBackwards(tr, listType))\n        .command(() => joinListForwards(tr, listType))\n        .run()\n    }\n  }\n  if (!keepMarks || !marks || !dispatch) {\n\n    return chain()\n      // try to convert node to default node if needed\n      .command(() => {\n        const canWrapInList = can().wrapInList(listType, attributes)\n\n        if (canWrapInList) {\n          return true\n        }\n\n        return commands.clearNodes()\n      })\n      .wrapInList(listType, attributes)\n      .command(() => joinListBackwards(tr, listType))\n      .command(() => joinListForwards(tr, listType))\n      .run()\n  }\n\n  return (\n    chain()\n    // try to convert node to default node if needed\n      .command(() => {\n        const canWrapInList = can().wrapInList(listType, attributes)\n\n        const filteredMarks = marks.filter(mark => splittableMarks.includes(mark.type.name))\n\n        tr.ensureMarks(filteredMarks)\n\n        if (canWrapInList) {\n          return true\n        }\n\n        return commands.clearNodes()\n      })\n      .wrapInList(listType, attributes)\n      .command(() => joinListBackwards(tr, listType))\n      .command(() => joinListForwards(tr, listType))\n      .run()\n  )\n}\n", "import { MarkType } from '@tiptap/pm/model'\n\nimport { getMarkType } from '../helpers/getMarkType.js'\nimport { isMarkActive } from '../helpers/isMarkActive.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    toggleMark: {\n      /**\n       * Toggle a mark on and off.\n       * @param typeOrName The mark type or name.\n       * @param attributes The attributes of the mark.\n       * @param options.extendEmptyMarkRange Removes the mark even across the current selection. Defaults to `false`.\n       * @example editor.commands.toggleMark('bold')\n       */\n      toggleMark: (\n        /**\n         * The mark type or name.\n         */\n        typeOrName: string | MarkType,\n\n        /**\n         * The attributes of the mark.\n         */\n        attributes?: Record<string, any>,\n\n        options?: {\n          /**\n           * Removes the mark even across the current selection. Defaults to `false`.\n           */\n          extendEmptyMarkRange?: boolean\n        },\n      ) => ReturnType\n    }\n  }\n}\n\nexport const toggleMark: RawCommands['toggleMark'] = (typeOrName, attributes = {}, options = {}) => ({ state, commands }) => {\n  const { extendEmptyMarkRange = false } = options\n  const type = getMarkType(typeOrName, state.schema)\n  const isActive = isMarkActive(state, type, attributes)\n\n  if (isActive) {\n    return commands.unsetMark(type, { extendEmptyMarkRange })\n  }\n\n  return commands.setMark(type, attributes)\n}\n", "import { NodeType } from '@tiptap/pm/model'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { isNodeActive } from '../helpers/isNodeActive.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    toggleNode: {\n      /**\n       * Toggle a node with another node.\n       * @param typeOrName The type or name of the node.\n       * @param toggleTypeOrName The type or name of the node to toggle.\n       * @param attributes The attributes of the node.\n       * @example editor.commands.toggleNode('heading', 'paragraph')\n       */\n      toggleNode: (\n        typeOrName: string | NodeType,\n        toggleTypeOrName: string | NodeType,\n        attributes?: Record<string, any>,\n      ) => ReturnType\n    }\n  }\n}\n\nexport const toggleNode: RawCommands['toggleNode'] = (typeOrName, toggleTypeOrName, attributes = {}) => ({ state, commands }) => {\n  const type = getNodeType(typeOrName, state.schema)\n  const toggleType = getNodeType(toggleTypeOrName, state.schema)\n  const isActive = isNodeActive(state, type, attributes)\n\n  let attributesToCopy: Record<string, any> | undefined\n\n  if (state.selection.$anchor.sameParent(state.selection.$head)) {\n    // only copy attributes if the selection is pointing to a node of the same type\n    attributesToCopy = state.selection.$anchor.parent.attrs\n  }\n\n  if (isActive) {\n    return commands.setNode(toggleType, attributesToCopy)\n  }\n\n  // If the node is not active, we want to set the new node type with the given attributes\n  // Copying over the attributes from the current node if the selection is pointing to a node of the same type\n  return commands.setNode(type, { ...attributesToCopy, ...attributes })\n}\n", "import { NodeType } from '@tiptap/pm/model'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { isNodeActive } from '../helpers/isNodeActive.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    toggleWrap: {\n      /**\n       * Wraps nodes in another node, or removes an existing wrap.\n       * @param typeOrName The type or name of the node.\n       * @param attributes The attributes of the node.\n       * @example editor.commands.toggleWrap('blockquote')\n       */\n      toggleWrap: (typeOrName: string | NodeType, attributes?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nexport const toggleWrap: RawCommands['toggleWrap'] = (typeOrName, attributes = {}) => ({ state, commands }) => {\n  const type = getNodeType(typeOrName, state.schema)\n  const isActive = isNodeActive(state, type, attributes)\n\n  if (isActive) {\n    return commands.lift(type)\n  }\n\n  return commands.wrapIn(type, attributes)\n}\n", "import { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    undoInputRule: {\n      /**\n       * Undo an input rule.\n       * @example editor.commands.undoInputRule()\n       */\n      undoInputRule: () => ReturnType,\n    }\n  }\n}\n\nexport const undoInputRule: RawCommands['undoInputRule'] = () => ({ state, dispatch }) => {\n  const plugins = state.plugins\n\n  for (let i = 0; i < plugins.length; i += 1) {\n    const plugin = plugins[i]\n    let undoable\n\n    // @ts-ignore\n    // eslint-disable-next-line\n    if (plugin.spec.isInputRules && (undoable = plugin.getState(state))) {\n      if (dispatch) {\n        const tr = state.tr\n        const toUndo = undoable.transform\n\n        for (let j = toUndo.steps.length - 1; j >= 0; j -= 1) {\n          tr.step(toUndo.steps[j].invert(toUndo.docs[j]))\n        }\n\n        if (undoable.text) {\n          const marks = tr.doc.resolve(undoable.from).marks()\n\n          tr.replaceWith(undoable.from, undoable.to, state.schema.text(undoable.text, marks))\n        } else {\n          tr.delete(undoable.from, undoable.to)\n        }\n      }\n\n      return true\n    }\n  }\n\n  return false\n}\n", "import { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    unsetAllMarks: {\n      /**\n       * Remove all marks in the current selection.\n       * @example editor.commands.unsetAllMarks()\n       */\n      unsetAllMarks: () => ReturnType,\n    }\n  }\n}\n\nexport const unsetAllMarks: RawCommands['unsetAllMarks'] = () => ({ tr, dispatch }) => {\n  const { selection } = tr\n  const { empty, ranges } = selection\n\n  if (empty) {\n    return true\n  }\n\n  if (dispatch) {\n    ranges.forEach(range => {\n      tr.removeMark(range.$from.pos, range.$to.pos)\n    })\n  }\n\n  return true\n}\n", "import { MarkType } from '@tiptap/pm/model'\n\nimport { getMarkRange } from '../helpers/getMarkRange.js'\nimport { getMarkType } from '../helpers/getMarkType.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    unsetMark: {\n      /**\n       * Remove all marks in the current selection.\n       * @param typeOrName The mark type or name.\n       * @param options.extendEmptyMarkRange Removes the mark even across the current selection. Defaults to `false`.\n       * @example editor.commands.unsetMark('bold')\n       */\n      unsetMark: (\n        /**\n         * The mark type or name.\n         */\n        typeOrName: string | MarkType,\n\n        options?: {\n          /**\n           * Removes the mark even across the current selection. Defaults to `false`.\n           */\n          extendEmptyMarkRange?: boolean\n        },\n      ) => ReturnType\n    }\n  }\n}\n\nexport const unsetMark: RawCommands['unsetMark'] = (typeOrName, options = {}) => ({ tr, state, dispatch }) => {\n  const { extendEmptyMarkRange = false } = options\n  const { selection } = tr\n  const type = getMarkType(typeOrName, state.schema)\n  const { $from, empty, ranges } = selection\n\n  if (!dispatch) {\n    return true\n  }\n\n  if (empty && extendEmptyMarkRange) {\n    let { from, to } = selection\n    const attrs = $from.marks().find(mark => mark.type === type)?.attrs\n    const range = getMarkRange($from, type, attrs)\n\n    if (range) {\n      from = range.from\n      to = range.to\n    }\n\n    tr.removeMark(from, to, type)\n  } else {\n    ranges.forEach(range => {\n      tr.removeMark(range.$from.pos, range.$to.pos, type)\n    })\n  }\n\n  tr.removeStoredMark(type)\n\n  return true\n}\n", "import {\n  Mark, MarkType, Node, NodeType,\n} from '@tiptap/pm/model'\nimport { SelectionRange } from '@tiptap/pm/state'\n\nimport { getMarkType } from '../helpers/getMarkType.js'\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { getSchemaTypeNameByName } from '../helpers/getSchemaTypeNameByName.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    updateAttributes: {\n      /**\n       * Update attributes of a node or mark.\n       * @param typeOrName The type or name of the node or mark.\n       * @param attributes The attributes of the node or mark.\n       * @example editor.commands.updateAttributes('mention', { userId: \"2\" })\n       */\n      updateAttributes: (\n        /**\n         * The type or name of the node or mark.\n         */\n        typeOrName: string | NodeType | MarkType,\n\n        /**\n         * The attributes of the node or mark.\n         */\n        attributes: Record<string, any>,\n      ) => ReturnType\n    }\n  }\n}\n\nexport const updateAttributes: RawCommands['updateAttributes'] = (typeOrName, attributes = {}) => ({ tr, state, dispatch }) => {\n\n  let nodeType: NodeType | null = null\n  let markType: MarkType | null = null\n\n  const schemaType = getSchemaTypeNameByName(\n    typeof typeOrName === 'string' ? typeOrName : typeOrName.name,\n    state.schema,\n  )\n\n  if (!schemaType) {\n    return false\n  }\n\n  if (schemaType === 'node') {\n    nodeType = getNodeType(typeOrName as NodeType, state.schema)\n  }\n\n  if (schemaType === 'mark') {\n    markType = getMarkType(typeOrName as MarkType, state.schema)\n  }\n\n  if (dispatch) {\n    tr.selection.ranges.forEach((range: SelectionRange) => {\n\n      const from = range.$from.pos\n      const to = range.$to.pos\n\n      let lastPos: number | undefined\n      let lastNode: Node | undefined\n      let trimmedFrom: number\n      let trimmedTo: number\n\n      if (tr.selection.empty) {\n        state.doc.nodesBetween(from, to, (node: Node, pos: number) => {\n\n          if (nodeType && nodeType === node.type) {\n            trimmedFrom = Math.max(pos, from)\n            trimmedTo = Math.min(pos + node.nodeSize, to)\n            lastPos = pos\n            lastNode = node\n          }\n        })\n      } else {\n        state.doc.nodesBetween(from, to, (node: Node, pos: number) => {\n\n          if (pos < from && nodeType && nodeType === node.type) {\n            trimmedFrom = Math.max(pos, from)\n            trimmedTo = Math.min(pos + node.nodeSize, to)\n            lastPos = pos\n            lastNode = node\n          }\n\n          if (pos >= from && pos <= to) {\n\n            if (nodeType && nodeType === node.type) {\n              tr.setNodeMarkup(pos, undefined, {\n                ...node.attrs,\n                ...attributes,\n              })\n            }\n\n            if (markType && node.marks.length) {\n              node.marks.forEach((mark: Mark) => {\n\n                if (markType === mark.type) {\n                  const trimmedFrom2 = Math.max(pos, from)\n                  const trimmedTo2 = Math.min(pos + node.nodeSize, to)\n\n                  tr.addMark(\n                    trimmedFrom2,\n                    trimmedTo2,\n                    markType.create({\n                      ...mark.attrs,\n                      ...attributes,\n                    }),\n                  )\n                }\n              })\n            }\n          }\n        })\n      }\n\n      if (lastNode) {\n\n        if (lastPos !== undefined) {\n          tr.setNodeMarkup(lastPos, undefined, {\n            ...lastNode.attrs,\n            ...attributes,\n          })\n        }\n\n        if (markType && lastNode.marks.length) {\n          lastNode.marks.forEach((mark: Mark) => {\n\n            if (markType === mark.type) {\n              tr.addMark(\n                trimmedFrom,\n                trimmedTo,\n                markType.create({\n                  ...mark.attrs,\n                  ...attributes,\n                }),\n              )\n            }\n          })\n        }\n      }\n    })\n  }\n\n  return true\n}\n", "import { wrapIn as originalWrapIn } from '@tiptap/pm/commands'\nimport { NodeType } from '@tiptap/pm/model'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    wrapIn: {\n      /**\n       * Wraps nodes in another node.\n       * @param typeOrName The type or name of the node.\n       * @param attributes The attributes of the node.\n       * @example editor.commands.wrapIn('blockquote')\n       */\n      wrapIn: (typeOrName: string | NodeType, attributes?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nexport const wrapIn: RawCommands['wrapIn'] = (typeOrName, attributes = {}) => ({ state, dispatch }) => {\n  const type = getNodeType(typeOrName, state.schema)\n\n  return originalWrapIn(type, attributes)(state, dispatch)\n}\n", "import { NodeType } from '@tiptap/pm/model'\nimport { wrapInList as originalWrapInList } from '@tiptap/pm/schema-list'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    wrapInList: {\n      /**\n       * Wrap a node in a list.\n       * @param typeOrName The type or name of the node.\n       * @param attributes The attributes of the node.\n       * @example editor.commands.wrapInList('bulletList')\n       */\n      wrapInList: (typeOrName: string | NodeType, attributes?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nexport const wrapInList: RawCommands['wrapInList'] = (typeOrName, attributes = {}) => ({ state, dispatch }) => {\n  const type = getNodeType(typeOrName, state.schema)\n\n  return originalWrapInList(type, attributes)(state, dispatch)\n}\n", "import * as commands from '../commands/index.js'\nimport { Extension } from '../Extension.js'\n\nexport * from '../commands/index.js'\n\nexport const Commands = Extension.create({\n  name: 'commands',\n\n  addCommands() {\n    return {\n      ...commands,\n    }\n  },\n})\n", "import { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'\n\nimport { Extension } from '../Extension.js'\n\nexport const Drop = Extension.create({\n  name: 'drop',\n\n  addProseMirrorPlugins() {\n    return [\n      new Plugin({\n        key: new PluginKey('tiptapDrop'),\n\n        props: {\n          handleDrop: (_, e, slice, moved) => {\n            this.editor.emit('drop', {\n              editor: this.editor,\n              event: e,\n              slice,\n              moved,\n            })\n          },\n        },\n      }),\n    ]\n  },\n})\n", "import { Plugin, Plugin<PERSON><PERSON> } from '@tiptap/pm/state'\n\nimport { Extension } from '../Extension.js'\n\nexport const Editable = Extension.create({\n  name: 'editable',\n\n  addProseMirrorPlugins() {\n    return [\n      new Plugin({\n        key: new PluginKey('editable'),\n        props: {\n          editable: () => this.editor.options.editable,\n        },\n      }),\n    ]\n  },\n})\n", "import { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'\n\nimport { Extension } from '../Extension.js'\n\nexport const focusEventsPluginKey = new PluginKey('focusEvents')\n\nexport const FocusEvents = Extension.create({\n  name: 'focusEvents',\n\n  addProseMirrorPlugins() {\n    const { editor } = this\n\n    return [\n      new Plugin({\n        key: focusEventsPluginKey,\n        props: {\n          handleDOMEvents: {\n            focus: (view, event: Event) => {\n              editor.isFocused = true\n\n              const transaction = editor.state.tr\n                .setMeta('focus', { event })\n                .setMeta('addToHistory', false)\n\n              view.dispatch(transaction)\n\n              return false\n            },\n            blur: (view, event: Event) => {\n              editor.isFocused = false\n\n              const transaction = editor.state.tr\n                .setMeta('blur', { event })\n                .setMeta('addToHistory', false)\n\n              view.dispatch(transaction)\n\n              return false\n            },\n          },\n        },\n      }),\n    ]\n  },\n})\n", "import { Plugin, Plugin<PERSON><PERSON>, <PERSON> } from '@tiptap/pm/state'\n\nimport { CommandManager } from '../CommandManager.js'\nimport { Extension } from '../Extension.js'\nimport { createChainableState } from '../helpers/createChainableState.js'\nimport { isNodeEmpty } from '../helpers/isNodeEmpty.js'\nimport { isiOS } from '../utilities/isiOS.js'\nimport { isMacOS } from '../utilities/isMacOS.js'\n\nexport const Keymap = Extension.create({\n  name: 'keymap',\n\n  addKeyboardShortcuts() {\n    const handleBackspace = () => this.editor.commands.first(({ commands }) => [\n      () => commands.undoInputRule(),\n\n      // maybe convert first text block node to default node\n      () => commands.command(({ tr }) => {\n        const { selection, doc } = tr\n        const { empty, $anchor } = selection\n        const { pos, parent } = $anchor\n        const $parentPos = $anchor.parent.isTextblock && pos > 0 ? tr.doc.resolve(pos - 1) : $anchor\n        const parentIsIsolating = $parentPos.parent.type.spec.isolating\n\n        const parentPos = $anchor.pos - $anchor.parentOffset\n\n        const isAtStart = (parentIsIsolating && $parentPos.parent.childCount === 1)\n          ? parentPos === $anchor.pos\n          : Selection.atStart(doc).from === pos\n\n        if (\n          !empty\n          || !parent.type.isTextblock\n          || parent.textContent.length\n          || !isAtStart\n          || (isAtStart && $anchor.parent.type.name === 'paragraph') // prevent clearNodes when no nodes to clear, otherwise history stack is appended\n        ) {\n          return false\n        }\n\n        return commands.clearNodes()\n      }),\n\n      () => commands.deleteSelection(),\n      () => commands.joinBackward(),\n      () => commands.selectNodeBackward(),\n    ])\n\n    const handleDelete = () => this.editor.commands.first(({ commands }) => [\n      () => commands.deleteSelection(),\n      () => commands.deleteCurrentNode(),\n      () => commands.joinForward(),\n      () => commands.selectNodeForward(),\n    ])\n\n    const handleEnter = () => this.editor.commands.first(({ commands }) => [\n      () => commands.newlineInCode(),\n      () => commands.createParagraphNear(),\n      () => commands.liftEmptyBlock(),\n      () => commands.splitBlock(),\n    ])\n\n    const baseKeymap = {\n      Enter: handleEnter,\n      'Mod-Enter': () => this.editor.commands.exitCode(),\n      Backspace: handleBackspace,\n      'Mod-Backspace': handleBackspace,\n      'Shift-Backspace': handleBackspace,\n      Delete: handleDelete,\n      'Mod-Delete': handleDelete,\n      'Mod-a': () => this.editor.commands.selectAll(),\n    }\n\n    const pcKeymap = {\n      ...baseKeymap,\n    }\n\n    const macKeymap = {\n      ...baseKeymap,\n      'Ctrl-h': handleBackspace,\n      'Alt-Backspace': handleBackspace,\n      'Ctrl-d': handleDelete,\n      'Ctrl-Alt-Backspace': handleDelete,\n      'Alt-Delete': handleDelete,\n      'Alt-d': handleDelete,\n      'Ctrl-a': () => this.editor.commands.selectTextblockStart(),\n      'Ctrl-e': () => this.editor.commands.selectTextblockEnd(),\n    }\n\n    if (isiOS() || isMacOS()) {\n      return macKeymap\n    }\n\n    return pcKeymap\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      // With this plugin we check if the whole document was selected and deleted.\n      // In this case we will additionally call `clearNodes()` to convert e.g. a heading\n      // to a paragraph if necessary.\n      // This is an alternative to ProseMirror's `AllSelection`, which doesn’t work well\n      // with many other commands.\n      new Plugin({\n        key: new PluginKey('clearDocument'),\n        appendTransaction: (transactions, oldState, newState) => {\n          if (transactions.some(tr => tr.getMeta('composition'))) {\n            return\n          }\n\n          const docChanges = transactions.some(transaction => transaction.docChanged)\n            && !oldState.doc.eq(newState.doc)\n\n          const ignoreTr = transactions.some(transaction => transaction.getMeta('preventClearDocument'))\n\n          if (!docChanges || ignoreTr) {\n            return\n          }\n\n          const { empty, from, to } = oldState.selection\n          const allFrom = Selection.atStart(oldState.doc).from\n          const allEnd = Selection.atEnd(oldState.doc).to\n          const allWasSelected = from === allFrom && to === allEnd\n\n          if (empty || !allWasSelected) {\n            return\n          }\n\n          const isEmpty = isNodeEmpty(newState.doc)\n\n          if (!isEmpty) {\n            return\n          }\n\n          const tr = newState.tr\n          const state = createChainableState({\n            state: newState,\n            transaction: tr,\n          })\n          const { commands } = new CommandManager({\n            editor: this.editor,\n            state,\n          })\n\n          commands.clearNodes()\n\n          if (!tr.steps.length) {\n            return\n          }\n\n          return tr\n        },\n      }),\n    ]\n  },\n})\n", "import { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'\n\nimport { Extension } from '../Extension.js'\n\nexport const Paste = Extension.create({\n  name: 'paste',\n\n  addProseMirrorPlugins() {\n\n    return [\n      new Plugin({\n        key: new PluginKey('tiptapPaste'),\n\n        props: {\n          handlePaste: (_view, e, slice) => {\n            this.editor.emit('paste', {\n              editor: this.editor,\n              event: e,\n              slice,\n            })\n          },\n        },\n      }),\n    ]\n  },\n})\n", "import { Plugin, Plugin<PERSON><PERSON> } from '@tiptap/pm/state'\n\nimport { Extension } from '../Extension.js'\n\nexport const Tabindex = Extension.create({\n  name: 'tabindex',\n\n  addProseMirrorPlugins() {\n    return [\n      new Plugin({\n        key: new PluginKey('tabindex'),\n        props: {\n          attributes: (): { [name: string]: string; } => (this.editor.isEditable ? { tabindex: '0' } : {}),\n        },\n      }),\n    ]\n  },\n})\n", "import {\n  Fragment, Node, ResolvedPos,\n} from '@tiptap/pm/model'\n\nimport { Editor } from './Editor.js'\nimport { Content, Range } from './types.js'\n\nexport class NodePos {\n  private resolvedPos: ResolvedPos\n\n  private isBlock: boolean\n\n  private editor: Editor\n\n  private get name(): string {\n    return this.node.type.name\n  }\n\n  constructor(pos: ResolvedPos, editor: Editor, isBlock = false, node: Node | null = null) {\n    this.isBlock = isBlock\n    this.resolvedPos = pos\n    this.editor = editor\n    this.currentNode = node\n  }\n\n  private currentNode: Node | null = null\n\n  get node(): Node {\n    return this.currentNode || this.resolvedPos.node()\n  }\n\n  get element(): HTMLElement {\n    return this.editor.view.domAtPos(this.pos).node as HTMLElement\n  }\n\n  public actualDepth: number | null = null\n\n  get depth(): number {\n    return this.actualDepth ?? this.resolvedPos.depth\n  }\n\n  get pos(): number {\n    return this.resolvedPos.pos\n  }\n\n  get content(): Fragment {\n    return this.node.content\n  }\n\n  set content(content: Content) {\n    let from = this.from\n    let to = this.to\n\n    if (this.isBlock) {\n      if (this.content.size === 0) {\n        console.error(`You can’t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`)\n        return\n      }\n\n      from = this.from + 1\n      to = this.to - 1\n    }\n\n    this.editor.commands.insertContentAt({ from, to }, content)\n  }\n\n  get attributes(): { [key: string]: any } {\n    return this.node.attrs\n  }\n\n  get textContent(): string {\n    return this.node.textContent\n  }\n\n  get size(): number {\n    return this.node.nodeSize\n  }\n\n  get from(): number {\n    if (this.isBlock) {\n      return this.pos\n    }\n\n    return this.resolvedPos.start(this.resolvedPos.depth)\n  }\n\n  get range(): Range {\n    return {\n      from: this.from,\n      to: this.to,\n    }\n  }\n\n  get to(): number {\n    if (this.isBlock) {\n      return this.pos + this.size\n    }\n\n    return this.resolvedPos.end(this.resolvedPos.depth) + (this.node.isText ? 0 : 1)\n  }\n\n  get parent(): NodePos | null {\n    if (this.depth === 0) {\n      return null\n    }\n\n    const parentPos = this.resolvedPos.start(this.resolvedPos.depth - 1)\n    const $pos = this.resolvedPos.doc.resolve(parentPos)\n\n    return new NodePos($pos, this.editor)\n  }\n\n  get before(): NodePos | null {\n    let $pos = this.resolvedPos.doc.resolve(this.from - (this.isBlock ? 1 : 2))\n\n    if ($pos.depth !== this.depth) {\n      $pos = this.resolvedPos.doc.resolve(this.from - 3)\n    }\n\n    return new NodePos($pos, this.editor)\n  }\n\n  get after(): NodePos | null {\n    let $pos = this.resolvedPos.doc.resolve(this.to + (this.isBlock ? 2 : 1))\n\n    if ($pos.depth !== this.depth) {\n      $pos = this.resolvedPos.doc.resolve(this.to + 3)\n    }\n\n    return new NodePos($pos, this.editor)\n  }\n\n  get children(): NodePos[] {\n    const children: NodePos[] = []\n\n    this.node.content.forEach((node, offset) => {\n      const isBlock = node.isBlock && !node.isTextblock\n      const isNonTextAtom = node.isAtom && !node.isText\n\n      const targetPos = this.pos + offset + (isNonTextAtom ? 0 : 1)\n      const $pos = this.resolvedPos.doc.resolve(targetPos)\n\n      if (!isBlock && $pos.depth <= this.depth) {\n        return\n      }\n\n      const childNodePos = new NodePos($pos, this.editor, isBlock, isBlock ? node : null)\n\n      if (isBlock) {\n        childNodePos.actualDepth = this.depth + 1\n      }\n\n      children.push(new NodePos($pos, this.editor, isBlock, isBlock ? node : null))\n    })\n\n    return children\n  }\n\n  get firstChild(): NodePos | null {\n    return this.children[0] || null\n  }\n\n  get lastChild(): NodePos | null {\n    const children = this.children\n\n    return children[children.length - 1] || null\n  }\n\n  closest(selector: string, attributes: { [key: string]: any } = {}): NodePos | null {\n    let node: NodePos | null = null\n    let currentNode = this.parent\n\n    while (currentNode && !node) {\n      if (currentNode.node.type.name === selector) {\n        if (Object.keys(attributes).length > 0) {\n          const nodeAttributes = currentNode.node.attrs\n          const attrKeys = Object.keys(attributes)\n\n          for (let index = 0; index < attrKeys.length; index += 1) {\n            const key = attrKeys[index]\n\n            if (nodeAttributes[key] !== attributes[key]) {\n              break\n            }\n          }\n        } else {\n          node = currentNode\n        }\n      }\n\n      currentNode = currentNode.parent\n    }\n\n    return node\n  }\n\n  querySelector(selector: string, attributes: { [key: string]: any } = {}): NodePos | null {\n    return this.querySelectorAll(selector, attributes, true)[0] || null\n  }\n\n  querySelectorAll(selector: string, attributes: { [key: string]: any } = {}, firstItemOnly = false): NodePos[] {\n    let nodes: NodePos[] = []\n\n    if (!this.children || this.children.length === 0) {\n      return nodes\n    }\n    const attrKeys = Object.keys(attributes)\n\n    /**\n     * Finds all children recursively that match the selector and attributes\n     * If firstItemOnly is true, it will return the first item found\n     */\n    this.children.forEach(childPos => {\n      // If we already found a node and we only want the first item, we dont need to keep going\n      if (firstItemOnly && nodes.length > 0) {\n        return\n      }\n\n      if (childPos.node.type.name === selector) {\n        const doesAllAttributesMatch = attrKeys.every(key => attributes[key] === childPos.node.attrs[key])\n\n        if (doesAllAttributesMatch) {\n          nodes.push(childPos)\n        }\n      }\n\n      // If we already found a node and we only want the first item, we can stop here and skip the recursion\n      if (firstItemOnly && nodes.length > 0) {\n        return\n      }\n\n      nodes = nodes.concat(childPos.querySelectorAll(selector, attributes, firstItemOnly))\n    })\n\n    return nodes\n  }\n\n  setAttribute(attributes: { [key: string]: any }) {\n    const { tr } = this.editor.state\n\n    tr.setNodeMarkup(this.from, undefined, {\n      ...this.node.attrs,\n      ...attributes,\n    })\n\n    this.editor.view.dispatch(tr)\n  }\n}\n", "export const style = `.ProseMirror {\n  position: relative;\n}\n\n.ProseMirror {\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  white-space: break-spaces;\n  -webkit-font-variant-ligatures: none;\n  font-variant-ligatures: none;\n  font-feature-settings: \"liga\" 0; /* the above doesn't seem to work in Edge */\n}\n\n.ProseMirror [contenteditable=\"false\"] {\n  white-space: normal;\n}\n\n.ProseMirror [contenteditable=\"false\"] [contenteditable=\"true\"] {\n  white-space: pre-wrap;\n}\n\n.ProseMirror pre {\n  white-space: pre-wrap;\n}\n\nimg.ProseMirror-separator {\n  display: inline !important;\n  border: none !important;\n  margin: 0 !important;\n  width: 0 !important;\n  height: 0 !important;\n}\n\n.ProseMirror-gapcursor {\n  display: none;\n  pointer-events: none;\n  position: absolute;\n  margin: 0;\n}\n\n.ProseMirror-gapcursor:after {\n  content: \"\";\n  display: block;\n  position: absolute;\n  top: -2px;\n  width: 20px;\n  border-top: 1px solid black;\n  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;\n}\n\n@keyframes ProseMirror-cursor-blink {\n  to {\n    visibility: hidden;\n  }\n}\n\n.ProseMirror-hideselection *::selection {\n  background: transparent;\n}\n\n.ProseMirror-hideselection *::-moz-selection {\n  background: transparent;\n}\n\n.ProseMirror-hideselection * {\n  caret-color: transparent;\n}\n\n.ProseMirror-focused .ProseMirror-gapcursor {\n  display: block;\n}\n\n.tippy-box[data-animation=fade][data-state=hidden] {\n  opacity: 0\n}`\n", "export function createStyleTag(style: string, nonce?: string, suffix?: string): HTMLStyleElement {\n  const tiptapStyleTag = (<HTMLStyleElement>document.querySelector(`style[data-tiptap-style${suffix ? `-${suffix}` : ''}]`))\n\n  if (tiptapStyleTag !== null) {\n    return tiptapStyleTag\n  }\n\n  const styleNode = document.createElement('style')\n\n  if (nonce) {\n    styleNode.setAttribute('nonce', nonce)\n  }\n\n  styleNode.setAttribute(`data-tiptap-style${suffix ? `-${suffix}` : ''}`, '')\n  styleNode.innerHTML = style\n  document.getElementsByTagName('head')[0].appendChild(styleNode)\n\n  return styleNode\n}\n", "/* eslint-disable @typescript-eslint/no-empty-object-type */\nimport {\n  MarkType,\n  Node as ProseMirrorNode,\n  NodeType,\n  Schema,\n} from '@tiptap/pm/model'\nimport {\n  EditorState, Plugin, PluginKey, Transaction,\n} from '@tiptap/pm/state'\nimport { EditorView } from '@tiptap/pm/view'\n\nimport { CommandManager } from './CommandManager.js'\nimport { EventEmitter } from './EventEmitter.js'\nimport { ExtensionManager } from './ExtensionManager.js'\nimport {\n  ClipboardTextSerializer, Commands, Drop, Editable, FocusEvents, Keymap, Paste,\n  Tabindex,\n} from './extensions/index.js'\nimport { createDocument } from './helpers/createDocument.js'\nimport { getAttributes } from './helpers/getAttributes.js'\nimport { getHTMLFromFragment } from './helpers/getHTMLFromFragment.js'\nimport { getText } from './helpers/getText.js'\nimport { getTextSerializersFromSchema } from './helpers/getTextSerializersFromSchema.js'\nimport { isActive } from './helpers/isActive.js'\nimport { isNodeEmpty } from './helpers/isNodeEmpty.js'\nimport { resolveFocusPosition } from './helpers/resolveFocusPosition.js'\nimport { NodePos } from './NodePos.js'\nimport { style } from './style.js'\nimport {\n  CanCommands,\n  ChainedCommands,\n  EditorEvents,\n  EditorOptions,\n  JSONContent,\n  SingleCommands,\n  TextSerializer,\n} from './types.js'\nimport { createStyleTag } from './utilities/createStyleTag.js'\nimport { isFunction } from './utilities/isFunction.js'\n\nexport * as extensions from './extensions/index.js'\n\n// @ts-ignore\nexport interface TiptapEditorHTMLElement extends HTMLElement {\n  editor?: Editor\n}\n\nexport class Editor extends EventEmitter<EditorEvents> {\n  private commandManager!: CommandManager\n\n  public extensionManager!: ExtensionManager\n\n  private css!: HTMLStyleElement\n\n  public schema!: Schema\n\n  public view!: EditorView\n\n  public isFocused = false\n\n  /**\n   * The editor is considered initialized after the `create` event has been emitted.\n   */\n  public isInitialized = false\n\n  public extensionStorage: Record<string, any> = {}\n\n  public options: EditorOptions = {\n    element: document.createElement('div'),\n    content: '',\n    injectCSS: true,\n    injectNonce: undefined,\n    extensions: [],\n    autofocus: false,\n    editable: true,\n    editorProps: {},\n    parseOptions: {},\n    coreExtensionOptions: {},\n    enableInputRules: true,\n    enablePasteRules: true,\n    enableCoreExtensions: true,\n    enableContentCheck: false,\n    emitContentError: false,\n    onBeforeCreate: () => null,\n    onCreate: () => null,\n    onUpdate: () => null,\n    onSelectionUpdate: () => null,\n    onTransaction: () => null,\n    onFocus: () => null,\n    onBlur: () => null,\n    onDestroy: () => null,\n    onContentError: ({ error }) => { throw error },\n    onPaste: () => null,\n    onDrop: () => null,\n  }\n\n  constructor(options: Partial<EditorOptions> = {}) {\n    super()\n    this.setOptions(options)\n    this.createExtensionManager()\n    this.createCommandManager()\n    this.createSchema()\n    this.on('beforeCreate', this.options.onBeforeCreate)\n    this.emit('beforeCreate', { editor: this })\n    this.on('contentError', this.options.onContentError)\n    this.createView()\n    this.injectCSS()\n    this.on('create', this.options.onCreate)\n    this.on('update', this.options.onUpdate)\n    this.on('selectionUpdate', this.options.onSelectionUpdate)\n    this.on('transaction', this.options.onTransaction)\n    this.on('focus', this.options.onFocus)\n    this.on('blur', this.options.onBlur)\n    this.on('destroy', this.options.onDestroy)\n    this.on('drop', ({ event, slice, moved }) => this.options.onDrop(event, slice, moved))\n    this.on('paste', ({ event, slice }) => this.options.onPaste(event, slice))\n\n    window.setTimeout(() => {\n      if (this.isDestroyed) {\n        return\n      }\n\n      this.commands.focus(this.options.autofocus)\n      this.emit('create', { editor: this })\n      this.isInitialized = true\n    }, 0)\n  }\n\n  /**\n   * Returns the editor storage.\n   */\n  public get storage(): Record<string, any> {\n    return this.extensionStorage\n  }\n\n  /**\n   * An object of all registered commands.\n   */\n  public get commands(): SingleCommands {\n    return this.commandManager.commands\n  }\n\n  /**\n   * Create a command chain to call multiple commands at once.\n   */\n  public chain(): ChainedCommands {\n    return this.commandManager.chain()\n  }\n\n  /**\n   * Check if a command or a command chain can be executed. Without executing it.\n   */\n  public can(): CanCommands {\n    return this.commandManager.can()\n  }\n\n  /**\n   * Inject CSS styles.\n   */\n  private injectCSS(): void {\n    if (this.options.injectCSS && document) {\n      this.css = createStyleTag(style, this.options.injectNonce)\n    }\n  }\n\n  /**\n   * Update editor options.\n   *\n   * @param options A list of options\n   */\n  public setOptions(options: Partial<EditorOptions> = {}): void {\n    this.options = {\n      ...this.options,\n      ...options,\n    }\n\n    if (!this.view || !this.state || this.isDestroyed) {\n      return\n    }\n\n    if (this.options.editorProps) {\n      this.view.setProps(this.options.editorProps)\n    }\n\n    this.view.updateState(this.state)\n  }\n\n  /**\n   * Update editable state of the editor.\n   */\n  public setEditable(editable: boolean, emitUpdate = true): void {\n    this.setOptions({ editable })\n\n    if (emitUpdate) {\n      this.emit('update', { editor: this, transaction: this.state.tr })\n    }\n  }\n\n  /**\n   * Returns whether the editor is editable.\n   */\n  public get isEditable(): boolean {\n    // since plugins are applied after creating the view\n    // `editable` is always `true` for one tick.\n    // that’s why we also have to check for `options.editable`\n    return this.options.editable && this.view && this.view.editable\n  }\n\n  /**\n   * Returns the editor state.\n   */\n  public get state(): EditorState {\n    return this.view.state\n  }\n\n  /**\n   * Register a ProseMirror plugin.\n   *\n   * @param plugin A ProseMirror plugin\n   * @param handlePlugins Control how to merge the plugin into the existing plugins.\n   * @returns The new editor state\n   */\n  public registerPlugin(\n    plugin: Plugin,\n    handlePlugins?: (newPlugin: Plugin, plugins: Plugin[]) => Plugin[],\n  ): EditorState {\n    const plugins = isFunction(handlePlugins)\n      ? handlePlugins(plugin, [...this.state.plugins])\n      : [...this.state.plugins, plugin]\n\n    const state = this.state.reconfigure({ plugins })\n\n    this.view.updateState(state)\n\n    return state\n  }\n\n  /**\n   * Unregister a ProseMirror plugin.\n   *\n   * @param nameOrPluginKeyToRemove The plugins name\n   * @returns The new editor state or undefined if the editor is destroyed\n   */\n  public unregisterPlugin(nameOrPluginKeyToRemove: string | PluginKey | (string | PluginKey)[]): EditorState | undefined {\n    if (this.isDestroyed) {\n      return undefined\n    }\n\n    const prevPlugins = this.state.plugins\n    let plugins = prevPlugins;\n\n    ([] as (string | PluginKey)[]).concat(nameOrPluginKeyToRemove).forEach(nameOrPluginKey => {\n      // @ts-ignore\n      const name = typeof nameOrPluginKey === 'string' ? `${nameOrPluginKey}$` : nameOrPluginKey.key\n\n      // @ts-ignore\n      plugins = plugins.filter(plugin => !plugin.key.startsWith(name))\n    })\n\n    if (prevPlugins.length === plugins.length) {\n      // No plugin was removed, so we don’t need to update the state\n      return undefined\n    }\n\n    const state = this.state.reconfigure({\n      plugins,\n    })\n\n    this.view.updateState(state)\n\n    return state\n  }\n\n  /**\n   * Creates an extension manager.\n   */\n  private createExtensionManager(): void {\n\n    const coreExtensions = this.options.enableCoreExtensions ? [\n      Editable,\n      ClipboardTextSerializer.configure({\n        blockSeparator: this.options.coreExtensionOptions?.clipboardTextSerializer?.blockSeparator,\n      }),\n      Commands,\n      FocusEvents,\n      Keymap,\n      Tabindex,\n      Drop,\n      Paste,\n    ].filter(ext => {\n      if (typeof this.options.enableCoreExtensions === 'object') {\n        return this.options.enableCoreExtensions[ext.name as keyof typeof this.options.enableCoreExtensions] !== false\n      }\n      return true\n    }) : []\n    const allExtensions = [...coreExtensions, ...this.options.extensions].filter(extension => {\n      return ['extension', 'node', 'mark'].includes(extension?.type)\n    })\n\n    this.extensionManager = new ExtensionManager(allExtensions, this)\n  }\n\n  /**\n   * Creates an command manager.\n   */\n  private createCommandManager(): void {\n    this.commandManager = new CommandManager({\n      editor: this,\n    })\n  }\n\n  /**\n   * Creates a ProseMirror schema.\n   */\n  private createSchema(): void {\n    this.schema = this.extensionManager.schema\n  }\n\n  /**\n   * Creates a ProseMirror view.\n   */\n  private createView(): void {\n    let doc: ProseMirrorNode\n\n    try {\n      doc = createDocument(\n        this.options.content,\n        this.schema,\n        this.options.parseOptions,\n        { errorOnInvalidContent: this.options.enableContentCheck },\n      )\n    } catch (e) {\n      if (!(e instanceof Error) || !['[tiptap error]: Invalid JSON content', '[tiptap error]: Invalid HTML content'].includes(e.message)) {\n        // Not the content error we were expecting\n        throw e\n      }\n      this.emit('contentError', {\n        editor: this,\n        error: e as Error,\n        disableCollaboration: () => {\n          if (this.storage.collaboration) {\n            this.storage.collaboration.isDisabled = true\n          }\n          // To avoid syncing back invalid content, reinitialize the extensions without the collaboration extension\n          this.options.extensions = this.options.extensions.filter(extension => extension.name !== 'collaboration')\n\n          // Restart the initialization process by recreating the extension manager with the new set of extensions\n          this.createExtensionManager()\n        },\n      })\n\n      // Content is invalid, but attempt to create it anyway, stripping out the invalid parts\n      doc = createDocument(\n        this.options.content,\n        this.schema,\n        this.options.parseOptions,\n        { errorOnInvalidContent: false },\n      )\n    }\n    const selection = resolveFocusPosition(doc, this.options.autofocus)\n\n    this.view = new EditorView(this.options.element, {\n      ...this.options.editorProps,\n      attributes: {\n        // add `role=\"textbox\"` to the editor element\n        role: 'textbox',\n        ...this.options.editorProps?.attributes,\n      },\n      dispatchTransaction: this.dispatchTransaction.bind(this),\n      state: EditorState.create({\n        doc,\n        selection: selection || undefined,\n      }),\n    })\n\n    // `editor.view` is not yet available at this time.\n    // Therefore we will add all plugins and node views directly afterwards.\n    const newState = this.state.reconfigure({\n      plugins: this.extensionManager.plugins,\n    })\n\n    this.view.updateState(newState)\n\n    this.createNodeViews()\n    this.prependClass()\n\n    // Let’s store the editor instance in the DOM element.\n    // So we’ll have access to it for tests.\n    // @ts-ignore\n    const dom = this.view.dom as TiptapEditorHTMLElement\n\n    dom.editor = this\n  }\n\n  /**\n   * Creates all node views.\n   */\n  public createNodeViews(): void {\n    if (this.view.isDestroyed) {\n      return\n    }\n\n    this.view.setProps({\n      nodeViews: this.extensionManager.nodeViews,\n    })\n  }\n\n  /**\n   * Prepend class name to element.\n   */\n  public prependClass(): void {\n    this.view.dom.className = `tiptap ${this.view.dom.className}`\n  }\n\n  public isCapturingTransaction = false\n\n  private capturedTransaction: Transaction | null = null\n\n  public captureTransaction(fn: () => void) {\n    this.isCapturingTransaction = true\n    fn()\n    this.isCapturingTransaction = false\n\n    const tr = this.capturedTransaction\n\n    this.capturedTransaction = null\n\n    return tr\n  }\n\n  /**\n   * The callback over which to send transactions (state updates) produced by the view.\n   *\n   * @param transaction An editor state transaction\n   */\n  private dispatchTransaction(transaction: Transaction): void {\n    // if the editor / the view of the editor was destroyed\n    // the transaction should not be dispatched as there is no view anymore.\n    if (this.view.isDestroyed) {\n      return\n    }\n\n    if (this.isCapturingTransaction) {\n      if (!this.capturedTransaction) {\n        this.capturedTransaction = transaction\n\n        return\n      }\n\n      transaction.steps.forEach(step => this.capturedTransaction?.step(step))\n\n      return\n    }\n\n    const state = this.state.apply(transaction)\n    const selectionHasChanged = !this.state.selection.eq(state.selection)\n\n    this.emit('beforeTransaction', {\n      editor: this,\n      transaction,\n      nextState: state,\n    })\n    this.view.updateState(state)\n    this.emit('transaction', {\n      editor: this,\n      transaction,\n    })\n\n    if (selectionHasChanged) {\n      this.emit('selectionUpdate', {\n        editor: this,\n        transaction,\n      })\n    }\n\n    const focus = transaction.getMeta('focus')\n    const blur = transaction.getMeta('blur')\n\n    if (focus) {\n      this.emit('focus', {\n        editor: this,\n        event: focus.event,\n        transaction,\n      })\n    }\n\n    if (blur) {\n      this.emit('blur', {\n        editor: this,\n        event: blur.event,\n        transaction,\n      })\n    }\n\n    if (!transaction.docChanged || transaction.getMeta('preventUpdate')) {\n      return\n    }\n\n    this.emit('update', {\n      editor: this,\n      transaction,\n    })\n  }\n\n  /**\n   * Get attributes of the currently selected node or mark.\n   */\n  public getAttributes(nameOrType: string | NodeType | MarkType): Record<string, any> {\n    return getAttributes(this.state, nameOrType)\n  }\n\n  /**\n   * Returns if the currently selected node or mark is active.\n   *\n   * @param name Name of the node or mark\n   * @param attributes Attributes of the node or mark\n   */\n  public isActive(name: string, attributes?: {}): boolean\n  public isActive(attributes: {}): boolean\n  public isActive(nameOrAttributes: string, attributesOrUndefined?: {}): boolean {\n    const name = typeof nameOrAttributes === 'string' ? nameOrAttributes : null\n\n    const attributes = typeof nameOrAttributes === 'string' ? attributesOrUndefined : nameOrAttributes\n\n    return isActive(this.state, name, attributes)\n  }\n\n  /**\n   * Get the document as JSON.\n   */\n  public getJSON(): JSONContent {\n    return this.state.doc.toJSON()\n  }\n\n  /**\n   * Get the document as HTML.\n   */\n  public getHTML(): string {\n    return getHTMLFromFragment(this.state.doc.content, this.schema)\n  }\n\n  /**\n   * Get the document as text.\n   */\n  public getText(options?: {\n    blockSeparator?: string\n    textSerializers?: Record<string, TextSerializer>\n  }): string {\n    const { blockSeparator = '\\n\\n', textSerializers = {} } = options || {}\n\n    return getText(this.state.doc, {\n      blockSeparator,\n      textSerializers: {\n        ...getTextSerializersFromSchema(this.schema),\n        ...textSerializers,\n      },\n    })\n  }\n\n  /**\n   * Check if there is no content.\n   */\n  public get isEmpty(): boolean {\n    return isNodeEmpty(this.state.doc)\n  }\n\n  /**\n   * Get the number of characters for the current document.\n   *\n   * @deprecated\n   */\n  public getCharacterCount(): number {\n    console.warn(\n      '[tiptap warn]: \"editor.getCharacterCount()\" is deprecated. Please use \"editor.storage.characterCount.characters()\" instead.',\n    )\n\n    return this.state.doc.content.size - 2\n  }\n\n  /**\n   * Destroy the editor.\n   */\n  public destroy(): void {\n    this.emit('destroy')\n\n    if (this.view) {\n      // Cleanup our reference to prevent circular references which caused memory leaks\n      // @ts-ignore\n      const dom = this.view.dom as TiptapEditorHTMLElement\n\n      if (dom && dom.editor) {\n        delete dom.editor\n      }\n      this.view.destroy()\n    }\n\n    this.removeAllListeners()\n  }\n\n  /**\n   * Check if the editor is already destroyed.\n   */\n  public get isDestroyed(): boolean {\n    // @ts-ignore\n    return !this.view?.docView\n  }\n\n  public $node(selector: string, attributes?: { [key: string]: any }): NodePos | null {\n    return this.$doc?.querySelector(selector, attributes) || null\n  }\n\n  public $nodes(selector: string, attributes?: { [key: string]: any }): NodePos[] | null {\n    return this.$doc?.querySelectorAll(selector, attributes) || null\n  }\n\n  public $pos(pos: number) {\n    const $pos = this.state.doc.resolve(pos)\n\n    return new NodePos($pos, this)\n  }\n\n  get $doc() {\n    return this.$pos(0)\n  }\n}\n", "import { MarkType } from '@tiptap/pm/model'\n\nimport { getMarksBetween } from '../helpers/getMarksBetween.js'\nimport { InputRule, InputRuleFinder } from '../InputRule.js'\nimport { ExtendedRegExpMatchArray } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\n\n/**\n * Build an input rule that adds a mark when the\n * matched text is typed into it.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#input-rules\n */\nexport function markInputRule(config: {\n  find: InputRuleFinder\n  type: MarkType\n  getAttributes?:\n    | Record<string, any>\n    | ((match: ExtendedRegExpMatchArray) => Record<string, any>)\n    | false\n    | null\n}) {\n  return new InputRule({\n    find: config.find,\n    handler: ({ state, range, match }) => {\n      const attributes = callOrReturn(config.getAttributes, undefined, match)\n\n      if (attributes === false || attributes === null) {\n        return null\n      }\n\n      const { tr } = state\n      const captureGroup = match[match.length - 1]\n      const fullMatch = match[0]\n\n      if (captureGroup) {\n        const startSpaces = fullMatch.search(/\\S/)\n        const textStart = range.from + fullMatch.indexOf(captureGroup)\n        const textEnd = textStart + captureGroup.length\n\n        const excludedMarks = getMarksBetween(range.from, range.to, state.doc)\n          .filter(item => {\n            // @ts-ignore\n            const excluded = item.mark.type.excluded as MarkType[]\n\n            return excluded.find(type => type === config.type && type !== item.mark.type)\n          })\n          .filter(item => item.to > textStart)\n\n        if (excludedMarks.length) {\n          return null\n        }\n\n        if (textEnd < range.to) {\n          tr.delete(textEnd, range.to)\n        }\n\n        if (textStart > range.from) {\n          tr.delete(range.from + startSpaces, textStart)\n        }\n\n        const markEnd = range.from + startSpaces + captureGroup.length\n\n        tr.addMark(range.from + startSpaces, markEnd, config.type.create(attributes || {}))\n\n        tr.removeStoredMark(config.type)\n      }\n    },\n  })\n}\n", "import { NodeType } from '@tiptap/pm/model'\n\nimport { InputRule, InputRuleFinder } from '../InputRule.js'\nimport { ExtendedRegExpMatchArray } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\n\n/**\n * Build an input rule that adds a node when the\n * matched text is typed into it.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#input-rules\n */\nexport function nodeInputRule(config: {\n  /**\n   * The regex to match.\n   */\n  find: InputRuleFinder\n\n  /**\n   * The node type to add.\n   */\n  type: NodeType\n\n  /**\n   * A function that returns the attributes for the node\n   * can also be an object of attributes\n   */\n  getAttributes?:\n    | Record<string, any>\n    | ((match: ExtendedRegExpMatchArray) => Record<string, any>)\n    | false\n    | null\n}) {\n  return new InputRule({\n    find: config.find,\n    handler: ({ state, range, match }) => {\n      const attributes = callOrReturn(config.getAttributes, undefined, match) || {}\n      const { tr } = state\n      const start = range.from\n      let end = range.to\n\n      const newNode = config.type.create(attributes)\n\n      if (match[1]) {\n        const offset = match[0].lastIndexOf(match[1])\n        let matchStart = start + offset\n\n        if (matchStart > end) {\n          matchStart = end\n        } else {\n          end = matchStart + match[1].length\n        }\n\n        // insert last typed character\n        const lastChar = match[0][match[0].length - 1]\n\n        tr.insertText(lastChar, start + match[0].length - 1)\n\n        // insert node from input rule\n        tr.replaceWith(matchStart, end, newNode)\n      } else if (match[0]) {\n        const insertionStart = config.type.isInline ? start : start - 1\n\n        tr.insert(insertionStart, config.type.create(attributes)).delete(\n          tr.mapping.map(start),\n          tr.mapping.map(end),\n        )\n      }\n\n      tr.scrollIntoView()\n    },\n  })\n}\n", "import { NodeType } from '@tiptap/pm/model'\n\nimport { InputRule, InputRuleFinder } from '../InputRule.js'\nimport { ExtendedRegExpMatchArray } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\n\n/**\n * Build an input rule that changes the type of a textblock when the\n * matched text is typed into it. When using a regular expresion you’ll\n * probably want the regexp to start with `^`, so that the pattern can\n * only occur at the start of a textblock.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#input-rules\n */\nexport function textblockTypeInputRule(config: {\n  find: InputRuleFinder\n  type: NodeType\n  getAttributes?:\n    | Record<string, any>\n    | ((match: ExtendedRegExpMatchArray) => Record<string, any>)\n    | false\n    | null\n}) {\n  return new InputRule({\n    find: config.find,\n    handler: ({ state, range, match }) => {\n      const $start = state.doc.resolve(range.from)\n      const attributes = callOrReturn(config.getAttributes, undefined, match) || {}\n\n      if (!$start.node(-1).canReplaceWith($start.index(-1), $start.indexAfter(-1), config.type)) {\n        return null\n      }\n\n      state.tr\n        .delete(range.from, range.to)\n        .setBlockType(range.from, range.from, config.type, attributes)\n    },\n  })\n}\n", "import { InputRule, InputRuleFinder } from '../InputRule.js'\n\n/**\n * Build an input rule that replaces text when the\n * matched text is typed into it.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#input-rules\n */\nexport function textInputRule(config: {\n  find: InputRuleFinder,\n  replace: string,\n}) {\n  return new InputRule({\n    find: config.find,\n    handler: ({ state, range, match }) => {\n      let insert = config.replace\n      let start = range.from\n      const end = range.to\n\n      if (match[1]) {\n        const offset = match[0].lastIndexOf(match[1])\n\n        insert += match[0].slice(offset + match[1].length)\n        start += offset\n\n        const cutOff = start - end\n\n        if (cutOff > 0) {\n          insert = match[0].slice(offset - cutOff, offset) + insert\n          start = end\n        }\n      }\n\n      state.tr.insertText(insert, start, end)\n    },\n  })\n}\n", "import { Node as ProseMir<PERSON>rNode, NodeType } from '@tiptap/pm/model'\nimport { canJoin, findWrapping } from '@tiptap/pm/transform'\n\nimport { Editor } from '../Editor.js'\nimport { InputRule, InputRuleFinder } from '../InputRule.js'\nimport { ExtendedRegExpMatchArray } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\n\n/**\n * Build an input rule for automatically wrapping a textblock when a\n * given string is typed. When using a regular expresion you’ll\n * probably want the regexp to start with `^`, so that the pattern can\n * only occur at the start of a textblock.\n *\n * `type` is the type of node to wrap in.\n *\n * By default, if there’s a node with the same type above the newly\n * wrapped node, the rule will try to join those\n * two nodes. You can pass a join predicate, which takes a regular\n * expression match and the node before the wrapped node, and can\n * return a boolean to indicate whether a join should happen.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#input-rules\n */\nexport function wrappingInputRule(config: {\n  find: InputRuleFinder,\n  type: NodeType,\n  keepMarks?: boolean,\n  keepAttributes?: boolean,\n  editor?: Editor\n  getAttributes?:\n  | Record<string, any>\n  | ((match: ExtendedRegExpMatchArray) => Record<string, any>)\n  | false\n  | null\n  ,\n  joinPredicate?: (match: ExtendedRegExpMatchArray, node: ProseMirrorNode) => boolean,\n}) {\n  return new InputRule({\n    find: config.find,\n    handler: ({\n      state, range, match, chain,\n    }) => {\n      const attributes = callOrReturn(config.getAttributes, undefined, match) || {}\n      const tr = state.tr.delete(range.from, range.to)\n      const $start = tr.doc.resolve(range.from)\n      const blockRange = $start.blockRange()\n      const wrapping = blockRange && findWrapping(blockRange, config.type, attributes)\n\n      if (!wrapping) {\n        return null\n      }\n\n      tr.wrap(blockRange, wrapping)\n\n      if (config.keepMarks && config.editor) {\n        const { selection, storedMarks } = state\n        const { splittableMarks } = config.editor.extensionManager\n        const marks = storedMarks || (selection.$to.parentOffset && selection.$from.marks())\n\n        if (marks) {\n          const filteredMarks = marks.filter(mark => splittableMarks.includes(mark.type.name))\n\n          tr.ensureMarks(filteredMarks)\n        }\n      }\n      if (config.keepAttributes) {\n        /** If the nodeType is `bulletList` or `orderedList` set the `nodeType` as `listItem` */\n        const nodeType = config.type.name === 'bulletList' || config.type.name === 'orderedList' ? 'listItem' : 'taskList'\n\n        chain().updateAttributes(nodeType, attributes).run()\n      }\n\n      const before = tr.doc.resolve(range.from - 1).nodeBefore\n\n      if (\n        before\n        && before.type === config.type\n        && canJoin(tr.doc, range.from - 1)\n        && (!config.joinPredicate || config.joinPredicate(match, before))\n      ) {\n        tr.join(range.from - 1)\n      }\n    },\n  })\n}\n", "import {\n  DOMOutputSpec, Node as ProseMirror<PERSON>ode, NodeSpec, NodeType,\n} from '@tiptap/pm/model'\nimport { Plugin, Transaction } from '@tiptap/pm/state'\n\nimport { Editor } from './Editor.js'\nimport { getExtensionField } from './helpers/getExtensionField.js'\nimport { NodeConfig } from './index.js'\nimport { InputRule } from './InputRule.js'\nimport { Mark } from './Mark.js'\nimport { PasteRule } from './PasteRule.js'\nimport {\n  AnyConfig,\n  Attributes,\n  Extensions,\n  GlobalAttributes,\n  KeyboardShortcutCommand,\n  NodeViewRenderer,\n  ParentConfig,\n  RawCommands,\n} from './types.js'\nimport { callOrReturn } from './utilities/callOrReturn.js'\nimport { mergeDeep } from './utilities/mergeDeep.js'\n\ndeclare module '@tiptap/core' {\n  interface NodeConfig<Options = any, Storage = any> {\n    // @ts-ignore - this is a dynamic key\n    [key: string]: any\n\n    /**\n     * The extension name - this must be unique.\n     * It will be used to identify the extension.\n     *\n     * @example 'myExtension'\n     */\n    name: string\n\n    /**\n     * The priority of your extension. The higher, the earlier it will be called\n     * and will take precedence over other extensions with a lower priority.\n     * @default 100\n     * @example 101\n     */\n    priority?: number\n\n    /**\n     * The default options for this extension.\n     * @example\n     * defaultOptions: {\n     *   myOption: 'foo',\n     *   myOtherOption: 10,\n     * }\n     */\n    defaultOptions?: Options\n\n    /**\n     * This method will add options to this extension\n     * @see https://tiptap.dev/guide/custom-extensions#settings\n     * @example\n     * addOptions() {\n     *  return {\n     *    myOption: 'foo',\n     *    myOtherOption: 10,\n     * }\n     */\n    addOptions?: (this: {\n      name: string\n      parent: Exclude<ParentConfig<NodeConfig<Options, Storage>>['addOptions'], undefined>\n    }) => Options\n\n    /**\n     * The default storage this extension can save data to.\n     * @see https://tiptap.dev/guide/custom-extensions#storage\n     * @example\n     * defaultStorage: {\n     *   prefetchedUsers: [],\n     *   loading: false,\n     * }\n     */\n    addStorage?: (this: {\n      name: string\n      options: Options\n      parent: Exclude<ParentConfig<NodeConfig<Options, Storage>>['addStorage'], undefined>\n    }) => Storage\n\n    /**\n     * This function adds globalAttributes to specific nodes.\n     * @see https://tiptap.dev/guide/custom-extensions#global-attributes\n     * @example\n     * addGlobalAttributes() {\n     *   return [\n     *     {\n             // Extend the following extensions\n     *       types: [\n     *         'heading',\n     *         'paragraph',\n     *       ],\n     *       // … with those attributes\n     *       attributes: {\n     *         textAlign: {\n     *           default: 'left',\n     *           renderHTML: attributes => ({\n     *             style: `text-align: ${attributes.textAlign}`,\n     *           }),\n     *           parseHTML: element => element.style.textAlign || 'left',\n     *         },\n     *       },\n     *     },\n     *   ]\n     * }\n     */\n    addGlobalAttributes?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      extensions: (Node | Mark)[]\n      parent: ParentConfig<NodeConfig<Options, Storage>>['addGlobalAttributes']\n    }) => GlobalAttributes\n\n    /**\n     * This function adds commands to the editor\n     * @see https://tiptap.dev/guide/custom-extensions#keyboard-shortcuts\n     * @example\n     * addCommands() {\n     *   return {\n     *     myCommand: () => ({ chain }) => chain().setMark('type', 'foo').run(),\n     *   }\n     * }\n     */\n    addCommands?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: NodeType\n      parent: ParentConfig<NodeConfig<Options, Storage>>['addCommands']\n    }) => Partial<RawCommands>\n\n    /**\n     * This function registers keyboard shortcuts.\n     * @see https://tiptap.dev/guide/custom-extensions#keyboard-shortcuts\n     * @example\n     * addKeyboardShortcuts() {\n     *   return {\n     *     'Mod-l': () => this.editor.commands.toggleBulletList(),\n     *   }\n     * },\n     */\n    addKeyboardShortcuts?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: NodeType\n      parent: ParentConfig<NodeConfig<Options, Storage>>['addKeyboardShortcuts']\n    }) => {\n      [key: string]: KeyboardShortcutCommand\n    }\n\n    /**\n     * This function adds input rules to the editor.\n     * @see https://tiptap.dev/guide/custom-extensions#input-rules\n     * @example\n     * addInputRules() {\n     *   return [\n     *     markInputRule({\n     *       find: inputRegex,\n     *       type: this.type,\n     *     }),\n     *   ]\n     * },\n     */\n    addInputRules?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: NodeType\n      parent: ParentConfig<NodeConfig<Options, Storage>>['addInputRules']\n    }) => InputRule[]\n\n    /**\n     * This function adds paste rules to the editor.\n     * @see https://tiptap.dev/guide/custom-extensions#paste-rules\n     * @example\n     * addPasteRules() {\n     *   return [\n     *     markPasteRule({\n     *       find: pasteRegex,\n     *       type: this.type,\n     *     }),\n     *   ]\n     * },\n     */\n    addPasteRules?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: NodeType\n      parent: ParentConfig<NodeConfig<Options, Storage>>['addPasteRules']\n    }) => PasteRule[]\n\n    /**\n     * This function adds Prosemirror plugins to the editor\n     * @see https://tiptap.dev/guide/custom-extensions#prosemirror-plugins\n     * @example\n     * addProseMirrorPlugins() {\n     *   return [\n     *     customPlugin(),\n     *   ]\n     * }\n     */\n    addProseMirrorPlugins?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: NodeType\n      parent: ParentConfig<NodeConfig<Options, Storage>>['addProseMirrorPlugins']\n    }) => Plugin[]\n\n    /**\n     * This function adds additional extensions to the editor. This is useful for\n     * building extension kits.\n     * @example\n     * addExtensions() {\n     *   return [\n     *     BulletList,\n     *     OrderedList,\n     *     ListItem\n     *   ]\n     * }\n     */\n    addExtensions?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      parent: ParentConfig<NodeConfig<Options, Storage>>['addExtensions']\n    }) => Extensions\n\n    /**\n     * This function extends the schema of the node.\n     * @example\n     * extendNodeSchema() {\n     *   return {\n     *     group: 'inline',\n     *     selectable: false,\n     *   }\n     * }\n     */\n    extendNodeSchema?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<NodeConfig<Options, Storage>>['extendNodeSchema']\n          },\n          extension: Node,\n        ) => Record<string, any>)\n      | null\n\n    /**\n     * This function extends the schema of the mark.\n     * @example\n     * extendMarkSchema() {\n     *   return {\n     *     group: 'inline',\n     *     selectable: false,\n     *   }\n     * }\n     */\n    extendMarkSchema?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<NodeConfig<Options, Storage>>['extendMarkSchema']\n            editor?: Editor\n          },\n          extension: Node,\n        ) => Record<string, any>)\n      | null\n\n    /**\n     * The editor is not ready yet.\n     */\n    onBeforeCreate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: NodeType\n          parent: ParentConfig<NodeConfig<Options, Storage>>['onBeforeCreate']\n        }) => void)\n      | null\n\n    /**\n     * The editor is ready.\n     */\n    onCreate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: NodeType\n          parent: ParentConfig<NodeConfig<Options, Storage>>['onCreate']\n        }) => void)\n      | null\n\n    /**\n     * The content has changed.\n     */\n    onUpdate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: NodeType\n          parent: ParentConfig<NodeConfig<Options, Storage>>['onUpdate']\n        }) => void)\n      | null\n\n    /**\n     * The selection has changed.\n     */\n    onSelectionUpdate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: NodeType\n          parent: ParentConfig<NodeConfig<Options, Storage>>['onSelectionUpdate']\n        }) => void)\n      | null\n\n    /**\n     * The editor state has changed.\n     */\n    onTransaction?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            type: NodeType\n            parent: ParentConfig<NodeConfig<Options, Storage>>['onTransaction']\n          },\n          props: {\n            editor: Editor\n            transaction: Transaction\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor is focused.\n     */\n    onFocus?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            type: NodeType\n            parent: ParentConfig<NodeConfig<Options, Storage>>['onFocus']\n          },\n          props: {\n            event: FocusEvent\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor isn’t focused anymore.\n     */\n    onBlur?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            type: NodeType\n            parent: ParentConfig<NodeConfig<Options, Storage>>['onBlur']\n          },\n          props: {\n            event: FocusEvent\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor is destroyed.\n     */\n    onDestroy?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: NodeType\n          parent: ParentConfig<NodeConfig<Options, Storage>>['onDestroy']\n        }) => void)\n      | null\n\n    /**\n     * Node View\n     */\n    addNodeView?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: NodeType\n          parent: ParentConfig<NodeConfig<Options, Storage>>['addNodeView']\n        }) => NodeViewRenderer)\n      | null\n\n    /**\n     * Defines if this node should be a top level node (doc)\n     * @default false\n     * @example true\n     */\n    topNode?: boolean\n\n    /**\n     * The content expression for this node, as described in the [schema\n     * guide](/docs/guide/#schema.content_expressions). When not given,\n     * the node does not allow any content.\n     *\n     * You can read more about it on the Prosemirror documentation here\n     * @see https://prosemirror.net/docs/guide/#schema.content_expressions\n     * @default undefined\n     * @example content: 'block+'\n     * @example content: 'headline paragraph block*'\n     */\n    content?:\n      | NodeSpec['content']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['content']\n          editor?: Editor\n        }) => NodeSpec['content'])\n\n    /**\n     * The marks that are allowed inside of this node. May be a\n     * space-separated string referring to mark names or groups, `\"_\"`\n     * to explicitly allow all marks, or `\"\"` to disallow marks. When\n     * not given, nodes with inline content default to allowing all\n     * marks, other nodes default to not allowing marks.\n     *\n     * @example marks: 'strong em'\n     */\n    marks?:\n      | NodeSpec['marks']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['marks']\n          editor?: Editor\n        }) => NodeSpec['marks'])\n\n    /**\n     * The group or space-separated groups to which this node belongs,\n     * which can be referred to in the content expressions for the\n     * schema.\n     *\n     * By default Tiptap uses the groups 'block' and 'inline' for nodes. You\n     * can also use custom groups if you want to group specific nodes together\n     * and handle them in your schema.\n     * @example group: 'block'\n     * @example group: 'inline'\n     * @example group: 'customBlock' // this uses a custom group\n     */\n    group?:\n      | NodeSpec['group']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['group']\n          editor?: Editor\n        }) => NodeSpec['group'])\n\n    /**\n     * Should be set to true for inline nodes. (Implied for text nodes.)\n     */\n    inline?:\n      | NodeSpec['inline']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['inline']\n          editor?: Editor\n        }) => NodeSpec['inline'])\n\n    /**\n     * Can be set to true to indicate that, though this isn't a [leaf\n     * node](https://prosemirror.net/docs/ref/#model.NodeType.isLeaf), it doesn't have directly editable\n     * content and should be treated as a single unit in the view.\n     *\n     * @example atom: true\n     */\n    atom?:\n      | NodeSpec['atom']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['atom']\n          editor?: Editor\n        }) => NodeSpec['atom'])\n\n    /**\n     * Controls whether nodes of this type can be selected as a [node\n     * selection](https://prosemirror.net/docs/ref/#state.NodeSelection). Defaults to true for non-text\n     * nodes.\n     *\n     * @default true\n     * @example selectable: false\n     */\n    selectable?:\n      | NodeSpec['selectable']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['selectable']\n          editor?: Editor\n        }) => NodeSpec['selectable'])\n\n    /**\n     * Determines whether nodes of this type can be dragged without\n     * being selected. Defaults to false.\n     *\n     * @default: false\n     * @example: draggable: true\n     */\n    draggable?:\n      | NodeSpec['draggable']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['draggable']\n          editor?: Editor\n        }) => NodeSpec['draggable'])\n\n    /**\n     * Can be used to indicate that this node contains code, which\n     * causes some commands to behave differently.\n     */\n    code?:\n      | NodeSpec['code']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['code']\n          editor?: Editor\n        }) => NodeSpec['code'])\n\n    /**\n     * Controls way whitespace in this a node is parsed. The default is\n     * `\"normal\"`, which causes the [DOM parser](https://prosemirror.net/docs/ref/#model.DOMParser) to\n     * collapse whitespace in normal mode, and normalize it (replacing\n     * newlines and such with spaces) otherwise. `\"pre\"` causes the\n     * parser to preserve spaces inside the node. When this option isn't\n     * given, but [`code`](https://prosemirror.net/docs/ref/#model.NodeSpec.code) is true, `whitespace`\n     * will default to `\"pre\"`. Note that this option doesn't influence\n     * the way the node is rendered—that should be handled by `toDOM`\n     * and/or styling.\n     */\n    whitespace?:\n      | NodeSpec['whitespace']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['whitespace']\n          editor?: Editor\n        }) => NodeSpec['whitespace'])\n\n    /**\n     * Allows a **single** node to be set as linebreak equivalent (e.g. hardBreak).\n     * When converting between block types that have whitespace set to \"pre\"\n     * and don't support the linebreak node (e.g. codeBlock) and other block types\n     * that do support the linebreak node (e.g. paragraphs) - this node will be used\n     * as the linebreak instead of stripping the newline.\n     *\n     * See [linebreakReplacement](https://prosemirror.net/docs/ref/#model.NodeSpec.linebreakReplacement).\n     */\n    linebreakReplacement?:\n      | NodeSpec['linebreakReplacement']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['linebreakReplacement']\n          editor?: Editor\n        }) => NodeSpec['linebreakReplacement'])\n\n    /**\n     * When enabled, enables both\n     * [`definingAsContext`](https://prosemirror.net/docs/ref/#model.NodeSpec.definingAsContext) and\n     * [`definingForContent`](https://prosemirror.net/docs/ref/#model.NodeSpec.definingForContent).\n     *\n     * @default false\n     * @example isolating: true\n     */\n    defining?:\n      | NodeSpec['defining']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['defining']\n          editor?: Editor\n        }) => NodeSpec['defining'])\n\n    /**\n     * When enabled (default is false), the sides of nodes of this type\n     * count as boundaries that regular editing operations, like\n     * backspacing or lifting, won't cross. An example of a node that\n     * should probably have this enabled is a table cell.\n     */\n    isolating?:\n      | NodeSpec['isolating']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['isolating']\n          editor?: Editor\n        }) => NodeSpec['isolating'])\n\n    /**\n     * Associates DOM parser information with this node, which can be\n     * used by [`DOMParser.fromSchema`](https://prosemirror.net/docs/ref/#model.DOMParser^fromSchema) to\n     * automatically derive a parser. The `node` field in the rules is\n     * implied (the name of this node will be filled in automatically).\n     * If you supply your own parser, you do not need to also specify\n     * parsing rules in your schema.\n     *\n     * @example parseHTML: [{ tag: 'div', attrs: { 'data-id': 'my-block' } }]\n     */\n    parseHTML?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      parent: ParentConfig<NodeConfig<Options, Storage>>['parseHTML']\n      editor?: Editor\n    }) => NodeSpec['parseDOM']\n\n    /**\n     * A description of a DOM structure. Can be either a string, which is\n     * interpreted as a text node, a DOM node, which is interpreted as\n     * itself, a `{dom, contentDOM}` object, or an array.\n     *\n     * An array describes a DOM element. The first value in the array\n     * should be a string—the name of the DOM element, optionally prefixed\n     * by a namespace URL and a space. If the second element is plain\n     * object, it is interpreted as a set of attributes for the element.\n     * Any elements after that (including the 2nd if it's not an attribute\n     * object) are interpreted as children of the DOM elements, and must\n     * either be valid `DOMOutputSpec` values, or the number zero.\n     *\n     * The number zero (pronounced “hole”) is used to indicate the place\n     * where a node's child nodes should be inserted. If it occurs in an\n     * output spec, it should be the only child element in its parent\n     * node.\n     *\n     * @example toDOM: ['div[data-id=\"my-block\"]', { class: 'my-block' }, 0]\n     */\n    renderHTML?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<NodeConfig<Options, Storage>>['renderHTML']\n            editor?: Editor\n          },\n          props: {\n            node: ProseMirrorNode\n            HTMLAttributes: Record<string, any>\n          },\n        ) => DOMOutputSpec)\n      | null\n\n    /**\n     * renders the node as text\n     * @example renderText: () => 'foo\n     */\n    renderText?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<NodeConfig<Options, Storage>>['renderText']\n            editor?: Editor\n          },\n          props: {\n            node: ProseMirrorNode\n            pos: number\n            parent: ProseMirrorNode\n            index: number\n          },\n        ) => string)\n      | null\n\n    /**\n     * Add attributes to the node\n     * @example addAttributes: () => ({ class: 'foo' })\n     */\n    addAttributes?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      parent: ParentConfig<NodeConfig<Options, Storage>>['addAttributes']\n      editor?: Editor\n    // eslint-disable-next-line @typescript-eslint/no-empty-object-type\n    }) => Attributes | {}\n  }\n}\n\n/**\n * The Node class is used to create custom node extensions.\n * @see https://tiptap.dev/api/extensions#create-a-new-extension\n */\nexport class Node<Options = any, Storage = any> {\n  type = 'node'\n\n  name = 'node'\n\n  parent: Node | null = null\n\n  child: Node | null = null\n\n  options: Options\n\n  storage: Storage\n\n  config: NodeConfig = {\n    name: this.name,\n    defaultOptions: {},\n  }\n\n  constructor(config: Partial<NodeConfig<Options, Storage>> = {}) {\n    this.config = {\n      ...this.config,\n      ...config,\n    }\n\n    this.name = this.config.name\n\n    if (config.defaultOptions && Object.keys(config.defaultOptions).length > 0) {\n      console.warn(\n        `[tiptap warn]: BREAKING CHANGE: \"defaultOptions\" is deprecated. Please use \"addOptions\" instead. Found in extension: \"${this.name}\".`,\n      )\n    }\n\n    // TODO: remove `addOptions` fallback\n    this.options = this.config.defaultOptions\n\n    if (this.config.addOptions) {\n      this.options = callOrReturn(\n        getExtensionField<AnyConfig['addOptions']>(this, 'addOptions', {\n          name: this.name,\n        }),\n      )\n    }\n\n    this.storage = callOrReturn(\n      getExtensionField<AnyConfig['addStorage']>(this, 'addStorage', {\n        name: this.name,\n        options: this.options,\n      }),\n    ) || {}\n  }\n\n  static create<O = any, S = any>(config: Partial<NodeConfig<O, S>> = {}) {\n    return new Node<O, S>(config)\n  }\n\n  configure(options: Partial<Options> = {}) {\n    // return a new instance so we can use the same extension\n    // with different calls of `configure`\n    const extension = this.extend<Options, Storage>({\n      ...this.config,\n      addOptions: () => {\n        return mergeDeep(this.options as Record<string, any>, options) as Options\n      },\n    })\n\n    // Always preserve the current name\n    extension.name = this.name\n    // Set the parent to be our parent\n    extension.parent = this.parent\n\n    return extension\n  }\n\n  extend<ExtendedOptions = Options, ExtendedStorage = Storage>(\n    extendedConfig: Partial<NodeConfig<ExtendedOptions, ExtendedStorage>> = {},\n  ) {\n    const extension = new Node<ExtendedOptions, ExtendedStorage>(extendedConfig)\n\n    extension.parent = this\n\n    this.child = extension\n\n    extension.name = extendedConfig.name ? extendedConfig.name : extension.parent.name\n\n    if (extendedConfig.defaultOptions && Object.keys(extendedConfig.defaultOptions).length > 0) {\n      console.warn(\n        `[tiptap warn]: BREAKING CHANGE: \"defaultOptions\" is deprecated. Please use \"addOptions\" instead. Found in extension: \"${extension.name}\".`,\n      )\n    }\n\n    extension.options = callOrReturn(\n      getExtensionField<AnyConfig['addOptions']>(extension, 'addOptions', {\n        name: extension.name,\n      }),\n    )\n\n    extension.storage = callOrReturn(\n      getExtensionField<AnyConfig['addStorage']>(extension, 'addStorage', {\n        name: extension.name,\n        options: extension.options,\n      }),\n    )\n\n    return extension\n  }\n}\n", "import { NodeSelection } from '@tiptap/pm/state'\nimport { NodeView as ProseMirrorNodeView, ViewMutationRecord } from '@tiptap/pm/view'\n\nimport { Editor as CoreEditor } from './Editor.js'\nimport { DecorationWithType, NodeViewRendererOptions, NodeViewRendererProps } from './types.js'\nimport { isAndroid } from './utilities/isAndroid.js'\nimport { isiOS } from './utilities/isiOS.js'\n\n/**\n * Node views are used to customize the rendered DOM structure of a node.\n * @see https://tiptap.dev/guide/node-views\n */\nexport class NodeView<\n  Component,\n  NodeEditor extends CoreEditor = CoreEditor,\n  Options extends NodeViewRendererOptions = NodeViewRendererOptions,\n> implements ProseMirrorNodeView {\n  component: Component\n\n  editor: NodeEditor\n\n  options: Options\n\n  extension: NodeViewRendererProps['extension']\n\n  node: NodeViewRendererProps['node']\n\n  decorations: NodeViewRendererProps['decorations']\n\n  innerDecorations: NodeViewRendererProps['innerDecorations']\n\n  view: NodeViewRendererProps['view']\n\n  getPos: NodeViewRendererProps['getPos']\n\n  HTMLAttributes: NodeViewRendererProps['HTMLAttributes']\n\n  isDragging = false\n\n  constructor(component: Component, props: NodeViewRendererProps, options?: Partial<Options>) {\n    this.component = component\n    this.editor = props.editor as NodeEditor\n    this.options = {\n      stopEvent: null,\n      ignoreMutation: null,\n      ...options,\n    } as Options\n    this.extension = props.extension\n    this.node = props.node\n    this.decorations = props.decorations as DecorationWithType[]\n    this.innerDecorations = props.innerDecorations\n    this.view = props.view\n    this.HTMLAttributes = props.HTMLAttributes\n    this.getPos = props.getPos\n    this.mount()\n  }\n\n  mount() {\n    // eslint-disable-next-line\n    return\n  }\n\n  get dom(): HTMLElement {\n    return this.editor.view.dom as HTMLElement\n  }\n\n  get contentDOM(): HTMLElement | null {\n    return null\n  }\n\n  onDragStart(event: DragEvent) {\n    const { view } = this.editor\n    const target = event.target as HTMLElement\n\n    // get the drag handle element\n    // `closest` is not available for text nodes so we may have to use its parent\n    const dragHandle = target.nodeType === 3\n      ? target.parentElement?.closest('[data-drag-handle]')\n      : target.closest('[data-drag-handle]')\n\n    if (!this.dom || this.contentDOM?.contains(target) || !dragHandle) {\n      return\n    }\n\n    let x = 0\n    let y = 0\n\n    // calculate offset for drag element if we use a different drag handle element\n    if (this.dom !== dragHandle) {\n      const domBox = this.dom.getBoundingClientRect()\n      const handleBox = dragHandle.getBoundingClientRect()\n\n      // In React, we have to go through nativeEvent to reach offsetX/offsetY.\n      const offsetX = event.offsetX ?? (event as any).nativeEvent?.offsetX\n      const offsetY = event.offsetY ?? (event as any).nativeEvent?.offsetY\n\n      x = handleBox.x - domBox.x + offsetX\n      y = handleBox.y - domBox.y + offsetY\n    }\n\n    const clonedNode = this.dom.cloneNode(true) as HTMLElement\n\n    event.dataTransfer?.setDragImage(clonedNode, x, y)\n\n    const pos = this.getPos()\n\n    if (typeof pos !== 'number') {\n      return\n    }\n    // we need to tell ProseMirror that we want to move the whole node\n    // so we create a NodeSelection\n    const selection = NodeSelection.create(view.state.doc, pos)\n    const transaction = view.state.tr.setSelection(selection)\n\n    view.dispatch(transaction)\n  }\n\n  stopEvent(event: Event) {\n    if (!this.dom) {\n      return false\n    }\n\n    if (typeof this.options.stopEvent === 'function') {\n      return this.options.stopEvent({ event })\n    }\n\n    const target = event.target as HTMLElement\n    const isInElement = this.dom.contains(target) && !this.contentDOM?.contains(target)\n\n    // any event from child nodes should be handled by ProseMirror\n    if (!isInElement) {\n      return false\n    }\n\n    const isDragEvent = event.type.startsWith('drag')\n    const isDropEvent = event.type === 'drop'\n    const isInput = ['INPUT', 'BUTTON', 'SELECT', 'TEXTAREA'].includes(target.tagName) || target.isContentEditable\n\n    // any input event within node views should be ignored by ProseMirror\n    if (isInput && !isDropEvent && !isDragEvent) {\n      return true\n    }\n\n    const { isEditable } = this.editor\n    const { isDragging } = this\n    const isDraggable = !!this.node.type.spec.draggable\n    const isSelectable = NodeSelection.isSelectable(this.node)\n    const isCopyEvent = event.type === 'copy'\n    const isPasteEvent = event.type === 'paste'\n    const isCutEvent = event.type === 'cut'\n    const isClickEvent = event.type === 'mousedown'\n\n    // ProseMirror tries to drag selectable nodes\n    // even if `draggable` is set to `false`\n    // this fix prevents that\n    if (!isDraggable && isSelectable && isDragEvent && event.target === this.dom) {\n      event.preventDefault()\n    }\n\n    if (isDraggable && isDragEvent && !isDragging && event.target === this.dom) {\n      event.preventDefault()\n      return false\n    }\n\n    // we have to store that dragging started\n    if (isDraggable && isEditable && !isDragging && isClickEvent) {\n      const dragHandle = target.closest('[data-drag-handle]')\n      const isValidDragHandle = dragHandle && (this.dom === dragHandle || this.dom.contains(dragHandle))\n\n      if (isValidDragHandle) {\n        this.isDragging = true\n\n        document.addEventListener(\n          'dragend',\n          () => {\n            this.isDragging = false\n          },\n          { once: true },\n        )\n\n        document.addEventListener(\n          'drop',\n          () => {\n            this.isDragging = false\n          },\n          { once: true },\n        )\n\n        document.addEventListener(\n          'mouseup',\n          () => {\n            this.isDragging = false\n          },\n          { once: true },\n        )\n      }\n    }\n\n    // these events are handled by prosemirror\n    if (\n      isDragging\n      || isDropEvent\n      || isCopyEvent\n      || isPasteEvent\n      || isCutEvent\n      || (isClickEvent && isSelectable)\n    ) {\n      return false\n    }\n\n    return true\n  }\n\n  /**\n   * Called when a DOM [mutation](https://developer.mozilla.org/en-US/docs/Web/API/MutationObserver) or a selection change happens within the view.\n   * @return `false` if the editor should re-read the selection or re-parse the range around the mutation\n   * @return `true` if it can safely be ignored.\n   */\n  ignoreMutation(mutation: ViewMutationRecord) {\n    if (!this.dom || !this.contentDOM) {\n      return true\n    }\n\n    if (typeof this.options.ignoreMutation === 'function') {\n      return this.options.ignoreMutation({ mutation })\n    }\n\n    // a leaf/atom node is like a black box for ProseMirror\n    // and should be fully handled by the node view\n    if (this.node.isLeaf || this.node.isAtom) {\n      return true\n    }\n\n    // ProseMirror should handle any selections\n    if (mutation.type === 'selection') {\n      return false\n    }\n\n    // try to prevent a bug on iOS and Android that will break node views on enter\n    // this is because ProseMirror can’t preventDispatch on enter\n    // this will lead to a re-render of the node view on enter\n    // see: https://github.com/ueberdosis/tiptap/issues/1214\n    // see: https://github.com/ueberdosis/tiptap/issues/2534\n    if (\n      this.dom.contains(mutation.target)\n      && mutation.type === 'childList'\n      && (isiOS() || isAndroid())\n      && this.editor.isFocused\n    ) {\n      const changedNodes = [\n        ...Array.from(mutation.addedNodes),\n        ...Array.from(mutation.removedNodes),\n      ] as HTMLElement[]\n\n      // we’ll check if every changed node is contentEditable\n      // to make sure it’s probably mutated by ProseMirror\n      if (changedNodes.every(node => node.isContentEditable)) {\n        return false\n      }\n    }\n\n    // we will allow mutation contentDOM with attributes\n    // so we can for example adding classes within our node view\n    if (this.contentDOM === mutation.target && mutation.type === 'attributes') {\n      return true\n    }\n\n    // ProseMirror should handle any changes within contentDOM\n    if (this.contentDOM.contains(mutation.target)) {\n      return false\n    }\n\n    return true\n  }\n\n  /**\n   * Update the attributes of the prosemirror node.\n   */\n  updateAttributes(attributes: Record<string, any>): void {\n    this.editor.commands.command(({ tr }) => {\n      const pos = this.getPos()\n\n      if (typeof pos !== 'number') {\n        return false\n      }\n\n      tr.setNodeMarkup(pos, undefined, {\n        ...this.node.attrs,\n        ...attributes,\n      })\n\n      return true\n    })\n  }\n\n  /**\n   * Delete the node.\n   */\n  deleteNode(): void {\n    const from = this.getPos()\n\n    if (typeof from !== 'number') {\n      return\n    }\n    const to = from + this.node.nodeSize\n\n    this.editor.commands.deleteRange({ from, to })\n  }\n}\n", "import { MarkType } from '@tiptap/pm/model'\n\nimport { getMarksBetween } from '../helpers/getMarksBetween.js'\nimport { PasteRule, PasteRuleFinder } from '../PasteRule.js'\nimport { ExtendedRegExpMatchArray } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\n\n/**\n * Build an paste rule that adds a mark when the\n * matched text is pasted into it.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#paste-rules\n */\nexport function markPasteRule(config: {\n  find: PasteRuleFinder\n  type: MarkType\n  getAttributes?:\n    | Record<string, any>\n    | ((match: ExtendedRegExpMatchArray, event: ClipboardEvent) => Record<string, any>)\n    | false\n    | null\n}) {\n  return new PasteRule({\n    find: config.find,\n    handler: ({\n      state, range, match, pasteEvent,\n    }) => {\n      const attributes = callOrReturn(config.getAttributes, undefined, match, pasteEvent)\n\n      if (attributes === false || attributes === null) {\n        return null\n      }\n\n      const { tr } = state\n      const captureGroup = match[match.length - 1]\n      const fullMatch = match[0]\n      let markEnd = range.to\n\n      if (captureGroup) {\n        const startSpaces = fullMatch.search(/\\S/)\n        const textStart = range.from + fullMatch.indexOf(captureGroup)\n        const textEnd = textStart + captureGroup.length\n\n        const excludedMarks = getMarksBetween(range.from, range.to, state.doc)\n          .filter(item => {\n            // @ts-ignore\n            const excluded = item.mark.type.excluded as MarkType[]\n\n            return excluded.find(type => type === config.type && type !== item.mark.type)\n          })\n          .filter(item => item.to > textStart)\n\n        if (excludedMarks.length) {\n          return null\n        }\n\n        if (textEnd < range.to) {\n          tr.delete(textEnd, range.to)\n        }\n\n        if (textStart > range.from) {\n          tr.delete(range.from + startSpaces, textStart)\n        }\n\n        markEnd = range.from + startSpaces + captureGroup.length\n\n        tr.addMark(range.from + startSpaces, markEnd, config.type.create(attributes || {}))\n\n        tr.removeStoredMark(config.type)\n      }\n    },\n  })\n}\n", "// source: https://stackoverflow.com/a/6969486\nexport function escapeForRegEx(string: string): string {\n  return string.replace(/[-/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&')\n}\n", "export function isString(value: any): value is string {\n  return typeof value === 'string'\n}\n", "import { NodeType } from '@tiptap/pm/model'\n\nimport { PasteRule, PasteRuleFinder } from '../PasteRule.js'\nimport { ExtendedRegExpMatchArray, JSONContent } from '../types.js'\nimport { callOrReturn } from '../utilities/index.js'\n\n/**\n * Build an paste rule that adds a node when the\n * matched text is pasted into it.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#paste-rules\n */\nexport function nodePasteRule(config: {\n  find: PasteRuleFinder\n  type: NodeType\n  getAttributes?:\n    | Record<string, any>\n    | ((match: ExtendedRegExpMatchArray, event: ClipboardEvent) => Record<string, any>)\n    | false\n    | null\n  getContent?:\n    | JSONContent[]\n    | ((attrs: Record<string, any>) => JSONContent[])\n    | false\n    | null\n}) {\n  return new PasteRule({\n    find: config.find,\n    handler({\n      match, chain, range, pasteEvent,\n    }) {\n      const attributes = callOrReturn(config.getAttributes, undefined, match, pasteEvent)\n      const content = callOrReturn(config.getContent, undefined, attributes)\n\n      if (attributes === false || attributes === null) {\n        return null\n      }\n\n      const node = { type: config.type.name, attrs: attributes } as JSONContent\n\n      if (content) {\n        node.content = content\n      }\n\n      if (match.input) {\n        chain().deleteRange(range).insertContentAt(range.from, node)\n      }\n    },\n  })\n}\n", "import { PasteRule, PasteRuleFinder } from '../PasteRule.js'\n\n/**\n * Build an paste rule that replaces text when the\n * matched text is pasted into it.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#paste-rules\n */\nexport function textPasteRule(config: {\n  find: PasteRuleFinder,\n  replace: string,\n}) {\n  return new PasteRule({\n    find: config.find,\n    handler: ({ state, range, match }) => {\n      let insert = config.replace\n      let start = range.from\n      const end = range.to\n\n      if (match[1]) {\n        const offset = match[0].lastIndexOf(match[1])\n\n        insert += match[0].slice(offset + match[1].length)\n        start += offset\n\n        const cutOff = start - end\n\n        if (cutOff > 0) {\n          insert = match[0].slice(offset - cutOff, offset) + insert\n          start = end\n        }\n      }\n\n      state.tr.insertText(insert, start, end)\n    },\n  })\n}\n", "import { Transaction } from '@tiptap/pm/state'\n\nexport interface TrackerResult {\n  position: number\n  deleted: boolean\n}\n\nexport class Tracker {\n  transaction: Transaction\n\n  currentStep: number\n\n  constructor(transaction: Transaction) {\n    this.transaction = transaction\n    this.currentStep = this.transaction.steps.length\n  }\n\n  map(position: number): TrackerResult {\n    let deleted = false\n\n    const mappedPosition = this.transaction.steps\n      .slice(this.currentStep)\n      .reduce((newPosition, step) => {\n        const mapResult = step.getMap().mapResult(newPosition)\n\n        if (mapResult.deleted) {\n          deleted = true\n        }\n\n        return mapResult.pos\n      }, position)\n\n    return {\n      position: mappedPosition,\n      deleted,\n    }\n  }\n}\n"], "names": ["<PERSON><PERSON><PERSON>", "DOMSerializer", "run", "Plugin", "Fragment", "keymap", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "liftTarget", "originalCreateParagraphNear", "state", "TextSelection", "originalDeleteSelection", "originalExitCode", "Selection", "ProseMirrorNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ReplaceStep", "ReplaceAroundStep", "originalJoinUp", "originalJoinDown", "originalJoinBackward", "originalJoin<PERSON>orward", "joinPoint", "originalCommand", "originalLift", "originalLiftEmptyBlock", "originalLiftListItem", "originalNewlineInCode", "AllSelection", "originalSelectNodeBackward", "originalSelectNodeForward", "originalSelectParentNode", "originalSelectTextblockEnd", "originalSelectTextblockStart", "transform", "Transform", "Node", "NodeSelection", "setBlockType", "originalSinkListItem", "canSplit", "Slice", "canJoin", "originalWrapIn", "originalWrapInList", "Editor<PERSON><PERSON><PERSON>", "EditorState", "findWrapping"], "mappings": ";;;;;;EAEA;;;;EAIG;EACG,SAAU,oBAAoB,CAAC,MAGpC,EAAA;EACC,IAAA,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM;EACrC,IAAA,IAAI,EAAE,SAAS,EAAE,GAAG,WAAW;EAC/B,IAAA,IAAI,EAAE,GAAG,EAAE,GAAG,WAAW;EACzB,IAAA,IAAI,EAAE,WAAW,EAAE,GAAG,WAAW;MAEjC,OAAO;EACL,QAAA,GAAG,KAAK;UACR,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;UAC9B,gBAAgB,EAAE,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC;UACpD,OAAO,EAAE,KAAK,CAAC,OAAO;UACtB,MAAM,EAAE,KAAK,CAAC,MAAM;UACpB,WAAW,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;UAC1C,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;EAChC,QAAA,IAAI,WAAW,GAAA;EACb,YAAA,OAAO,WAAW;WACnB;EACD,QAAA,IAAI,SAAS,GAAA;EACX,YAAA,OAAO,SAAS;WACjB;EACD,QAAA,IAAI,GAAG,GAAA;EACL,YAAA,OAAO,GAAG;WACX;EACD,QAAA,IAAI,EAAE,GAAA;EACJ,YAAA,SAAS,GAAG,WAAW,CAAC,SAAS;EACjC,YAAA,GAAG,GAAG,WAAW,CAAC,GAAG;EACrB,YAAA,WAAW,GAAG,WAAW,CAAC,WAAW;EAErC,YAAA,OAAO,WAAW;WACnB;OACF;EACH;;QCjCa,cAAc,CAAA;EAOzB,IAAA,WAAA,CAAY,KAA8C,EAAA;EACxD,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM;UAC1B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ;EACxD,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,KAAK;;EAGhC,IAAA,IAAI,cAAc,GAAA;EAChB,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW;;EAG3B,IAAA,IAAI,KAAK,GAAA;UACP,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK;;EAG9C,IAAA,IAAI,QAAQ,GAAA;UACV,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI;EAC3C,QAAA,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;EACvB,QAAA,MAAM,EAAE,EAAE,EAAE,GAAG,KAAK;UACpB,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;UAEjC,OAAO,MAAM,CAAC,WAAW,CACvB,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,KAAI;EAClD,YAAA,MAAM,MAAM,GAAG,CAAC,GAAG,IAAW,KAAI;kBAChC,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,KAAK,CAAC;EAExC,gBAAA,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;EAC1D,oBAAA,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;;EAGnB,gBAAA,OAAO,QAAQ;EACjB,aAAC;EAED,YAAA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;WACtB,CAAC,CAC0B;;EAGhC,IAAA,IAAI,KAAK,GAAA;EACP,QAAA,OAAO,MAAM,IAAI,CAAC,WAAW,EAAE;;EAGjC,IAAA,IAAI,GAAG,GAAA;EACL,QAAA,OAAO,MAAM,IAAI,CAAC,SAAS,EAAE;;EAGxB,IAAA,WAAW,CAAC,OAAqB,EAAE,cAAc,GAAG,IAAI,EAAA;UAC7D,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI;EAC3C,QAAA,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;UACvB,MAAM,SAAS,GAAc,EAAE;EAC/B,QAAA,MAAM,mBAAmB,GAAG,CAAC,CAAC,OAAO;EACrC,QAAA,MAAM,EAAE,GAAG,OAAO,IAAI,KAAK,CAAC,EAAE;UAE9B,MAAM,GAAG,GAAG,MAAK;EACf,YAAA,IACE,CAAC;qBACE;EACA,mBAAA,CAAC,EAAE,CAAC,OAAO,CAAC,iBAAiB;EAC7B,mBAAA,CAAC,IAAI,CAAC,cAAc,EACvB;EACA,gBAAA,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;;EAGnB,YAAA,OAAO,SAAS,CAAC,KAAK,CAAC,QAAQ,IAAI,QAAQ,KAAK,IAAI,CAAC;EACvD,SAAC;EAED,QAAA,MAAM,KAAK,GAAG;cACZ,GAAG,MAAM,CAAC,WAAW,CACnB,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,KAAI;EAClD,gBAAA,MAAM,cAAc,GAAG,CAAC,GAAG,IAAa,KAAI;sBAC1C,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,cAAc,CAAC;sBACjD,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,KAAK,CAAC;EAExC,oBAAA,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;EAExB,oBAAA,OAAO,KAAK;EACd,iBAAC;EAED,gBAAA,OAAO,CAAC,IAAI,EAAE,cAAc,CAAC;EAC/B,aAAC,CAAC,CACH;cACD,GAAG;WAC0B;EAE/B,QAAA,OAAO,KAAK;;EAGP,IAAA,SAAS,CAAC,OAAqB,EAAA;EACpC,QAAA,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,IAAI;UACnC,MAAM,QAAQ,GAAG,KAAK;EACtB,QAAA,MAAM,EAAE,GAAG,OAAO,IAAI,KAAK,CAAC,EAAE;UAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,QAAQ,CAAC;UAC3C,MAAM,iBAAiB,GAAG,MAAM,CAAC,WAAW,CAC1C,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,KAAI;cAClD,OAAO,CAAC,IAAI,EAAE,CAAC,GAAG,IAAa,KAAK,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,CAAC;WACzF,CAAC,CAC0B;UAE9B,OAAO;EACL,YAAA,GAAG,iBAAiB;cACpB,KAAK,EAAE,MAAM,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,QAAQ,CAAC;WAC7B;;EAGX,IAAA,UAAU,CAAC,EAAe,EAAE,cAAc,GAAG,IAAI,EAAA;UACtD,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI;EAC3C,QAAA,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;EAEvB,QAAA,MAAM,KAAK,GAAiB;cAC1B,EAAE;cACF,MAAM;cACN,IAAI;cACJ,KAAK,EAAE,oBAAoB,CAAC;kBAC1B,KAAK;EACL,gBAAA,WAAW,EAAE,EAAE;eAChB,CAAC;EACF,YAAA,QAAQ,EAAE,cAAc,GAAG,MAAM,SAAS,GAAG,SAAS;cACtD,KAAK,EAAE,MAAM,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,cAAc,CAAC;cACjD,GAAG,EAAE,MAAM,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;EAC7B,YAAA,IAAI,QAAQ,GAAA;kBACV,OAAO,MAAM,CAAC,WAAW,CACvB,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,KAAI;EAClD,oBAAA,OAAO,CAAC,IAAI,EAAE,CAAC,GAAG,IAAa,KAAK,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC;mBAC7D,CAAC,CAC0B;eAC/B;WACF;EAED,QAAA,OAAO,KAAK;;EAEf;;QCtIY,YAAY,CAAA;EAAzB,IAAA,WAAA,GAAA;UAEU,IAAS,CAAA,SAAA,GAAqD,EAAE;;MAEjE,EAAE,CAAmC,KAAgB,EAAE,EAAkC,EAAA;UAC9F,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;EAC1B,YAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,EAAE;;UAG5B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;EAE9B,QAAA,OAAO,IAAI;;EAGN,IAAA,IAAI,CAAmC,KAAgB,EAAE,GAAG,IAAgC,EAAA;UACjG,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;UAEvC,IAAI,SAAS,EAAE;EACb,YAAA,SAAS,CAAC,OAAO,CAAC,QAAQ,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;;EAG3D,QAAA,OAAO,IAAI;;MAGN,GAAG,CAAmC,KAAgB,EAAE,EAAmC,EAAA;UAChG,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;UAEvC,IAAI,SAAS,EAAE;cACb,IAAI,EAAE,EAAE;EACN,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,QAAQ,IAAI,QAAQ,KAAK,EAAE,CAAC;;mBAChE;EACL,gBAAA,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;;;EAIhC,QAAA,OAAO,IAAI;;MAGN,IAAI,CAAmC,KAAgB,EAAE,EAAkC,EAAA;EAChG,QAAA,MAAM,MAAM,GAAG,CAAC,GAAG,IAAgC,KAAI;EACrD,YAAA,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;EACvB,YAAA,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;EACtB,SAAC;UAED,OAAO,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;;MAGxB,kBAAkB,GAAA;EACvB,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE;;EAEtB;;EC1DD;;;;;;EAMG;WACa,iBAAiB,CAC/B,SAAuB,EACvB,KAAa,EACb,OAAmD,EAAA;EAGnD,IAAA,IAAI,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,SAAS,IAAI,SAAS,CAAC,MAAM,EAAE;UAC7D,OAAO,iBAAiB,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC;;MAG5D,IAAI,OAAO,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,UAAU,EAAE;UACjD,MAAM,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;EACzC,YAAA,GAAG,OAAO;cACV,MAAM,EAAE,SAAS,CAAC;oBACd,iBAAiB,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO;EACpD,kBAAE,IAAI;EACT,SAAA,CAAC;EAEF,QAAA,OAAO,KAAK;;EAGd,IAAA,OAAO,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC;EAChC;;EC1BM,SAAU,eAAe,CAAC,UAAsB,EAAA;EACpD,IAAA,MAAM,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,KAAK,WAAW,CAAgB;EACpG,IAAA,MAAM,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,KAAK,MAAM,CAAW;EAC1F,IAAA,MAAM,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,KAAK,MAAM,CAAW;MAE1F,OAAO;UACL,cAAc;UACd,cAAc;UACd,cAAc;OACf;EACH;;ECJA;;;EAGG;EACG,SAAU,2BAA2B,CAAC,UAAsB,EAAA;MAChE,MAAM,mBAAmB,GAAyB,EAAE;MACpD,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,GAAG,eAAe,CAAC,UAAU,CAAC;MACtE,MAAM,qBAAqB,GAAG,CAAC,GAAG,cAAc,EAAE,GAAG,cAAc,CAAC;EACpE,IAAA,MAAM,gBAAgB,GAAwB;EAC5C,QAAA,OAAO,EAAE,IAAI;EACb,QAAA,QAAQ,EAAE,IAAI;EACd,QAAA,UAAU,EAAE,IAAI;EAChB,QAAA,SAAS,EAAE,IAAI;EACf,QAAA,WAAW,EAAE,IAAI;EACjB,QAAA,UAAU,EAAE,KAAK;OAClB;EAED,IAAA,UAAU,CAAC,OAAO,CAAC,SAAS,IAAG;EAC7B,QAAA,MAAM,OAAO,GAAG;cACd,IAAI,EAAE,SAAS,CAAC,IAAI;cACpB,OAAO,EAAE,SAAS,CAAC,OAAO;cAC1B,OAAO,EAAE,SAAS,CAAC,OAAO;EAC1B,YAAA,UAAU,EAAE,qBAAqB;WAClC;UAED,MAAM,mBAAmB,GAAG,iBAAiB,CAC3C,SAAS,EACT,qBAAqB,EACrB,OAAO,CACR;UAED,IAAI,CAAC,mBAAmB,EAAE;cACxB;;EAGF,QAAA,MAAM,gBAAgB,GAAG,mBAAmB,EAAE;EAE9C,QAAA,gBAAgB,CAAC,OAAO,CAAC,eAAe,IAAG;EACzC,YAAA,eAAe,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,IAAG;kBACnC;EACG,qBAAA,OAAO,CAAC,eAAe,CAAC,UAAU;uBAClC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,SAAS,CAAC,KAAI;sBAC7B,mBAAmB,CAAC,IAAI,CAAC;0BACvB,IAAI;0BACJ,IAAI;EACJ,wBAAA,SAAS,EAAE;EACT,4BAAA,GAAG,gBAAgB;EACnB,4BAAA,GAAG,SAAS;EACb,yBAAA;EACF,qBAAA,CAAC;EACJ,iBAAC,CAAC;EACN,aAAC,CAAC;EACJ,SAAC,CAAC;EACJ,KAAC,CAAC;EAEF,IAAA,qBAAqB,CAAC,OAAO,CAAC,SAAS,IAAG;EACxC,QAAA,MAAM,OAAO,GAAG;cACd,IAAI,EAAE,SAAS,CAAC,IAAI;cACpB,OAAO,EAAE,SAAS,CAAC,OAAO;cAC1B,OAAO,EAAE,SAAS,CAAC,OAAO;WAC3B;UAED,MAAM,aAAa,GAAG,iBAAiB,CACrC,SAAS,EACT,eAAe,EACf,OAAO,CACR;UAED,IAAI,CAAC,aAAa,EAAE;cAClB;;;EAIF,QAAA,MAAM,UAAU,GAAG,aAAa,EAAgB;UAEhD;eACG,OAAO,CAAC,UAAU;eAClB,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,SAAS,CAAC,KAAI;EAC7B,YAAA,MAAM,UAAU,GAAG;EACjB,gBAAA,GAAG,gBAAgB;EACnB,gBAAA,GAAG,SAAS;eACb;EAED,YAAA,IAAI,QAAO,UAAU,KAAA,IAAA,IAAV,UAAU,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAV,UAAU,CAAE,OAAO,CAAA,KAAK,UAAU,EAAE;EAC7C,gBAAA,UAAU,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,EAAE;;cAG3C,IAAI,CAAA,UAAU,KAAV,IAAA,IAAA,UAAU,uBAAV,UAAU,CAAE,UAAU,KAAI,CAAA,UAAU,KAAV,IAAA,IAAA,UAAU,uBAAV,UAAU,CAAE,OAAO,MAAK,SAAS,EAAE;kBAC/D,OAAO,UAAU,CAAC,OAAO;;cAG3B,mBAAmB,CAAC,IAAI,CAAC;kBACvB,IAAI,EAAE,SAAS,CAAC,IAAI;kBACpB,IAAI;EACJ,gBAAA,SAAS,EAAE,UAAU;EACtB,aAAA,CAAC;EACJ,SAAC,CAAC;EACN,KAAC,CAAC;EAEF,IAAA,OAAO,mBAAmB;EAC5B;;EC7GgB,SAAA,WAAW,CAAC,UAA6B,EAAE,MAAc,EAAA;EACvE,IAAA,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;UAClC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;EAC7B,YAAA,MAAM,KAAK,CACT,CAAA,6BAAA,EAAgC,UAAU,CAAA,yCAAA,CAA2C,CACtF;;EAGH,QAAA,OAAO,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC;;EAGjC,IAAA,OAAO,UAAU;EACnB;;ECdgB,SAAA,eAAe,CAAC,GAAG,OAA8B,EAAA;EAC/D,IAAA,OAAO;WACJ,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI;EACrB,SAAA,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,KAAI;EACtB,QAAA,MAAM,gBAAgB,GAAG,EAAE,GAAG,KAAK,EAAE;EAErC,QAAA,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAI;EAC5C,YAAA,MAAM,MAAM,GAAG,gBAAgB,CAAC,GAAG,CAAC;cAEpC,IAAI,CAAC,MAAM,EAAE;EACX,gBAAA,gBAAgB,CAAC,GAAG,CAAC,GAAG,KAAK;kBAE7B;;EAGF,YAAA,IAAI,GAAG,KAAK,OAAO,EAAE;EACnB,gBAAA,MAAM,YAAY,GAAa,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;kBACpE,MAAM,eAAe,GAAa,gBAAgB,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;EAE/F,gBAAA,MAAM,aAAa,GAAG,YAAY,CAAC,MAAM,CACvC,UAAU,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,UAAU,CAAC,CACpD;EAED,gBAAA,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,eAAe,EAAE,GAAG,aAAa,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;;EACnE,iBAAA,IAAI,GAAG,KAAK,OAAO,EAAE;EAC1B,gBAAA,MAAM,SAAS,GAAa,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,KAAa,KAAK,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE;EAC9G,gBAAA,MAAM,cAAc,GAAa,gBAAgB,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,KAAa,KAAK,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE;EAEnJ,gBAAA,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAkB;EAE1C,gBAAA,cAAc,CAAC,OAAO,CAAC,KAAK,IAAG;sBAC7B,MAAM,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;EAEjE,oBAAA,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC;EAC7B,iBAAC,CAAC;EAEF,gBAAA,SAAS,CAAC,OAAO,CAAC,KAAK,IAAG;sBACxB,MAAM,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;EAEjE,oBAAA,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC;EAC7B,iBAAC,CAAC;EAEF,gBAAA,gBAAgB,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,KAAK,GAAG,QAAQ,CAAA,EAAA,EAAK,GAAG,CAAA,CAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;mBAC5G;EACL,gBAAA,gBAAgB,CAAC,GAAG,CAAC,GAAG,KAAK;;EAEjC,SAAC,CAAC;EAEF,QAAA,OAAO,gBAAgB;OACxB,EAAE,EAAE,CAAC;EACV;;EC7CgB,SAAA,qBAAqB,CACnC,UAAuB,EACvB,mBAAyC,EAAA;EAEzC,IAAA,OAAO;EACJ,SAAA,MAAM,CACL,SAAS,IAAI,SAAS,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI,CAAC,IAAI;WAErD,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ;WACtC,GAAG,CAAC,IAAI,IAAG;EACV,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;cAC9B,OAAO;EACL,gBAAA,CAAC,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;eACzC;;EAGH,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE;EAC1D,KAAC;EACA,SAAA,MAAM,CAAC,CAAC,UAAU,EAAE,SAAS,KAAK,eAAe,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC;EAClF;;ECxBA;EACM,SAAU,UAAU,CAAC,KAAU,EAAA;EACnC,IAAA,OAAO,OAAO,KAAK,KAAK,UAAU;EACpC;;ECAA;;;;;;EAMG;EACG,SAAU,YAAY,CAAI,KAAQ,EAAE,OAAe,GAAA,SAAS,EAAE,GAAG,KAAY,EAAA;EACjF,IAAA,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;UACrB,IAAI,OAAO,EAAE;cACX,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,CAAC;;EAGtC,QAAA,OAAO,KAAK,CAAC,GAAG,KAAK,CAAC;;EAGxB,IAAA,OAAO,KAA2B;EACpC;;ECpBgB,SAAA,aAAa,CAAC,KAAK,GAAG,EAAE,EAAA;EACtC,IAAA,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,WAAW,KAAK,MAAM;EACxE;;ECFM,SAAU,UAAU,CAAC,KAAU,EAAA;EACnC,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;EAC7B,QAAA,OAAO,KAAK;;EAGd,IAAA,IAAI,KAAK,CAAC,KAAK,CAAC,sBAAsB,CAAC,EAAE;EACvC,QAAA,OAAO,MAAM,CAAC,KAAK,CAAC;;EAGtB,IAAA,IAAI,KAAK,KAAK,MAAM,EAAE;EACpB,QAAA,OAAO,IAAI;;EAGb,IAAA,IAAI,KAAK,KAAK,OAAO,EAAE;EACrB,QAAA,OAAO,KAAK;;EAGd,IAAA,OAAO,KAAK;EACd;;ECbA;;;;;EAKG;EACa,SAAA,oCAAoC,CAClD,SAAoB,EACpB,mBAAyC,EAAA;EAEzC,IAAA,IAAI,OAAO,IAAI,SAAS,EAAE;EACxB,QAAA,OAAO,SAAS;;MAGlB,OAAO;EACL,QAAA,GAAG,SAAS;EACZ,QAAA,QAAQ,EAAE,CAAC,IAAiB,KAAI;cAC9B,MAAM,aAAa,GAAG,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK;EAErF,YAAA,IAAI,aAAa,KAAK,KAAK,EAAE;EAC3B,gBAAA,OAAO,KAAK;;cAGd,MAAM,aAAa,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,KAAI;EAC/D,gBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;wBACzB,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI;EAC/B,sBAAE,UAAU,CAAC,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;kBAE9C,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;EACzC,oBAAA,OAAO,KAAK;;kBAGd,OAAO;EACL,oBAAA,GAAG,KAAK;EACR,oBAAA,CAAC,IAAI,CAAC,IAAI,GAAG,KAAK;mBACnB;eACF,EAAE,EAAE,CAAC;EAEN,YAAA,OAAO,EAAE,GAAG,aAAa,EAAE,GAAG,aAAa,EAAE;WAC9C;OACF;EACH;;EChCA,SAAS,iBAAiB,CAAI,IAAO,EAAA;MACnC,OAAO,MAAM,CAAC,WAAW;;EAEvB,IAAA,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAI;UAC3C,IAAI,GAAG,KAAK,OAAO,IAAI,aAAa,CAAC,KAA2B,CAAC,EAAE;EACjE,YAAA,OAAO,KAAK;;EAGd,QAAA,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;OAC7C,CAAC,CACE;EACR;EAEA;;;;;EAKG;EACa,SAAA,6BAA6B,CAAC,UAAsB,EAAE,MAAe,EAAA;;EACnF,IAAA,MAAM,aAAa,GAAG,2BAA2B,CAAC,UAAU,CAAC;MAC7D,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,GAAG,eAAe,CAAC,UAAU,CAAC;MACtE,MAAM,OAAO,GAAG,CAAA,EAAA,GAAA,cAAc,CAAC,IAAI,CAAC,SAAS,IAAI,iBAAiB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,IAAI;EAE/F,IAAA,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAC9B,cAAc,CAAC,GAAG,CAAC,SAAS,IAAG;EAC7B,QAAA,MAAM,mBAAmB,GAAG,aAAa,CAAC,MAAM,CAC9C,SAAS,IAAI,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC/C;EACD,QAAA,MAAM,OAAO,GAAG;cACd,IAAI,EAAE,SAAS,CAAC,IAAI;cACpB,OAAO,EAAE,SAAS,CAAC,OAAO;cAC1B,OAAO,EAAE,SAAS,CAAC,OAAO;cAC1B,MAAM;WACP;UAED,MAAM,eAAe,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC,KAAI;cACtD,MAAM,gBAAgB,GAAG,iBAAiB,CACxC,CAAC,EACD,kBAAkB,EAClB,OAAO,CACR;cAED,OAAO;EACL,gBAAA,GAAG,MAAM;EACT,gBAAA,IAAI,gBAAgB,GAAG,gBAAgB,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;eACzD;WACF,EAAE,EAAE,CAAC;UAEN,MAAM,MAAM,GAAa,iBAAiB,CAAC;EACzC,YAAA,GAAG,eAAe;cAClB,OAAO,EAAE,YAAY,CACnB,iBAAiB,CAAwB,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC,CACxE;cACD,KAAK,EAAE,YAAY,CAAC,iBAAiB,CAAsB,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;cACxF,KAAK,EAAE,YAAY,CAAC,iBAAiB,CAAsB,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;cACxF,MAAM,EAAE,YAAY,CAAC,iBAAiB,CAAuB,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;cAC3F,IAAI,EAAE,YAAY,CAAC,iBAAiB,CAAqB,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;cACrF,UAAU,EAAE,YAAY,CACtB,iBAAiB,CAA2B,SAAS,EAAE,YAAY,EAAE,OAAO,CAAC,CAC9E;cACD,SAAS,EAAE,YAAY,CACrB,iBAAiB,CAA0B,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC,CAC5E;cACD,IAAI,EAAE,YAAY,CAAC,iBAAiB,CAAqB,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;cACrF,UAAU,EAAE,YAAY,CAAC,iBAAiB,CAA2B,SAAS,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;cACvG,oBAAoB,EAAE,YAAY,CAAC,iBAAiB,CAAqC,SAAS,EAAE,sBAAsB,EAAE,OAAO,CAAC,CAAC;cACrI,QAAQ,EAAE,YAAY,CACpB,iBAAiB,CAAyB,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC,CAC1E;cACD,SAAS,EAAE,YAAY,CACrB,iBAAiB,CAA0B,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC,CAC5E;cACD,KAAK,EAAE,MAAM,CAAC,WAAW,CACvB,mBAAmB,CAAC,GAAG,CAAC,kBAAkB,IAAG;;kBAC3C,OAAO,CAAC,kBAAkB,CAAC,IAAI,EAAE,EAAE,OAAO,EAAE,CAAA,EAAA,GAAA,kBAAkB,aAAlB,kBAAkB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAlB,kBAAkB,CAAE,SAAS,0CAAE,OAAO,EAAE,CAAC;EACvF,aAAC,CAAC,CACH;EACF,SAAA,CAAC;EAEF,QAAA,MAAM,SAAS,GAAG,YAAY,CAC5B,iBAAiB,CAA0B,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC,CAC5E;UAED,IAAI,SAAS,EAAE;EACb,YAAA,MAAM,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,IAAI,oCAAoC,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAmB;;UAGtI,MAAM,UAAU,GAAG,iBAAiB,CAClC,SAAS,EACT,YAAY,EACZ,OAAO,CACR;UAED,IAAI,UAAU,EAAE;cACd,MAAM,CAAC,KAAK,GAAG,IAAI,IAAI,UAAU,CAAC;kBAChC,IAAI;EACJ,gBAAA,cAAc,EAAE,qBAAqB,CAAC,IAAI,EAAE,mBAAmB,CAAC;EACjE,aAAA,CAAC;;UAGJ,MAAM,UAAU,GAAG,iBAAiB,CAClC,SAAS,EACT,YAAY,EACZ,OAAO,CACR;UAED,IAAI,UAAU,EAAE;EACd,YAAA,MAAM,CAAC,MAAM,GAAG,UAAU;;EAG5B,QAAA,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC;OAChC,CAAC,CACH;EAED,IAAA,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAC9B,cAAc,CAAC,GAAG,CAAC,SAAS,IAAG;EAC7B,QAAA,MAAM,mBAAmB,GAAG,aAAa,CAAC,MAAM,CAC9C,SAAS,IAAI,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC/C;EACD,QAAA,MAAM,OAAO,GAAG;cACd,IAAI,EAAE,SAAS,CAAC,IAAI;cACpB,OAAO,EAAE,SAAS,CAAC,OAAO;cAC1B,OAAO,EAAE,SAAS,CAAC,OAAO;cAC1B,MAAM;WACP;UAED,MAAM,eAAe,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC,KAAI;cACtD,MAAM,gBAAgB,GAAG,iBAAiB,CACxC,CAAC,EACD,kBAAkB,EAClB,OAAO,CACR;cAED,OAAO;EACL,gBAAA,GAAG,MAAM;EACT,gBAAA,IAAI,gBAAgB,GAAG,gBAAgB,CAAC,SAAgB,CAAC,GAAG,EAAE,CAAC;eAChE;WACF,EAAE,EAAE,CAAC;UAEN,MAAM,MAAM,GAAa,iBAAiB,CAAC;EACzC,YAAA,GAAG,eAAe;cAClB,SAAS,EAAE,YAAY,CACrB,iBAAiB,CAA0B,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC,CAC5E;cACD,QAAQ,EAAE,YAAY,CACpB,iBAAiB,CAAyB,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC,CAC1E;cACD,KAAK,EAAE,YAAY,CAAC,iBAAiB,CAAsB,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;cACxF,QAAQ,EAAE,YAAY,CACpB,iBAAiB,CAAyB,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC,CAC1E;cACD,IAAI,EAAE,YAAY,CAAC,iBAAiB,CAAqB,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;cACrF,KAAK,EAAE,MAAM,CAAC,WAAW,CACvB,mBAAmB,CAAC,GAAG,CAAC,kBAAkB,IAAG;;kBAC3C,OAAO,CAAC,kBAAkB,CAAC,IAAI,EAAE,EAAE,OAAO,EAAE,CAAA,EAAA,GAAA,kBAAkB,aAAlB,kBAAkB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAlB,kBAAkB,CAAE,SAAS,0CAAE,OAAO,EAAE,CAAC;EACvF,aAAC,CAAC,CACH;EACF,SAAA,CAAC;EAEF,QAAA,MAAM,SAAS,GAAG,YAAY,CAC5B,iBAAiB,CAA0B,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC,CAC5E;UAED,IAAI,SAAS,EAAE;EACb,YAAA,MAAM,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,IAAI,oCAAoC,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC;;UAGpH,MAAM,UAAU,GAAG,iBAAiB,CAClC,SAAS,EACT,YAAY,EACZ,OAAO,CACR;UAED,IAAI,UAAU,EAAE;cACd,MAAM,CAAC,KAAK,GAAG,IAAI,IAAI,UAAU,CAAC;kBAChC,IAAI;EACJ,gBAAA,cAAc,EAAE,qBAAqB,CAAC,IAAI,EAAE,mBAAmB,CAAC;EACjE,aAAA,CAAC;;EAGJ,QAAA,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC;OAChC,CAAC,CACH;MAED,OAAO,IAAIA,YAAM,CAAC;UAChB,OAAO;UACP,KAAK;UACL,KAAK;EACN,KAAA,CAAC;EACJ;;EC1MA;;;;;EAKG;EACa,SAAA,mBAAmB,CAAC,IAAY,EAAE,MAAc,EAAA;EAC9D,IAAA,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI;EACzD;;ECRgB,SAAA,uBAAuB,CAAC,SAAuB,EAAE,OAAoB,EAAA;EACnF,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;EAC1B,QAAA,OAAO,OAAO,CAAC,IAAI,CAAC,gBAAgB,IAAG;EACrC,YAAA,MAAM,IAAI,GAAG,OAAO,gBAAgB,KAAK;EACvC,kBAAE;EACF,kBAAE,gBAAgB,CAAC,IAAI;EAEzB,YAAA,OAAO,IAAI,KAAK,SAAS,CAAC,IAAI;EAChC,SAAC,CAAC;;EAGJ,IAAA,OAAO,OAAO;EAChB;;ECZgB,SAAA,mBAAmB,CAAC,QAAkB,EAAE,MAAc,EAAA;EACpE,IAAA,MAAM,gBAAgB,GAAGC,mBAAa,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,iBAAiB,CAAC,QAAQ,CAAC;MAErF,MAAM,iBAAiB,GAAG,QAAQ,CAAC,cAAc,CAAC,kBAAkB,EAAE;MACtE,MAAM,SAAS,GAAG,iBAAiB,CAAC,aAAa,CAAC,KAAK,CAAC;EAExD,IAAA,SAAS,CAAC,WAAW,CAAC,gBAAgB,CAAC;MAEvC,OAAO,SAAS,CAAC,SAAS;EAC5B;;ECTA;;;;;EAKG;AACU,QAAA,uBAAuB,GAAG,CAAC,KAAkB,EAAE,QAAQ,GAAG,GAAG,KAAI;MAC5E,IAAI,UAAU,GAAG,EAAE;EAEnB,IAAA,MAAM,WAAW,GAAG,KAAK,CAAC,YAAY;MAEtC,KAAK,CAAC,MAAM,CAAC,YAAY,CACvB,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,GAAG,QAAQ,CAAC,EACnC,WAAW,EACX,CAAC,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,KAAI;;EAC3B,QAAA,MAAM,KAAK,GAAG,CAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,IAAI,CAAC,IAAI,EAAC,MAAM,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,EAAA;cACpC,IAAI;cACJ,GAAG;cACH,MAAM;cACN,KAAK;WACN,CAAC;EACG,eAAA,IAAI,CAAC;EACL,eAAA,QAAQ;EAEb,QAAA,UAAU,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,GAAG,GAAG,CAAC,CAAC;EACpG,KAAC,CACF;EAED,IAAA,OAAO,UAAU;EACnB;;EC/BM,SAAU,QAAQ,CAAC,KAAU,EAAA;EACjC,IAAA,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,iBAAiB;EACpE;;QCyBa,SAAS,CAAA;EAYpB,IAAA,WAAA,CAAY,MAUX,EAAA;EACC,QAAA,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;EACvB,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO;;EAEhC;EAED,MAAM,uBAAuB,GAAG,CAC9B,IAAY,EACZ,IAAqB,KACc;EACnC,IAAA,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;EAClB,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;;EAGxB,IAAA,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC;MAEjC,IAAI,CAAC,cAAc,EAAE;EACnB,QAAA,OAAO,IAAI;;EAGb,IAAA,MAAM,MAAM,GAA6B,CAAC,cAAc,CAAC,IAAI,CAAC;EAE9D,IAAA,MAAM,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK;EACnC,IAAA,MAAM,CAAC,KAAK,GAAG,IAAI;EACnB,IAAA,MAAM,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI;EAEjC,IAAA,IAAI,cAAc,CAAC,WAAW,EAAE;EAC9B,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE;EAC7D,YAAA,OAAO,CAAC,IAAI,CACV,oFAAoF,CACrF;;EAGH,QAAA,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;;EAGzC,IAAA,OAAO,MAAM;EACf,CAAC;EAED,SAASC,KAAG,CAAC,MAOZ,EAAA;;EACC,IAAA,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,GACtC,GAAG,MAAM;EACV,IAAA,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;EAEvB,IAAA,IAAI,IAAI,CAAC,SAAS,EAAE;EAClB,QAAA,OAAO,KAAK;;EAGd,IAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC;EAE1C,IAAA;;EAEE,IAAA,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;;EAEpB,WAAA,CAAC,EAAC,CAAA,EAAA,IAAC,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,SAAS,CAAC,0CAAE,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,EACnF;EACA,QAAA,OAAO,KAAK;;MAGd,IAAI,OAAO,GAAG,KAAK;MAEnB,MAAM,UAAU,GAAG,uBAAuB,CAAC,KAAK,CAAC,GAAG,IAAI;EAExD,IAAA,KAAK,CAAC,OAAO,CAAC,IAAI,IAAG;UACnB,IAAI,OAAO,EAAE;cACX;;UAGF,MAAM,KAAK,GAAG,uBAAuB,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC;UAE5D,IAAI,CAAC,KAAK,EAAE;cACV;;EAGF,QAAA,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE;UACxB,MAAM,KAAK,GAAG,oBAAoB,CAAC;cACjC,KAAK,EAAE,IAAI,CAAC,KAAK;EACjB,YAAA,WAAW,EAAE,EAAE;EAChB,SAAA,CAAC;EACF,QAAA,MAAM,KAAK,GAAG;EACZ,YAAA,IAAI,EAAE,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;cAC5C,EAAE;WACH;UAED,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,IAAI,cAAc,CAAC;cAClD,MAAM;cACN,KAAK;EACN,SAAA,CAAC;EAEF,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;cAC3B,KAAK;cACL,KAAK;cACL,KAAK;cACL,QAAQ;cACR,KAAK;cACL,GAAG;EACJ,SAAA,CAAC;;UAGF,IAAI,OAAO,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE;cACxC;;;;EAKF,QAAA,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE;EACjB,YAAA,SAAS,EAAE,EAAE;cACb,IAAI;cACJ,EAAE;cACF,IAAI;EACL,SAAA,CAAC;EAEF,QAAA,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;UACjB,OAAO,GAAG,IAAI;EAChB,KAAC,CAAC;EAEF,IAAA,OAAO,OAAO;EAChB;EAEA;;;;EAIG;EACG,SAAU,gBAAgB,CAAC,KAA6C,EAAA;EAC5E,IAAA,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK;EAC/B,IAAA,MAAM,MAAM,GAAG,IAAIC,YAAM,CAAC;EACxB,QAAA,KAAK,EAAE;cACL,IAAI,GAAA;EACF,gBAAA,OAAO,IAAI;eACZ;EACD,YAAA,KAAK,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,EAAA;kBACnB,MAAM,MAAM,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC;kBAEjC,IAAI,MAAM,EAAE;EACV,oBAAA,OAAO,MAAM;;;kBAIf,MAAM,kBAAkB,GAAG,EAAE,CAAC,OAAO,CAAC,iBAAiB,CAKlD;EACL,gBAAA,MAAM,gBAAgB,GAAG,CAAC,CAAC,kBAAkB;kBAE7C,IAAI,gBAAgB,EAAE;sBACpB,UAAU,CAAC,MAAK;EACd,wBAAA,IAAI,EAAE,IAAI,EAAE,GAAG,kBAAkB;EAEjC,wBAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;8BAC5B,IAAI,GAAG,IAAc;;+BAChB;EACL,4BAAA,IAAI,GAAG,mBAAmB,CAACC,cAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC;;EAG/D,wBAAA,MAAM,EAAE,IAAI,EAAE,GAAG,kBAAkB;EACnC,wBAAA,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM;EAE7B,wBAAAF,KAAG,CAAC;8BACF,MAAM;8BACN,IAAI;8BACJ,EAAE;8BACF,IAAI;8BACJ,KAAK;8BACL,MAAM;EACP,yBAAA,CAAC;EACJ,qBAAC,CAAC;;EAGJ,gBAAA,OAAO,EAAE,CAAC,YAAY,IAAI,EAAE,CAAC,UAAU,GAAG,IAAI,GAAG,IAAI;eACtD;EACF,SAAA;EAED,QAAA,KAAK,EAAE;EACL,YAAA,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAA;EAClC,gBAAA,OAAOA,KAAG,CAAC;sBACT,MAAM;sBACN,IAAI;sBACJ,EAAE;sBACF,IAAI;sBACJ,KAAK;sBACL,MAAM;EACP,iBAAA,CAAC;eACH;EAED,YAAA,eAAe,EAAE;kBACf,cAAc,EAAE,IAAI,IAAG;sBACrB,UAAU,CAAC,MAAK;0BACd,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,SAA0B;0BAEzD,IAAI,OAAO,EAAE;EACX,4BAAAA,KAAG,CAAC;kCACF,MAAM;kCACN,IAAI,EAAE,OAAO,CAAC,GAAG;kCACjB,EAAE,EAAE,OAAO,CAAC,GAAG;EACf,gCAAA,IAAI,EAAE,EAAE;kCACR,KAAK;kCACL,MAAM;EACP,6BAAA,CAAC;;EAEN,qBAAC,CAAC;EAEF,oBAAA,OAAO,KAAK;mBACb;EACF,aAAA;;;cAID,aAAa,CAAC,IAAI,EAAE,KAAK,EAAA;EACvB,gBAAA,IAAI,KAAK,CAAC,GAAG,KAAK,OAAO,EAAE;EACzB,oBAAA,OAAO,KAAK;;kBAGd,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,SAA0B;kBAEzD,IAAI,OAAO,EAAE;EACX,oBAAA,OAAOA,KAAG,CAAC;0BACT,MAAM;0BACN,IAAI,EAAE,OAAO,CAAC,GAAG;0BACjB,EAAE,EAAE,OAAO,CAAC,GAAG;EACf,wBAAA,IAAI,EAAE,IAAI;0BACV,KAAK;0BACL,MAAM;EACP,qBAAA,CAAC;;EAGJ,gBAAA,OAAO,KAAK;eACb;EACF,SAAA;;EAGD,QAAA,YAAY,EAAE,IAAI;EACnB,KAAA,CAAW;EAEZ,IAAA,OAAO,MAAM;EACf;;ECtSA;EAEA,SAAS,OAAO,CAAC,KAAU,EAAA;EACzB,IAAA,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC3D;EAEM,SAAU,aAAa,CAAC,KAAU,EAAA;EACtC,IAAA,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE;EAC/B,QAAA,OAAO,KAAK;;EAGd,IAAA,OAAO,KAAK,CAAC,WAAW,KAAK,MAAM,IAAI,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,SAAS;EAC1F;;ECVgB,SAAA,SAAS,CAAC,MAA2B,EAAE,MAA2B,EAAA;EAChF,IAAA,MAAM,MAAM,GAAG,EAAE,GAAG,MAAM,EAAE;MAE5B,IAAI,aAAa,CAAC,MAAM,CAAC,IAAI,aAAa,CAAC,MAAM,CAAC,EAAE;UAClD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,GAAG,IAAG;EAChC,YAAA,IAAI,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;EAC5D,gBAAA,MAAM,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;;mBAC5C;kBACL,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC;;EAE7B,SAAC,CAAC;;EAGJ,IAAA,OAAO,MAAM;EACf;;ECmgBA;;;EAGG;QACU,IAAI,CAAA;EAkBf,IAAA,WAAA,CAAY,SAAgD,EAAE,EAAA;UAjB9D,IAAI,CAAA,IAAA,GAAG,MAAM;UAEb,IAAI,CAAA,IAAA,GAAG,MAAM;UAEb,IAAM,CAAA,MAAA,GAAgB,IAAI;UAE1B,IAAK,CAAA,KAAA,GAAgB,IAAI;EAMzB,QAAA,IAAA,CAAA,MAAM,GAAe;cACnB,IAAI,EAAE,IAAI,CAAC,IAAI;EACf,YAAA,cAAc,EAAE,EAAE;WACnB;UAGC,IAAI,CAAC,MAAM,GAAG;cACZ,GAAG,IAAI,CAAC,MAAM;EACd,YAAA,GAAG,MAAM;WACV;UAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;EAE5B,QAAA,IAAI,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;cAC1E,OAAO,CAAC,IAAI,CACV,CAAA,sHAAA,EAAyH,IAAI,CAAC,IAAI,CAAI,EAAA,CAAA,CACvI;;;UAIH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc;EAEzC,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;cAC1B,IAAI,CAAC,OAAO,GAAG,YAAY,CACzB,iBAAiB,CAA0B,IAAI,EAAE,YAAY,EAAE;kBAC7D,IAAI,EAAE,IAAI,CAAC,IAAI;EAChB,aAAA,CAAC,CACH;;UAGH,IAAI,CAAC,OAAO,GAAG,YAAY,CACzB,iBAAiB,CAA0B,IAAI,EAAE,YAAY,EAAE;cAC7D,IAAI,EAAE,IAAI,CAAC,IAAI;cACf,OAAO,EAAE,IAAI,CAAC,OAAO;WACtB,CAAC,CACH,IAAI,EAAE;;EAGT,IAAA,OAAO,MAAM,CAAmB,MAAA,GAAoC,EAAE,EAAA;EACpE,QAAA,OAAO,IAAI,IAAI,CAAO,MAAM,CAAC;;MAG/B,SAAS,CAAC,UAA4B,EAAE,EAAA;;;EAGtC,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAmB;cAC9C,GAAG,IAAI,CAAC,MAAM;cACd,UAAU,EAAE,MAAK;kBACf,OAAO,SAAS,CAAC,IAAI,CAAC,OAA8B,EAAE,OAAO,CAAY;eAC1E;EACF,SAAA,CAAC;;EAGF,QAAA,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;;EAE1B,QAAA,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;EAE9B,QAAA,OAAO,SAAS;;MAGlB,MAAM,CACJ,iBAAwE,EAAE,EAAA;EAE1E,QAAA,MAAM,SAAS,GAAG,IAAI,IAAI,CAAmC,cAAc,CAAC;EAE5E,QAAA,SAAS,CAAC,MAAM,GAAG,IAAI;EAEvB,QAAA,IAAI,CAAC,KAAK,GAAG,SAAS;UAEtB,SAAS,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI;EAElF,QAAA,IAAI,cAAc,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;cAC1F,OAAO,CAAC,IAAI,CACV,CAAA,sHAAA,EAAyH,SAAS,CAAC,IAAI,CAAI,EAAA,CAAA,CAC5I;;UAGH,SAAS,CAAC,OAAO,GAAG,YAAY,CAC9B,iBAAiB,CAA0B,SAAS,EAAE,YAAY,EAAE;cAClE,IAAI,EAAE,SAAS,CAAC,IAAI;EACrB,SAAA,CAAC,CACH;UAED,SAAS,CAAC,OAAO,GAAG,YAAY,CAC9B,iBAAiB,CAA0B,SAAS,EAAE,YAAY,EAAE;cAClE,IAAI,EAAE,SAAS,CAAC,IAAI;cACpB,OAAO,EAAE,SAAS,CAAC,OAAO;EAC3B,SAAA,CAAC,CACH;EAED,QAAA,OAAO,SAAS;;EAGlB,IAAA,OAAO,UAAU,CAAC,EAAE,MAAM,EAAE,IAAI,EAAkC,EAAA;EAChE,QAAA,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM,CAAC,KAAK;UAC3B,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK;UAC/C,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,KAAK,UAAU,CAAC,GAAG,EAAE;UAEnD,IAAI,OAAO,EAAE;EACX,YAAA,MAAM,YAAY,GAAG,UAAU,CAAC,KAAK,EAAE;cACvC,MAAM,QAAQ,GAAG,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAA,CAAC,KAAA,IAAA,IAAD,CAAC,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAD,CAAC,CAAE,IAAI,CAAC,IAAI,MAAK,IAAI,CAAC,IAAI,CAAC;cAErE,IAAI,CAAC,QAAQ,EAAE;EACb,gBAAA,OAAO,KAAK;;cAGd,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAA,CAAC,aAAD,CAAC,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAD,CAAC,CAAE,IAAI,CAAC,IAAI,MAAK,IAAI,CAAC,IAAI,CAAC;cAErE,IAAI,UAAU,EAAE;EACd,gBAAA,EAAE,CAAC,gBAAgB,CAAC,UAAU,CAAC;;cAEjC,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC;EAElC,YAAA,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;EAExB,YAAA,OAAO,IAAI;;EAGb,QAAA,OAAO,KAAK;;EAEf;;EC5pBK,SAAU,QAAQ,CAAC,KAAU,EAAA;EACjC,IAAA,OAAO,OAAO,KAAK,KAAK,QAAQ;EAClC;;EC2BA;;;EAGG;QACU,SAAS,CAAA;EAcpB,IAAA,WAAA,CAAY,MAYX,EAAA;EACC,QAAA,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;EACvB,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO;;EAEhC;EAED,MAAM,uBAAuB,GAAG,CAC9B,IAAY,EACZ,IAAqB,EACrB,KAA6B,KACC;EAC9B,IAAA,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;UAClB,OAAO,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;;MAGjC,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC;MAEjC,IAAI,CAAC,OAAO,EAAE;EACZ,QAAA,OAAO,EAAE;;EAGX,IAAA,OAAO,OAAO,CAAC,GAAG,CAAC,cAAc,IAAG;EAClC,QAAA,MAAM,MAAM,GAA6B,CAAC,cAAc,CAAC,IAAI,CAAC;EAE9D,QAAA,MAAM,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK;EACnC,QAAA,MAAM,CAAC,KAAK,GAAG,IAAI;EACnB,QAAA,MAAM,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI;EAEjC,QAAA,IAAI,cAAc,CAAC,WAAW,EAAE;EAC9B,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE;EAC7D,gBAAA,OAAO,CAAC,IAAI,CACV,oFAAoF,CACrF;;EAGH,YAAA,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;;EAGzC,QAAA,OAAO,MAAM;EACf,KAAC,CAAC;EACJ,CAAC;EAED,SAAS,GAAG,CAAC,MAQZ,EAAA;EACC,IAAA,MAAM,EACJ,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,GACrD,GAAG,MAAM;MAEV,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,IAAI,cAAc,CAAC;UAClD,MAAM;UACN,KAAK;EACN,KAAA,CAAC;MAEF,MAAM,QAAQ,GAAoB,EAAE;EAEpC,IAAA,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,GAAG,KAAI;EAC7C,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;cAC5C;;UAGF,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;EACxC,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;EACxD,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,GAAG,EAAE,UAAU,GAAG,GAAG,EAAE,SAAS,EAAE,QAAQ,CAAC;EAE/F,QAAA,MAAM,OAAO,GAAG,uBAAuB,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC;EAE3E,QAAA,OAAO,CAAC,OAAO,CAAC,KAAK,IAAG;EACtB,YAAA,IAAI,KAAK,CAAC,KAAK,KAAK,SAAS,EAAE;kBAC7B;;cAGF,MAAM,KAAK,GAAG,YAAY,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC;cAC5C,MAAM,GAAG,GAAG,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM;EACnC,YAAA,MAAM,KAAK,GAAG;kBACZ,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC;kBACjC,EAAE,EAAE,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC;eAC9B;EAED,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;kBAC3B,KAAK;kBACL,KAAK;kBACL,KAAK;kBACL,QAAQ;kBACR,KAAK;kBACL,GAAG;kBACH,UAAU;kBACV,SAAS;EACV,aAAA,CAAC;EAEF,YAAA,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;EACxB,SAAC,CAAC;EACJ,KAAC,CAAC;EAEF,IAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,IAAI,OAAO,KAAK,IAAI,CAAC;EAE3D,IAAA,OAAO,OAAO;EAChB;EAEA;EACA,IAAI,yBAAyB,GAAkB,IAAI;EAEnD,MAAM,yBAAyB,GAAG,CAAC,IAAY,KAAI;;EACjD,IAAA,MAAM,KAAK,GAAG,IAAI,cAAc,CAAC,OAAO,EAAE;UACxC,aAAa,EAAE,IAAI,YAAY,EAAE;EAClC,KAAA,CAAC;MAEF,CAAA,EAAA,GAAA,KAAK,CAAC,aAAa,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC;EAE/C,IAAA,OAAO,KAAK;EACd,CAAC;EAED;;;;EAIG;EACG,SAAU,gBAAgB,CAAC,KAA6C,EAAA;EAC5E,IAAA,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK;MAC/B,IAAI,iBAAiB,GAAmB,IAAI;MAC5C,IAAI,uBAAuB,GAAG,KAAK;MACnC,IAAI,wBAAwB,GAAG,KAAK;EACpC,IAAA,IAAI,UAAU,GAAG,OAAO,cAAc,KAAK,WAAW,GAAG,IAAI,cAAc,CAAC,OAAO,CAAC,GAAG,IAAI;EAC3F,IAAA,IAAI,SAA2B;EAE/B,IAAA,IAAI;EACF,QAAA,SAAS,GAAG,OAAO,SAAS,KAAK,WAAW,GAAG,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI;;EAC3E,IAAA,MAAM;UACN,SAAS,GAAG,IAAI;;EAGlB,IAAA,MAAM,YAAY,GAAG,CAAC,EACpB,KAAK,EACL,IAAI,EACJ,EAAE,EACF,IAAI,EACJ,QAAQ,GAOT,KAAI;EACH,QAAA,MAAM,EAAE,GAAG,KAAK,CAAC,EAAE;UACnB,MAAM,cAAc,GAAG,oBAAoB,CAAC;cAC1C,KAAK;EACL,YAAA,WAAW,EAAE,EAAE;EAChB,SAAA,CAAC;UAEF,MAAM,OAAO,GAAG,GAAG,CAAC;cAClB,MAAM;EACN,YAAA,KAAK,EAAE,cAAc;cACrB,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC;EAC3B,YAAA,EAAE,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC;cACZ,IAAI;EACJ,YAAA,UAAU,EAAE,QAAQ;cACpB,SAAS;EACV,SAAA,CAAC;UAEF,IAAI,CAAC,OAAO,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE;cAChC;;EAGF,QAAA,IAAI;EACF,YAAA,SAAS,GAAG,OAAO,SAAS,KAAK,WAAW,GAAG,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI;;EAC3E,QAAA,MAAM;cACN,SAAS,GAAG,IAAI;;EAElB,QAAA,UAAU,GAAG,OAAO,cAAc,KAAK,WAAW,GAAG,IAAI,cAAc,CAAC,OAAO,CAAC,GAAG,IAAI;EAEvF,QAAA,OAAO,EAAE;EACX,KAAC;MAED,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,IAAG;UAC/B,OAAO,IAAIC,YAAM,CAAC;;EAEhB,YAAA,IAAI,CAAC,IAAI,EAAA;EACP,gBAAA,MAAM,eAAe,GAAG,CAAC,KAAgB,KAAI;;EAC3C,oBAAA,iBAAiB,GAAG,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,GAAG,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,QAAQ,CAAC,KAAK,CAAC,MAAiB,CAAC;EAC3E,0BAAE,IAAI,CAAC,GAAG,CAAC;4BACT,IAAI;sBAER,IAAI,iBAAiB,EAAE;0BACrB,yBAAyB,GAAG,MAAM;;EAEtC,iBAAC;kBAED,MAAM,aAAa,GAAG,MAAK;sBACzB,IAAI,yBAAyB,EAAE;0BAC7B,yBAAyB,GAAG,IAAI;;EAEpC,iBAAC;EAED,gBAAA,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,eAAe,CAAC;EACrD,gBAAA,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,aAAa,CAAC;kBAEjD,OAAO;sBACL,OAAO,GAAA;EACL,wBAAA,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,eAAe,CAAC;EACxD,wBAAA,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,aAAa,CAAC;uBACrD;mBACF;eACF;EAED,YAAA,KAAK,EAAE;EACL,gBAAA,eAAe,EAAE;EACf,oBAAA,IAAI,EAAE,CAAC,IAAI,EAAE,KAAY,KAAI;0BAC3B,wBAAwB,GAAG,iBAAiB,KAAK,IAAI,CAAC,GAAG,CAAC,aAAa;0BACvE,SAAS,GAAG,KAAkB;0BAE9B,IAAI,CAAC,wBAAwB,EAAE;8BAC7B,MAAM,mBAAmB,GAAG,yBAAyB;8BAErD,IAAI,mBAAmB,EAAE;;kCAEvB,UAAU,CAAC,MAAK;EACd,oCAAA,MAAM,SAAS,GAAG,mBAAmB,CAAC,KAAK,CAAC,SAAS;sCAErD,IAAI,SAAS,EAAE;EACb,wCAAA,mBAAmB,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,IAAI,EAAE,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC;;mCAEvF,EAAE,EAAE,CAAC;;;EAGV,wBAAA,OAAO,KAAK;uBACb;EAED,oBAAA,KAAK,EAAE,CAAC,KAAK,EAAE,KAAY,KAAI;;0BAC7B,MAAM,IAAI,GAAG,CAAA,EAAA,GAAC,KAAwB,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,CAAC,WAAW,CAAC;0BAE1E,UAAU,GAAG,KAAuB;EAEpC,wBAAA,uBAAuB,GAAG,CAAC,EAAC,IAAI,KAAJ,IAAA,IAAA,IAAI,KAAJ,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,IAAI,CAAE,QAAQ,CAAC,eAAe,CAAC,CAAA;EAE3D,wBAAA,OAAO,KAAK;uBACb;EACF,iBAAA;EACF,aAAA;cAED,iBAAiB,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,KAAK,KAAI;EACnD,gBAAA,MAAM,WAAW,GAAG,YAAY,CAAC,CAAC,CAAC;EACnC,gBAAA,MAAM,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,OAAO,IAAI,CAAC,uBAAuB;EACtF,gBAAA,MAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,MAAM,IAAI,CAAC,wBAAwB;;kBAGrF,MAAM,kBAAkB,GAAG,WAAW,CAAC,OAAO,CAAC,iBAAiB,CAED;EAC/D,gBAAA,MAAM,gBAAgB,GAAG,CAAC,CAAC,kBAAkB;kBAE7C,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,IAAI,CAAC,gBAAgB,EAAE;sBAC5C;;;kBAIF,IAAI,gBAAgB,EAAE;EACpB,oBAAA,IAAI,EAAE,IAAI,EAAE,GAAG,kBAAkB;EAEjC,oBAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;0BAC5B,IAAI,GAAG,IAAc;;2BAChB;EACL,wBAAA,IAAI,GAAG,mBAAmB,CAACC,cAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC;;EAG/D,oBAAA,MAAM,EAAE,IAAI,EAAE,GAAG,kBAAkB;EACnC,oBAAA,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM;EAE7B,oBAAA,MAAM,QAAQ,GAAG,yBAAyB,CAAC,IAAI,CAAC;EAEhD,oBAAA,OAAO,YAAY,CAAC;0BAClB,IAAI;0BACJ,KAAK;0BACL,IAAI;EACJ,wBAAA,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE;0BACb,QAAQ;EACT,qBAAA,CAAC;;;EAIJ,gBAAA,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;EAClE,gBAAA,MAAM,EAAE,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;;EAG9D,gBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,KAAK,EAAE,CAAC,CAAC,EAAE;sBAC3C;;EAGF,gBAAA,OAAO,YAAY,CAAC;sBAClB,IAAI;sBACJ,KAAK;sBACL,IAAI;sBACJ,EAAE;EACF,oBAAA,QAAQ,EAAE,UAAU;EACrB,iBAAA,CAAC;eACH;EACF,SAAA,CAAC;EACJ,KAAC,CAAC;EAEF,IAAA,OAAO,OAAO;EAChB;;EC7WM,SAAU,cAAc,CAAC,KAAY,EAAA;MACzC,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,KAAK,KAAK,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC;MAEzE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;EACtC;;QCkBa,gBAAgB,CAAA;MAS3B,WAAY,CAAA,UAAsB,EAAE,MAAc,EAAA;UAFlD,IAAe,CAAA,eAAA,GAAa,EAAE;EAG5B,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM;UACpB,IAAI,CAAC,UAAU,GAAG,gBAAgB,CAAC,OAAO,CAAC,UAAU,CAAC;UACtD,IAAI,CAAC,MAAM,GAAG,6BAA6B,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC;UACpE,IAAI,CAAC,eAAe,EAAE;;EAGxB;;;;;EAKG;MACH,OAAO,OAAO,CAAC,UAAsB,EAAA;EACnC,QAAA,MAAM,kBAAkB,GAAG,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;EACtF,QAAA,MAAM,eAAe,GAAG,cAAc,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC;EAE3F,QAAA,IAAI,eAAe,CAAC,MAAM,EAAE;EAC1B,YAAA,OAAO,CAAC,IAAI,CACV,CAAA,iDAAA,EAAoD;iBACjD,GAAG,CAAC,IAAI,IAAI,CAAI,CAAA,EAAA,IAAI,GAAG;AACvB,iBAAA,IAAI,CAAC,IAAI,CAAC,CAAA,2BAAA,CAA6B,CAC3C;;EAGH,QAAA,OAAO,kBAAkB;;EAG3B;;;;EAIG;MACH,OAAO,OAAO,CAAC,UAAsB,EAAA;EACnC,QAAA,QACE;eACG,GAAG,CAAC,SAAS,IAAG;EACf,YAAA,MAAM,OAAO,GAAG;kBACd,IAAI,EAAE,SAAS,CAAC,IAAI;kBACpB,OAAO,EAAE,SAAS,CAAC,OAAO;kBAC1B,OAAO,EAAE,SAAS,CAAC,OAAO;eAC3B;cAED,MAAM,aAAa,GAAG,iBAAiB,CACrC,SAAS,EACT,eAAe,EACf,OAAO,CACR;cAED,IAAI,aAAa,EAAE;EACjB,gBAAA,OAAO,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC;;EAGtD,YAAA,OAAO,SAAS;EAClB,SAAC;;EAEA,aAAA,IAAI,CAAC,EAAE,CAAC;;EAIf;;;;EAIG;MACH,OAAO,IAAI,CAAC,UAAsB,EAAA;UAChC,MAAM,eAAe,GAAG,GAAG;UAE3B,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAI;cAC9B,MAAM,SAAS,GAAG,iBAAiB,CAAwB,CAAC,EAAE,UAAU,CAAC,IAAI,eAAe;cAC5F,MAAM,SAAS,GAAG,iBAAiB,CAAwB,CAAC,EAAE,UAAU,CAAC,IAAI,eAAe;EAE5F,YAAA,IAAI,SAAS,GAAG,SAAS,EAAE;kBACzB,OAAO,CAAC,CAAC;;EAGX,YAAA,IAAI,SAAS,GAAG,SAAS,EAAE;EACzB,gBAAA,OAAO,CAAC;;EAGV,YAAA,OAAO,CAAC;EACV,SAAC,CAAC;;EAGJ;;;EAGG;EACH,IAAA,IAAI,QAAQ,GAAA;UACV,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,SAAS,KAAI;EACpD,YAAA,MAAM,OAAO,GAAG;kBACd,IAAI,EAAE,SAAS,CAAC,IAAI;kBACpB,OAAO,EAAE,SAAS,CAAC,OAAO;kBAC1B,OAAO,EAAE,SAAS,CAAC,OAAO;kBAC1B,MAAM,EAAE,IAAI,CAAC,MAAM;kBACnB,IAAI,EAAE,mBAAmB,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;eACvD;cAED,MAAM,WAAW,GAAG,iBAAiB,CACnC,SAAS,EACT,aAAa,EACb,OAAO,CACR;cAED,IAAI,CAAC,WAAW,EAAE;EAChB,gBAAA,OAAO,QAAQ;;cAGjB,OAAO;EACL,gBAAA,GAAG,QAAQ;EACX,gBAAA,GAAG,WAAW,EAAE;eACjB;WACF,EAAE,EAAiB,CAAC;;EAGvB;;;EAGG;EACH,IAAA,IAAI,OAAO,GAAA;EACT,QAAA,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI;;;;;;EAOvB,QAAA,MAAM,UAAU,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,EAAE,CAAC;UAExE,MAAM,UAAU,GAAgB,EAAE;UAClC,MAAM,UAAU,GAAgB,EAAE;UAElC,MAAM,UAAU,GAAG;eAChB,GAAG,CAAC,SAAS,IAAG;EACf,YAAA,MAAM,OAAO,GAAG;kBACd,IAAI,EAAE,SAAS,CAAC,IAAI;kBACpB,OAAO,EAAE,SAAS,CAAC,OAAO;kBAC1B,OAAO,EAAE,SAAS,CAAC,OAAO;kBAC1B,MAAM;kBACN,IAAI,EAAE,mBAAmB,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;eACvD;cAED,MAAM,OAAO,GAAa,EAAE;cAE5B,MAAM,oBAAoB,GAAG,iBAAiB,CAC5C,SAAS,EACT,sBAAsB,EACtB,OAAO,CACR;cAED,IAAI,eAAe,GAAkC,EAAE;;EAGvD,YAAA,IAAI,SAAS,CAAC,IAAI,KAAK,MAAM,IAAI,iBAAiB,CAAwB,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC,EAAE;EACzG,gBAAA,eAAe,CAAC,UAAU,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,SAAiB,EAAE,CAAC;;cAGzF,IAAI,oBAAoB,EAAE;kBACxB,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,CACjC,MAAM,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAI;EAChE,oBAAA,OAAO,CAAC,QAAQ,EAAE,MAAM,MAAM,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;mBAC5C,CAAC,CACH;kBAED,eAAe,GAAG,EAAE,GAAG,eAAe,EAAE,GAAG,QAAQ,EAAE;;EAGvD,YAAA,MAAM,YAAY,GAAGC,aAAM,CAAC,eAAe,CAAC;EAE5C,YAAA,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC;cAE1B,MAAM,aAAa,GAAG,iBAAiB,CACrC,SAAS,EACT,eAAe,EACf,OAAO,CACR;EAED,YAAA,IAAI,uBAAuB,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,aAAa,EAAE;EACxF,gBAAA,UAAU,CAAC,IAAI,CAAC,GAAG,aAAa,EAAE,CAAC;;cAGrC,MAAM,aAAa,GAAG,iBAAiB,CACrC,SAAS,EACT,eAAe,EACf,OAAO,CACR;EAED,YAAA,IAAI,uBAAuB,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,aAAa,EAAE;EACxF,gBAAA,UAAU,CAAC,IAAI,CAAC,GAAG,aAAa,EAAE,CAAC;;cAGrC,MAAM,qBAAqB,GAAG,iBAAiB,CAC7C,SAAS,EACT,uBAAuB,EACvB,OAAO,CACR;cAED,IAAI,qBAAqB,EAAE;EACzB,gBAAA,MAAM,kBAAkB,GAAG,qBAAqB,EAAE;EAElD,gBAAA,OAAO,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC;;EAGrC,YAAA,OAAO,OAAO;EAChB,SAAC;EACA,aAAA,IAAI,EAAE;UAET,OAAO;EACL,YAAA,gBAAgB,CAAC;kBACf,MAAM;EACN,gBAAA,KAAK,EAAE,UAAU;eAClB,CAAC;EACF,YAAA,GAAG,gBAAgB,CAAC;kBAClB,MAAM;EACN,gBAAA,KAAK,EAAE,UAAU;eAClB,CAAC;EACF,YAAA,GAAG,UAAU;WACd;;EAGH;;;EAGG;EACH,IAAA,IAAI,UAAU,GAAA;EACZ,QAAA,OAAO,2BAA2B,CAAC,IAAI,CAAC,UAAU,CAAC;;EAGrD;;;EAGG;EACH,IAAA,IAAI,SAAS,GAAA;EACX,QAAA,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI;UACvB,MAAM,EAAE,cAAc,EAAE,GAAG,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC;EAE3D,QAAA,OAAO,MAAM,CAAC,WAAW,CACvB;EACG,aAAA,MAAM,CAAC,SAAS,IAAI,CAAC,CAAC,iBAAiB,CAAC,SAAS,EAAE,aAAa,CAAC;eACjE,GAAG,CAAC,SAAS,IAAG;cACf,MAAM,mBAAmB,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAChD,SAAS,IAAI,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC/C;EACD,YAAA,MAAM,OAAO,GAAG;kBACd,IAAI,EAAE,SAAS,CAAC,IAAI;kBACpB,OAAO,EAAE,SAAS,CAAC,OAAO;kBAC1B,OAAO,EAAE,SAAS,CAAC,OAAO;kBAC1B,MAAM;kBACN,IAAI,EAAE,WAAW,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;eAC/C;cACD,MAAM,WAAW,GAAG,iBAAiB,CACnC,SAAS,EACT,aAAa,EACb,OAAO,CACR;cAED,IAAI,CAAC,WAAW,EAAE;EAChB,gBAAA,OAAO,EAAE;;EAGX,YAAA,MAAM,QAAQ,GAAwB,CACpC,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,WAAW,EACX,gBAAgB,KACd;kBACF,MAAM,cAAc,GAAG,qBAAqB,CAAC,IAAI,EAAE,mBAAmB,CAAC;kBAEvE,OAAO,WAAW,EAAE,CAAC;;sBAEnB,IAAI;sBACJ,IAAI;EACJ,oBAAA,MAAM,EAAE,MAAsB;sBAC9B,WAAW;sBACX,gBAAgB;;sBAEhB,MAAM;sBACN,SAAS;sBACT,cAAc;EACf,iBAAA,CAAC;EACJ,aAAC;EAED,YAAA,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,QAAQ,CAAC;WAClC,CAAC,CACL;;EAGH;;;EAGG;MACK,eAAe,GAAA;EACrB,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,IAAG;;;EAElC,YAAA,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,OAAO;EAEhE,YAAA,MAAM,OAAO,GAAG;kBACd,IAAI,EAAE,SAAS,CAAC,IAAI;kBACpB,OAAO,EAAE,SAAS,CAAC,OAAO;kBAC1B,OAAO,EAAE,SAAS,CAAC,OAAO;kBAC1B,MAAM,EAAE,IAAI,CAAC,MAAM;kBACnB,IAAI,EAAE,mBAAmB,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;eACvD;EAED,YAAA,IAAI,SAAS,CAAC,IAAI,KAAK,MAAM,EAAE;EAC7B,gBAAA,MAAM,WAAW,GAAG,CAAA,EAAA,GAAA,YAAY,CAAC,iBAAiB,CAAC,SAAS,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,IAAI;kBAE9F,IAAI,WAAW,EAAE;sBACf,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;;;cAI7C,MAAM,cAAc,GAAG,iBAAiB,CACtC,SAAS,EACT,gBAAgB,EAChB,OAAO,CACR;cACD,MAAM,QAAQ,GAAG,iBAAiB,CAAwB,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC;cACzF,MAAM,QAAQ,GAAG,iBAAiB,CAAwB,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC;cACzF,MAAM,iBAAiB,GAAG,iBAAiB,CACzC,SAAS,EACT,mBAAmB,EACnB,OAAO,CACR;cACD,MAAM,aAAa,GAAG,iBAAiB,CACrC,SAAS,EACT,eAAe,EACf,OAAO,CACR;cACD,MAAM,OAAO,GAAG,iBAAiB,CAAuB,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC;cACtF,MAAM,MAAM,GAAG,iBAAiB,CAAsB,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC;cACnF,MAAM,SAAS,GAAG,iBAAiB,CAAyB,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC;cAE5F,IAAI,cAAc,EAAE;kBAClB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,cAAc,CAAC;;cAGhD,IAAI,QAAQ,EAAE;kBACZ,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;;cAGpC,IAAI,QAAQ,EAAE;kBACZ,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;;cAGpC,IAAI,iBAAiB,EAAE;kBACrB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;;cAGtD,IAAI,aAAa,EAAE;kBACjB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;;cAG9C,IAAI,OAAO,EAAE;kBACX,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;;cAGlC,IAAI,MAAM,EAAE;kBACV,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;;cAGhC,IAAI,SAAS,EAAE;kBACb,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;;EAExC,SAAC,CAAC;;EAEL;;ECAD;;;EAGG;QACU,SAAS,CAAA;EAkBpB,IAAA,WAAA,CAAY,SAAqD,EAAE,EAAA;UAjBnE,IAAI,CAAA,IAAA,GAAG,WAAW;UAElB,IAAI,CAAA,IAAA,GAAG,WAAW;UAElB,IAAM,CAAA,MAAA,GAAqB,IAAI;UAE/B,IAAK,CAAA,KAAA,GAAqB,IAAI;EAM9B,QAAA,IAAA,CAAA,MAAM,GAAoB;cACxB,IAAI,EAAE,IAAI,CAAC,IAAI;EACf,YAAA,cAAc,EAAE,EAAE;WACnB;UAGC,IAAI,CAAC,MAAM,GAAG;cACZ,GAAG,IAAI,CAAC,MAAM;EACd,YAAA,GAAG,MAAM;WACV;UAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;EAE5B,QAAA,IAAI,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;cAC1E,OAAO,CAAC,IAAI,CACV,CAAA,sHAAA,EAAyH,IAAI,CAAC,IAAI,CAAI,EAAA,CAAA,CACvI;;;UAIH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc;EAEzC,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;cAC1B,IAAI,CAAC,OAAO,GAAG,YAAY,CACzB,iBAAiB,CAA0B,IAAI,EAAE,YAAY,EAAE;kBAC7D,IAAI,EAAE,IAAI,CAAC,IAAI;EAChB,aAAA,CAAC,CACH;;UAGH,IAAI,CAAC,OAAO,GAAG,YAAY,CACzB,iBAAiB,CAA0B,IAAI,EAAE,YAAY,EAAE;cAC7D,IAAI,EAAE,IAAI,CAAC,IAAI;cACf,OAAO,EAAE,IAAI,CAAC,OAAO;WACtB,CAAC,CACH,IAAI,EAAE;;EAGT,IAAA,OAAO,MAAM,CAAmB,MAAA,GAAyC,EAAE,EAAA;EACzE,QAAA,OAAO,IAAI,SAAS,CAAO,MAAM,CAAC;;MAGpC,SAAS,CAAC,UAA4B,EAAE,EAAA;;;EAGtC,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAmB;cAC9C,GAAG,IAAI,CAAC,MAAM;cACd,UAAU,EAAE,MAAK;kBACf,OAAO,SAAS,CAAC,IAAI,CAAC,OAA8B,EAAE,OAAO,CAAY;eAC1E;EACF,SAAA,CAAC;;EAGF,QAAA,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;;EAE1B,QAAA,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;EAE9B,QAAA,OAAO,SAAS;;MAGlB,MAAM,CACJ,iBAA6E,EAAE,EAAA;EAE/E,QAAA,MAAM,SAAS,GAAG,IAAI,SAAS,CAAmC,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC;EAExG,QAAA,SAAS,CAAC,MAAM,GAAG,IAAI;EAEvB,QAAA,IAAI,CAAC,KAAK,GAAG,SAAS;UAEtB,SAAS,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI;EAElF,QAAA,IAAI,cAAc,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;cAC1F,OAAO,CAAC,IAAI,CACV,CAAA,sHAAA,EAAyH,SAAS,CAAC,IAAI,CAAI,EAAA,CAAA,CAC5I;;UAGH,SAAS,CAAC,OAAO,GAAG,YAAY,CAC9B,iBAAiB,CAA0B,SAAS,EAAE,YAAY,EAAE;cAClE,IAAI,EAAE,SAAS,CAAC,IAAI;EACrB,SAAA,CAAC,CACH;UAED,SAAS,CAAC,OAAO,GAAG,YAAY,CAC9B,iBAAiB,CAA0B,SAAS,EAAE,YAAY,EAAE;cAClE,IAAI,EAAE,SAAS,CAAC,IAAI;cACpB,OAAO,EAAE,SAAS,CAAC,OAAO;EAC3B,SAAA,CAAC,CACH;EAED,QAAA,OAAO,SAAS;;EAEnB;;ECvfD;;;;;;;EAOG;WACa,cAAc,CAC5B,SAA0B,EAC1B,KAAY,EACZ,OAGC,EAAA;EAED,IAAA,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,KAAK;EAC1B,IAAA,MAAM,EAAE,cAAc,GAAG,MAAM,EAAE,eAAe,GAAG,EAAE,EAAE,GAAG,OAAO,IAAI,EAAE;MACvE,IAAI,IAAI,GAAG,EAAE;EAEb,IAAA,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,KAAI;;UAC5D,IAAI,IAAI,CAAC,OAAO,IAAI,GAAG,GAAG,IAAI,EAAE;cAC9B,IAAI,IAAI,cAAc;;EAGxB,QAAA,MAAM,cAAc,GAAG,eAAe,KAAA,IAAA,IAAf,eAAe,KAAf,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,eAAe,CAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;UAExD,IAAI,cAAc,EAAE;cAClB,IAAI,MAAM,EAAE;kBACV,IAAI,IAAI,cAAc,CAAC;sBACrB,IAAI;sBACJ,GAAG;sBACH,MAAM;sBACN,KAAK;sBACL,KAAK;EACN,iBAAA,CAAC;;;EAGJ,YAAA,OAAO,KAAK;;EAGd,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;EACf,YAAA,IAAI,IAAI,CAAA,EAAA,GAAA,IAAI,KAAA,IAAA,IAAJ,IAAI,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAJ,IAAI,CAAE,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC,CAAA;;EAElE,KAAC,CAAC;EAEF,IAAA,OAAO,IAAI;EACb;;EC/CA;;;;EAIG;EACG,SAAU,4BAA4B,CAAC,MAAc,EAAA;MACzD,OAAO,MAAM,CAAC,WAAW,CACvB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK;EACxB,SAAA,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM;WACrC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CACnD;EACH;;ECLO,MAAM,uBAAuB,GAAG,SAAS,CAAC,MAAM,CAAiC;EACtF,IAAA,IAAI,EAAE,yBAAyB;MAE/B,UAAU,GAAA;UACR,OAAO;EACL,YAAA,cAAc,EAAE,SAAS;WAC1B;OACF;MAED,qBAAqB,GAAA;UACnB,OAAO;EACL,YAAA,IAAIF,YAAM,CAAC;EACT,gBAAA,GAAG,EAAE,IAAIG,eAAS,CAAC,yBAAyB,CAAC;EAC7C,gBAAA,KAAK,EAAE;sBACL,uBAAuB,EAAE,MAAK;EAC5B,wBAAA,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI;EACvB,wBAAA,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,MAAM;EAChC,wBAAA,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,KAAK;EAChC,wBAAA,MAAM,EAAE,MAAM,EAAE,GAAG,SAAS;0BAC5B,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;0BAC9D,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAC1D,wBAAA,MAAM,eAAe,GAAG,4BAA4B,CAAC,MAAM,CAAC;EAC5D,wBAAA,MAAM,KAAK,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE;EAE1B,wBAAA,OAAO,cAAc,CAAC,GAAG,EAAE,KAAK,EAAE;EAChC,4BAAA,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,KAAK;oCAChC,EAAE,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc;oCAC7C,EAAE,CAAC;8BACP,eAAe;EAChB,yBAAA,CAAC;uBACH;EACF,iBAAA;eACF,CAAC;WACH;OACF;EACF,CAAA,CAAC;;EC/BK,MAAM,IAAI,GAAwB,MAAM,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,KAAI;MAClE,qBAAqB,CAAC,MAAK;;EACzB,QAAA,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;EACtB,YAAA,IAAI,CAAC,GAAmB,CAAC,IAAI,EAAE;;;cAIhC,CAAA,EAAA,GAAA,MAAM,KAAN,IAAA,IAAA,MAAM,KAAN,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,MAAM,CAAE,YAAY,EAAE,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,EAAE;;EAE7C,KAAC,CAAC;EAEF,IAAA,OAAO,IAAI;EACb,CAAC;;ECXM,MAAM,YAAY,GAAgC,CAAC,UAAU,GAAG,KAAK,KAAK,CAAC,EAAE,QAAQ,EAAE,KAAI;MAChG,OAAO,QAAQ,CAAC,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC;EAC5C,CAAC;;ECDM,MAAM,UAAU,GAA8B,MAAM,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAI;EACrF,IAAA,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE;EACxB,IAAA,MAAM,EAAE,MAAM,EAAE,GAAG,SAAS;MAE5B,IAAI,CAAC,QAAQ,EAAE;EACb,QAAA,OAAO,IAAI;;MAGb,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,KAAI;EAChC,QAAA,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,KAAI;EACvD,YAAA,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;kBACpB;;EAGF,YAAA,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE;EAC3B,YAAA,MAAM,WAAW,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACjD,YAAA,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;cAC/D,MAAM,SAAS,GAAG,WAAW,CAAC,UAAU,CAAC,SAAS,CAAC;cAEnD,IAAI,CAAC,SAAS,EAAE;kBACd;;EAGF,YAAA,MAAM,eAAe,GAAGC,oBAAU,CAAC,SAAS,CAAC;EAE7C,YAAA,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;EACzB,gBAAA,MAAM,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;kBAE9E,EAAE,CAAC,aAAa,CAAC,SAAS,CAAC,KAAK,EAAE,WAAW,CAAC;;EAGhD,YAAA,IAAI,eAAe,IAAI,eAAe,KAAK,CAAC,EAAE;EAC5C,gBAAA,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,CAAC;;EAEvC,SAAC,CAAC;EACJ,KAAC,CAAC;EAEF,IAAA,OAAO,IAAI;EACb,CAAC;;ECnCM,MAAM,OAAO,GAA2B,EAAE,IAAI,KAAK,IAAG;EAC3D,IAAA,OAAO,EAAE,CAAC,KAAK,CAAC;EAClB,CAAC;;ECLM,MAAM,mBAAmB,GAAuC,MAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;EACnG,IAAA,OAAOC,8BAA2B,CAAC,KAAK,EAAE,QAAQ,CAAC;EACrD,CAAC;;ECEM,MAAM,GAAG,GAAuB,CAAC,WAAW,EAAE,SAAS,KAAK,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,KAAI;EACpF,IAAA,MAAM,SAAEC,OAAK,EAAE,GAAG,MAAM;EAExB,IAAA,MAAM,YAAY,GAAGA,OAAK,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,EAAE,CAAC;MAEtE,EAAE,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,EAAE,CAAC;MAChD,MAAM,MAAM,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC;MAExC,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,YAAY,CAAC,OAAO,CAAC;EAEvC,IAAA,EAAE,CAAC,YAAY,CAAC,IAAIC,mBAAa,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;EAE9D,IAAA,OAAO,IAAI;EACb,CAAC;;ECnBM,MAAM,iBAAiB,GAAqC,MAAM,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAI;EAC5F,IAAA,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE;MACxB,MAAM,WAAW,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE;;MAG5C,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,EAAE;EAChC,QAAA,OAAO,KAAK;;EAGd,IAAA,MAAM,IAAI,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO;EAEjC,IAAA,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE;UAClD,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;UAE7B,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI,EAAE;cAClC,IAAI,QAAQ,EAAE;kBACZ,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;kBAC/B,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;kBAE5B,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,cAAc,EAAE;;EAGtC,YAAA,OAAO,IAAI;;;EAIf,IAAA,OAAO,KAAK;EACd,CAAC;;ECvBM,MAAM,UAAU,GAA8B,UAAU,IAAI,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;MAC7F,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;EAClD,IAAA,MAAM,IAAI,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO;EAEjC,IAAA,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE;UAClD,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;EAE7B,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;cACtB,IAAI,QAAQ,EAAE;kBACZ,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;kBAC/B,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;kBAE5B,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,cAAc,EAAE;;EAGtC,YAAA,OAAO,IAAI;;;EAIf,IAAA,OAAO,KAAK;EACd,CAAC;;ECvBM,MAAM,WAAW,GAA+B,KAAK,IAAI,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAI;EACnF,IAAA,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,KAAK;MAE1B,IAAI,QAAQ,EAAE;EACZ,QAAA,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC;;EAGrB,IAAA,OAAO,IAAI;EACb,CAAC;;ECPM,MAAM,eAAe,GAAmC,MAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;EAC3F,IAAA,OAAOC,0BAAuB,CAAC,KAAK,EAAE,QAAQ,CAAC;EACjD,CAAC;;ECJM,MAAM,KAAK,GAAyB,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;EAChE,IAAA,OAAO,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC;EAC3C,CAAC;;ECAM,MAAM,QAAQ,GAA4B,MAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;EAC7E,IAAA,OAAOC,mBAAgB,CAAC,KAAK,EAAE,QAAQ,CAAC;EAC1C,CAAC;;EChBD;;;;EAIG;EACa,SAAA,cAAc,CAC5B,OAA4B,EAC5B,OAA4B,EAC5B,OAAA,GAA+B,EAAE,MAAM,EAAE,IAAI,EAAE,EAAA;MAE/C,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;EAEjC,IAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;EAChB,QAAA,OAAO,IAAI;;EAGb,IAAA,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,IAAG;EACtB,QAAA,IAAI,OAAO,CAAC,MAAM,EAAE;cAClB,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,OAAO,CAAC,GAAG,CAAC;;UAGtC,IAAI,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;EAC1B,YAAA,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;;UAGxC,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,OAAO,CAAC,GAAG,CAAC;EACtC,KAAC,CAAC;EACJ;;ECxBA,SAAS,aAAa,CACpB,KAAwB,EACxB,IAAc,EACd,aAAkC,EAAE,EAAA;EAEpC,IAAA,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,IAAG;EACvB,QAAA,QACE,IAAI,CAAC,IAAI,KAAK;iBACX,cAAc;;EAEf,YAAA,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACxE,UAAU,CACX;EAEL,KAAC,CAAC;EACJ;EAEA,SAAS,WAAW,CAClB,KAAwB,EACxB,IAAc,EACd,aAAkC,EAAE,EAAA;MAEpC,OAAO,CAAC,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC;EACjD;EAEA;;EAEG;WACa,YAAY;EAC1B;;EAEG;EACH,IAAiB;EACjB;;EAEG;EACH,IAAc;EACd;;;EAGG;EACH,UAAgC,EAAA;;EAEhC,IAAA,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE;UAClB;;EAEF,IAAA,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC;;MAGrD,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE;UACrE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC;;;MAIpD,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE;UACrE;;;EAIF,IAAA,UAAU,GAAG,UAAU,KAAI,CAAA,EAAA,GAAA,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAK,CAAA;;;EAIrD,IAAA,MAAM,IAAI,GAAG,aAAa,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,UAAU,CAAC;MAEnE,IAAI,CAAC,IAAI,EAAE;UACT;;EAGF,IAAA,IAAI,UAAU,GAAG,KAAK,CAAC,KAAK;MAC5B,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,KAAK,CAAC,MAAM;EAC1C,IAAA,IAAI,QAAQ,GAAG,UAAU,GAAG,CAAC;MAC7B,IAAI,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ;MAE3C,OACE,UAAU,GAAG;aACV,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,UAAU,CAAC,EAC9E;UACA,UAAU,IAAI,CAAC;UACf,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,QAAQ;;EAGpD,IAAA,OACE,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC;aACpB,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,UAAU,CAAC,EACxE;UACA,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,QAAQ;UAC9C,QAAQ,IAAI,CAAC;;MAGf,OAAO;EACL,QAAA,IAAI,EAAE,QAAQ;EACd,QAAA,EAAE,EAAE,MAAM;OACX;EACH;;ECjGgB,SAAA,WAAW,CAAC,UAA6B,EAAE,MAAc,EAAA;EACvE,IAAA,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;UAClC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;EAC7B,YAAA,MAAM,KAAK,CACT,CAAA,6BAAA,EAAgC,UAAU,CAAA,yCAAA,CAA2C,CACtF;;EAGH,QAAA,OAAO,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC;;EAGjC,IAAA,OAAO,UAAU;EACnB;;ECkBO,MAAM,eAAe,GAAmC,CAAC,UAAU,EAAE,UAAU,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,SAAEH,OAAK,EAAE,QAAQ,EAAE,KAAI;MAC1H,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAEA,OAAK,CAAC,MAAM,CAAC;EAClD,IAAA,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE;MAC7B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,SAAS;MAErC,IAAI,QAAQ,EAAE;UACZ,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC;EAEnD,QAAA,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC,EAAE,IAAI,EAAE,EAAE;EACjD,YAAA,MAAM,YAAY,GAAGC,mBAAa,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;EAEpE,YAAA,EAAE,CAAC,YAAY,CAAC,YAAY,CAAC;;;EAIjC,IAAA,OAAO,IAAI;EACb,CAAC;;ECjCM,MAAM,KAAK,GAAyB,QAAQ,IAAI,KAAK,IAAG;EAC7D,IAAA,MAAM,KAAK,GAAG,OAAO,QAAQ,KAAK;EAChC,UAAE,QAAQ,CAAC,KAAK;YACd,QAAQ;EAEZ,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;UACxC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;EACnB,YAAA,OAAO,IAAI;;;EAIf,IAAA,OAAO,KAAK;EACd,CAAC;;ECzBK,SAAU,eAAe,CAAC,KAAc,EAAA;MAC5C,OAAO,KAAK,YAAYA,mBAAa;EACvC;;ECJgB,SAAA,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAA;EAChD,IAAA,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC;EAC5C;;WCIgB,oBAAoB,CAClC,GAAoB,EACpB,WAA0B,IAAI,EAAA;MAE9B,IAAI,CAAC,QAAQ,EAAE;EACb,QAAA,OAAO,IAAI;;MAGb,MAAM,gBAAgB,GAAGG,eAAS,CAAC,OAAO,CAAC,GAAG,CAAC;MAC/C,MAAM,cAAc,GAAGA,eAAS,CAAC,KAAK,CAAC,GAAG,CAAC;MAE3C,IAAI,QAAQ,KAAK,OAAO,IAAI,QAAQ,KAAK,IAAI,EAAE;EAC7C,QAAA,OAAO,gBAAgB;;EAGzB,IAAA,IAAI,QAAQ,KAAK,KAAK,EAAE;EACtB,QAAA,OAAO,cAAc;;EAGvB,IAAA,MAAM,MAAM,GAAG,gBAAgB,CAAC,IAAI;EACpC,IAAA,MAAM,MAAM,GAAG,cAAc,CAAC,EAAE;EAEhC,IAAA,IAAI,QAAQ,KAAK,KAAK,EAAE;EACtB,QAAA,OAAOH,mBAAa,CAAC,MAAM,CACzB,GAAG,EACH,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,EACzB,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,CACzC;;MAGH,OAAOA,mBAAa,CAAC,MAAM,CACzB,GAAG,EACH,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,EAChC,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CACjC;EACH;;WCzCgB,SAAS,GAAA;EACvB,IAAA,OAAO,SAAS,CAAC,QAAQ,KAAK,SAAS,IAAI,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;EACjF;;WCFgB,KAAK,GAAA;MACnB,OAAO;UACL,gBAAgB;UAChB,kBAAkB;UAClB,gBAAgB;UAChB,MAAM;UACN,QAAQ;UACR,MAAM;EACP,KAAA,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ;;EAE1B,YAAC,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,YAAY,IAAI,QAAQ,CAAC;EACtE;;ECuBO,MAAM,KAAK,GAAyB,CAAC,QAAQ,GAAG,IAAI,EAAE,OAAO,GAAG,EAAE,KAAK,CAAC,EAC7E,MAAM,EACN,IAAI,EACJ,EAAE,EACF,QAAQ,GACT,KAAI;EACH,IAAA,OAAO,GAAG;EACR,QAAA,cAAc,EAAE,IAAI;EACpB,QAAA,GAAG,OAAO;OACX;MAED,MAAM,YAAY,GAAG,MAAK;;;EAGxB,QAAA,IAAI,KAAK,EAAE,IAAI,SAAS,EAAE,EAAE;EACzB,YAAA,IAAI,CAAC,GAAmB,CAAC,KAAK,EAAE;;;;UAKnC,qBAAqB,CAAC,MAAK;EACzB,YAAA,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;kBACvB,IAAI,CAAC,KAAK,EAAE;kBAEZ,IAAI,OAAO,aAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,cAAc,EAAE;EAC3B,oBAAA,MAAM,CAAC,QAAQ,CAAC,cAAc,EAAE;;;EAGtC,SAAC,CAAC;EACJ,KAAC;EAED,IAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,QAAQ,KAAK,IAAI,KAAK,QAAQ,KAAK,KAAK,EAAE;EAChE,QAAA,OAAO,IAAI;;;EAIb,IAAA,IAAI,QAAQ,IAAI,QAAQ,KAAK,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;EAC7E,QAAA,YAAY,EAAE;EACd,QAAA,OAAO,IAAI;;;;EAKb,IAAA,MAAM,SAAS,GAAG,oBAAoB,CAAC,EAAE,CAAC,GAAG,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,SAAS;EAClF,IAAA,MAAM,eAAe,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC;MAE5D,IAAI,QAAQ,EAAE;UACZ,IAAI,CAAC,eAAe,EAAE;EACpB,YAAA,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC;;;;EAK5B,QAAA,IAAI,eAAe,IAAI,EAAE,CAAC,WAAW,EAAE;EACrC,YAAA,EAAE,CAAC,cAAc,CAAC,EAAE,CAAC,WAAW,CAAC;;EAGnC,QAAA,YAAY,EAAE;;EAGhB,IAAA,OAAO,IAAI;EACb,CAAC;;EC1EM,MAAM,OAAO,GAA2B,CAAC,KAAK,EAAE,EAAE,KAAK,KAAK,IAAG;MACpE,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK,EAAE,CAAC,IAAI,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;EACpE,CAAC;;ECgBM,MAAM,aAAa,GAAiC,CAAC,KAAK,EAAE,OAAO,KAAK,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAI;MAClG,OAAO,QAAQ,CAAC,eAAe,CAC7B,EAAE,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE,EAChD,KAAK,EACL,OAAO,CACR;EACH,CAAC;;EC7CD,MAAM,iBAAiB,GAAG,CAAC,IAAiB,KAAI;EAC9C,IAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU;EAEhC,IAAA,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;EAChD,QAAA,MAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC;EAEzB,QAAA,IAAI,KAAK,CAAC,QAAQ,KAAK,CAAC,IAAI,KAAK,CAAC,SAAS,IAAI,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;EACpF,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;;EAClB,aAAA,IAAI,KAAK,CAAC,QAAQ,KAAK,CAAC,EAAE;cAC/B,iBAAiB,CAAC,KAAoB,CAAC;;;EAI3C,IAAA,OAAO,IAAI;EACb,CAAC;EAEK,SAAU,iBAAiB,CAAC,KAAa,EAAA;;EAE7C,IAAA,MAAM,YAAY,GAAG,CAAS,MAAA,EAAA,KAAK,SAAS;EAE5C,IAAA,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC,eAAe,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC,IAAI;EAEnF,IAAA,OAAO,iBAAiB,CAAC,IAAI,CAAC;EAChC;;ECNA;;;;;;EAMG;WACa,qBAAqB,CACnC,OAA6C,EAC7C,MAAc,EACd,OAAsC,EAAA;MAEtC,IAAI,OAAO,YAAYI,UAAe,IAAI,OAAO,YAAYV,cAAQ,EAAE;EACrE,QAAA,OAAO,OAAO;;EAEhB,IAAA,OAAO,GAAG;EACR,QAAA,KAAK,EAAE,IAAI;EACX,QAAA,YAAY,EAAE,EAAE;EAChB,QAAA,GAAG,OAAO;OACX;MAED,MAAM,aAAa,GAAG,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,IAAI;EACrE,IAAA,MAAM,aAAa,GAAG,OAAO,OAAO,KAAK,QAAQ;MAEjD,IAAI,aAAa,EAAE;EACjB,QAAA,IAAI;EACF,YAAA,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC;;cAGnE,IAAI,cAAc,EAAE;kBAClB,OAAOA,cAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;;cAG3E,MAAM,IAAI,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC;EAEzC,YAAA,IAAI,OAAO,CAAC,qBAAqB,EAAE;kBACjC,IAAI,CAAC,KAAK,EAAE;;EAGd,YAAA,OAAO,IAAI;;UACX,OAAO,KAAK,EAAE;EACd,YAAA,IAAI,OAAO,CAAC,qBAAqB,EAAE;kBACjC,MAAM,IAAI,KAAK,CAAC,sCAAsC,EAAE,EAAE,KAAK,EAAE,KAAc,EAAE,CAAC;;EAGpF,YAAA,OAAO,CAAC,IAAI,CAAC,iCAAiC,EAAE,eAAe,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC;cAE1F,OAAO,qBAAqB,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC;;;MAIrD,IAAI,aAAa,EAAE;;EAGjB,QAAA,IAAI,OAAO,CAAC,qBAAqB,EAAE;cACjC,IAAI,iBAAiB,GAAG,KAAK;cAC7B,IAAI,cAAc,GAAG,EAAE;;EAGvB,YAAA,MAAM,kBAAkB,GAAG,IAAIJ,YAAM,CAAC;EACpC,gBAAA,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO;EAC5B,gBAAA,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK;;;kBAGxB,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;EAC9B,oBAAA,4CAA4C,EAAE;EAC5C,wBAAA,OAAO,EAAE,SAAS;EAClB,wBAAA,KAAK,EAAE,OAAO;EACd,wBAAA,QAAQ,EAAE;EACR,4BAAA;EACE,gCAAA,GAAG,EAAE,GAAG;kCACR,QAAQ,EAAE,CAAC,IAAG;;sCAEZ,iBAAiB,GAAG,IAAI;;EAExB,oCAAA,cAAc,GAAG,OAAO,CAAC,KAAK,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,SAAS;EACxD,oCAAA,OAAO,IAAI;mCACZ;EACF,6BAAA;EACF,yBAAA;EACF,qBAAA;mBACF,CAAC;EACH,aAAA,CAAC;EAEF,YAAA,IAAI,OAAO,CAAC,KAAK,EAAE;EACjB,gBAAAe,eAAS,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,UAAU,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC;;mBAChG;EACL,gBAAAA,eAAS,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,KAAK,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC;;EAGlG,YAAA,IAAI,OAAO,CAAC,qBAAqB,IAAI,iBAAiB,EAAE;EACtD,gBAAA,MAAM,IAAI,KAAK,CAAC,sCAAsC,EAAE,EAAE,KAAK,EAAE,IAAI,KAAK,CAAC,CAA0B,uBAAA,EAAA,cAAc,EAAE,CAAC,EAAE,CAAC;;;UAI7H,MAAM,MAAM,GAAGA,eAAS,CAAC,UAAU,CAAC,MAAM,CAAC;EAE3C,QAAA,IAAI,OAAO,CAAC,KAAK,EAAE;EACjB,YAAA,OAAO,MAAM,CAAC,UAAU,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO;;EAGpF,QAAA,OAAO,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC;;MAIvE,OAAO,qBAAqB,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC;EACnD;;ECxHA;WACgB,uBAAuB,CAAC,EAAe,EAAE,QAAgB,EAAE,IAAY,EAAA;MACrF,MAAM,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC;EAEhC,IAAA,IAAI,IAAI,GAAG,QAAQ,EAAE;UACnB;;MAGF,MAAM,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;MAE3B,IAAI,EAAE,IAAI,YAAYC,qBAAW,IAAI,IAAI,YAAYC,2BAAiB,CAAC,EAAE;UACvE;;MAGF,MAAM,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;MACjC,IAAI,GAAG,GAAG,CAAC;EAEX,IAAA,GAAG,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,KAAI;EAC1C,QAAA,IAAI,GAAG,KAAK,CAAC,EAAE;cACb,GAAG,GAAG,KAAK;;EAEf,KAAC,CAAC;EAEF,IAAA,EAAE,CAAC,YAAY,CAACJ,eAAS,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;EAC5D;;EC+BA,MAAM,UAAU,GAAG,CAAC,cAA0C,KAAgC;EAC5F,IAAA,OAAO,EAAE,MAAM,IAAI,cAAc,CAAC;EACpC,CAAC;EAEM,MAAM,eAAe,GAAmC,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,KAAK,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAI;;MACxH,IAAI,QAAQ,EAAE;EACZ,QAAA,OAAO,GAAG;EACR,YAAA,YAAY,EAAE,MAAM,CAAC,OAAO,CAAC,YAAY;EACzC,YAAA,eAAe,EAAE,IAAI;EACrB,YAAA,eAAe,EAAE,KAAK;EACtB,YAAA,eAAe,EAAE,KAAK;EACtB,YAAA,GAAG,OAAO;WACX;EAED,QAAA,IAAI,OAAmC;EAEvC,QAAA,MAAM,gBAAgB,GAAG,CAAC,KAAY,KAAI;EACxC,YAAA,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE;kBAC1B,MAAM;kBACN,KAAK;kBACL,oBAAoB,EAAE,MAAK;EACzB,oBAAA,IAAI,MAAM,CAAC,OAAO,CAAC,aAAa,EAAE;0BAChC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,GAAG,IAAI;;mBAEjD;EACF,aAAA,CAAC;EACJ,SAAC;EAED,QAAA,MAAM,YAAY,GAAiB;EACjC,YAAA,kBAAkB,EAAE,MAAM;cAC1B,GAAG,OAAO,CAAC,YAAY;WACxB;;;EAID,QAAA,IAAI,CAAC,OAAO,CAAC,qBAAqB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,kBAAkB,IAAI,MAAM,CAAC,OAAO,CAAC,gBAAgB,EAAE;EAC3G,YAAA,IAAI;EACF,gBAAA,qBAAqB,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE;sBAC1C,YAAY;EACZ,oBAAA,qBAAqB,EAAE,IAAI;EAC5B,iBAAA,CAAC;;cACF,OAAO,CAAC,EAAE;kBACV,gBAAgB,CAAC,CAAU,CAAC;;;EAIhC,QAAA,IAAI;cACF,OAAO,GAAG,qBAAqB,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE;kBACpD,YAAY;kBACZ,qBAAqB,EAAE,CAAA,EAAA,GAAA,OAAO,CAAC,qBAAqB,mCAAI,MAAM,CAAC,OAAO,CAAC,kBAAkB;EAC1F,aAAA,CAAC;;UACF,OAAO,CAAC,EAAE;cACV,gBAAgB,CAAC,CAAU,CAAC;EAC5B,YAAA,OAAO,KAAK;;EAGd,QAAA,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,OAAO,QAAQ,KAAK,QAAQ,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;UAE7H,IAAI,iBAAiB,GAAG,IAAI;UAC5B,IAAI,kBAAkB,GAAG,IAAI;EAC7B,QAAA,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG,CAAC,OAAO,CAAC;EAEvD,QAAA,KAAK,CAAC,OAAO,CAAC,IAAI,IAAG;;cAEnB,IAAI,CAAC,KAAK,EAAE;cAEZ,iBAAiB,GAAG,iBAAiB,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG,KAAK;EAEtF,YAAA,kBAAkB,GAAG,kBAAkB,GAAG,IAAI,CAAC,OAAO,GAAG,KAAK;EAChE,SAAC,CAAC;;;;;;EAOF,QAAA,IAAI,IAAI,KAAK,EAAE,IAAI,kBAAkB,EAAE;EACrC,YAAA,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC;EACvC,YAAA,MAAM,gBAAgB,GAAG,MAAM,CAAC,WAAW,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU;cAE3F,IAAI,gBAAgB,EAAE;kBACpB,IAAI,IAAI,CAAC;kBACT,EAAE,IAAI,CAAC;;;EAIX,QAAA,IAAI,UAAU;;;UAId,IAAI,iBAAiB,EAAE;;;EAGrB,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;kBACxB,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;EAC7C,iBAAA,IAAI,KAAK,YAAYT,cAAQ,EAAE;kBACpC,IAAI,IAAI,GAAG,EAAE;EAEb,gBAAA,KAAK,CAAC,OAAO,CAAC,IAAI,IAAG;EACnB,oBAAA,IAAI,IAAI,CAAC,IAAI,EAAE;EACb,wBAAA,IAAI,IAAI,IAAI,CAAC,IAAI;;EAErB,iBAAC,CAAC;kBAEF,UAAU,GAAG,IAAI;;EACZ,iBAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE;EAC/D,gBAAA,UAAU,GAAG,KAAK,CAAC,IAAI;;mBAClB;kBACL,UAAU,GAAG,KAAe;;cAG9B,EAAE,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,CAAC;;eAC9B;cACL,UAAU,GAAG,OAAO;cAEpB,EAAE,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,EAAE,UAAU,CAAC;;;EAItC,QAAA,IAAI,OAAO,CAAC,eAAe,EAAE;EAC3B,YAAA,uBAAuB,CAAC,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;;EAGtD,QAAA,IAAI,OAAO,CAAC,eAAe,EAAE;EAC3B,YAAA,EAAE,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;;EAG3D,QAAA,IAAI,OAAO,CAAC,eAAe,EAAE;EAC3B,YAAA,EAAE,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;;;EAI7D,IAAA,OAAO,IAAI;EACb,CAAC;;ECjJM,MAAM,MAAM,GAA0B,MAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;EACzE,IAAA,OAAOc,iBAAc,CAAC,KAAK,EAAE,QAAQ,CAAC;EACxC,CAAC;EAEM,MAAM,QAAQ,GAA4B,MAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;EAC7E,IAAA,OAAOC,mBAAgB,CAAC,KAAK,EAAE,QAAQ,CAAC;EAC1C,CAAC;EAEM,MAAM,YAAY,GAAgC,MAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;EACrF,IAAA,OAAOC,uBAAoB,CAAC,KAAK,EAAE,QAAQ,CAAC;EAC9C,CAAC;EAEM,MAAM,WAAW,GAA+B,MAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;EACnF,IAAA,OAAOC,sBAAmB,CAAC,KAAK,EAAE,QAAQ,CAAC;EAC7C,CAAC;;EC5CM,MAAM,gBAAgB,GAAoC,MAAM,CAAC,EACtE,KAAK,EACL,QAAQ,EACR,EAAE,GACH,KAAI;EACH,IAAA,IAAI;EACF,QAAA,MAAM,KAAK,GAAGC,mBAAS,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;UAEjE,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;EACzC,YAAA,OAAO,KAAK;;EAGd,QAAA,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;UAEjB,IAAI,QAAQ,EAAE;cACZ,QAAQ,CAAC,EAAE,CAAC;;EAGd,QAAA,OAAO,IAAI;;EACX,IAAA,MAAM;EACN,QAAA,OAAO,KAAK;;EAEhB,CAAC;;ECtBM,MAAM,eAAe,GAAmC,MAAM,CAAC,EACpE,KAAK,EACL,QAAQ,EACR,EAAE,GACH,KAAI;EACH,IAAA,IAAI;EACF,QAAA,MAAM,KAAK,GAAGA,mBAAS,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;UAEjE,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;EACzC,YAAA,OAAO,KAAK;;EAGd,QAAA,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;UAEjB,IAAI,QAAQ,EAAE;cACZ,QAAQ,CAAC,EAAE,CAAC;;EAGd,QAAA,OAAO,IAAI;;EACX,IAAA,MAAM;EACN,QAAA,OAAO,KAAK;;EAEhB,CAAC;;ECvBM,MAAM,qBAAqB,GAAyC,MAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;EACvG,IAAA,OAAOC,gCAAe,CAAC,KAAK,EAAE,QAAQ,CAAC;EACzC,CAAC;;ECFM,MAAM,oBAAoB,GAAwC,MAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;EACrG,IAAA,OAAOA,+BAAe,CAAC,KAAK,EAAE,QAAQ,CAAC;EACzC,CAAC;;WCjBe,OAAO,GAAA;MACrB,OAAO,OAAO,SAAS,KAAK;YACxB,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ;YAC7B,KAAK;EACX;;ECAA,SAAS,gBAAgB,CAAC,IAAY,EAAA;MACpC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;MAClC,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;EAEpC,IAAA,IAAI,MAAM,KAAK,OAAO,EAAE;UACtB,MAAM,GAAG,GAAG;;EAGd,IAAA,IAAI,GAAG;EACP,IAAA,IAAI,IAAI;EACR,IAAA,IAAI,KAAK;EACT,IAAA,IAAI,IAAI;EAER,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;EAC5C,QAAA,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC;EAEpB,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;cAC/B,IAAI,GAAG,IAAI;;EACN,aAAA,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;cAChC,GAAG,GAAG,IAAI;;EACL,aAAA,IAAI,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;cAC1C,IAAI,GAAG,IAAI;;EACN,aAAA,IAAI,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;cAClC,KAAK,GAAG,IAAI;;EACP,aAAA,IAAI,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;EAC7B,YAAA,IAAI,KAAK,EAAE,IAAI,OAAO,EAAE,EAAE;kBACxB,IAAI,GAAG,IAAI;;mBACN;kBACL,IAAI,GAAG,IAAI;;;eAER;EACL,YAAA,MAAM,IAAI,KAAK,CAAC,+BAA+B,GAAG,CAAA,CAAE,CAAC;;;MAIzD,IAAI,GAAG,EAAE;EACP,QAAA,MAAM,GAAG,CAAA,IAAA,EAAO,MAAM,CAAA,CAAE;;MAG1B,IAAI,IAAI,EAAE;EACR,QAAA,MAAM,GAAG,CAAA,KAAA,EAAQ,MAAM,CAAA,CAAE;;MAG3B,IAAI,IAAI,EAAE;EACR,QAAA,MAAM,GAAG,CAAA,KAAA,EAAQ,MAAM,CAAA,CAAE;;MAG3B,IAAI,KAAK,EAAE;EACT,QAAA,MAAM,GAAG,CAAA,MAAA,EAAS,MAAM,CAAA,CAAE;;EAG5B,IAAA,OAAO,MAAM;EACf;EAeO,MAAM,gBAAgB,GAAoC,IAAI,IAAI,CAAC,EACxE,MAAM,EACN,IAAI,EACJ,EAAE,EACF,QAAQ,GACT,KAAI;MACH,MAAM,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC;MACnD,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;EAC/E,IAAA,MAAM,KAAK,GAAG,IAAI,aAAa,CAAC,SAAS,EAAE;UACzC,GAAG,EAAE,GAAG,KAAK;EACX,cAAE;EACF,cAAE,GAAG;EACP,QAAA,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;EAC5B,QAAA,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;EAC9B,QAAA,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;EAC9B,QAAA,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;EAChC,QAAA,OAAO,EAAE,IAAI;EACb,QAAA,UAAU,EAAE,IAAI;EACjB,KAAA,CAAC;EAEF,IAAA,MAAM,mBAAmB,GAAG,MAAM,CAAC,kBAAkB,CAAC,MAAK;EACzD,QAAA,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;EACrD,KAAC,CAAC;MAEF,mBAAmB,KAAA,IAAA,IAAnB,mBAAmB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAnB,mBAAmB,CAAE,KAAK,CAAC,OAAO,CAAC,IAAI,IAAG;UACxC,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,CAAC;EAEpC,QAAA,IAAI,OAAO,IAAI,QAAQ,EAAE;EACvB,YAAA,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC;;EAEzB,KAAC,CAAC;EAEF,IAAA,OAAO,IAAI;EACb,CAAC;;ECjGK,SAAU,YAAY,CAC1B,KAAkB,EAClB,UAAoC,EACpC,aAAkC,EAAE,EAAA;MAEpC,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC,SAAS;EAC3C,IAAA,MAAM,IAAI,GAAG,UAAU,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI;MAEtE,MAAM,UAAU,GAAgB,EAAE;EAElC,IAAA,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,GAAG,KAAI;EAC7C,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;cACf;;UAGF,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;EACxC,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC;UAEpD,UAAU,CAAC,IAAI,CAAC;cACd,IAAI;EACJ,YAAA,IAAI,EAAE,YAAY;EAClB,YAAA,EAAE,EAAE,UAAU;EACf,SAAA,CAAC;EACJ,KAAC,CAAC;EAEF,IAAA,MAAM,cAAc,GAAG,EAAE,GAAG,IAAI;MAChC,MAAM,iBAAiB,GAAG;WACvB,MAAM,CAAC,SAAS,IAAG;UAClB,IAAI,CAAC,IAAI,EAAE;EACT,YAAA,OAAO,IAAI;;UAGb,OAAO,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;EAC/C,KAAC;WACA,MAAM,CAAC,SAAS,IAAI,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;MAE3F,IAAI,KAAK,EAAE;EACT,QAAA,OAAO,CAAC,CAAC,iBAAiB,CAAC,MAAM;;MAGnC,MAAM,KAAK,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,SAAS,KAAK,GAAG,GAAG,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;MAElG,OAAO,KAAK,IAAI,cAAc;EAChC;;EC5BO,MAAM,IAAI,GAAwB,CAAC,UAAU,EAAE,UAAU,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;MAChG,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;MAClD,MAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC;MAEtD,IAAI,CAAC,QAAQ,EAAE;EACb,QAAA,OAAO,KAAK;;EAGd,IAAA,OAAOC,eAAY,CAAC,KAAK,EAAE,QAAQ,CAAC;EACtC,CAAC;;ECfM,MAAM,cAAc,GAAkC,MAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;EACzF,IAAA,OAAOC,yBAAsB,CAAC,KAAK,EAAE,QAAQ,CAAC;EAChD,CAAC;;ECCM,MAAM,YAAY,GAAgC,UAAU,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;MAC7F,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;MAElD,OAAOC,uBAAoB,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC;EACpD,CAAC;;ECPM,MAAM,aAAa,GAAiC,MAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;EACvF,IAAA,OAAOC,wBAAqB,CAAC,KAAK,EAAE,QAAQ,CAAC;EAC/C,CAAC;;EChBD;;;;;EAKG;EACa,SAAA,uBAAuB,CAAC,IAAY,EAAE,MAAc,EAAA;EAClE,IAAA,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;EACtB,QAAA,OAAO,MAAM;;EAGf,IAAA,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;EACtB,QAAA,OAAO,MAAM;;EAGf,IAAA,OAAO,IAAI;EACb;;EClBA;;;;EAIG;EACa,SAAA,WAAW,CAAC,GAAwB,EAAE,WAA8B,EAAA;EAClF,IAAA,MAAM,KAAK,GAAG,OAAO,WAAW,KAAK;YACjC,CAAC,WAAW;YACZ,WAAW;EAEf,IAAA,OAAO;WACJ,IAAI,CAAC,GAAG;EACR,SAAA,MAAM,CAAC,CAAC,MAA2B,EAAE,IAAI,KAAI;UAC5C,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;cACzB,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC;;EAG1B,QAAA,OAAO,MAAM;OACd,EAAE,EAAE,CAAC;EACV;;ECMO,MAAM,eAAe,GAAmC,CAAC,UAAU,EAAE,UAAU,KAAK,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;MACrH,IAAI,QAAQ,GAAoB,IAAI;MACpC,IAAI,QAAQ,GAAoB,IAAI;MAEpC,MAAM,UAAU,GAAG,uBAAuB,CACxC,OAAO,UAAU,KAAK,QAAQ,GAAG,UAAU,GAAG,UAAU,CAAC,IAAI,EAC7D,KAAK,CAAC,MAAM,CACb;MAED,IAAI,CAAC,UAAU,EAAE;EACf,QAAA,OAAO,KAAK;;EAGd,IAAA,IAAI,UAAU,KAAK,MAAM,EAAE;UACzB,QAAQ,GAAG,WAAW,CAAC,UAAsB,EAAE,KAAK,CAAC,MAAM,CAAC;;EAG9D,IAAA,IAAI,UAAU,KAAK,MAAM,EAAE;UACzB,QAAQ,GAAG,WAAW,CAAC,UAAsB,EAAE,KAAK,CAAC,MAAM,CAAC;;MAG9D,IAAI,QAAQ,EAAE;UACZ,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,IAAG;cAClC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,KAAI;kBACnE,IAAI,QAAQ,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,EAAE;EACtC,oBAAA,EAAE,CAAC,aAAa,CAAC,GAAG,EAAE,SAAS,EAAE,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;;kBAGvE,IAAI,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;EACjC,oBAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,IAAG;EACxB,wBAAA,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,EAAE;8BAC1B,EAAE,CAAC,OAAO,CACR,GAAG,EACH,GAAG,GAAG,IAAI,CAAC,QAAQ,EACnB,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CACrD;;EAEL,qBAAC,CAAC;;EAEN,aAAC,CAAC;EACJ,SAAC,CAAC;;EAGJ,IAAA,OAAO,IAAI;EACb,CAAC;;ECvDM,MAAM,cAAc,GAAkC,MAAM,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAI;MACtF,IAAI,QAAQ,EAAE;UACZ,EAAE,CAAC,cAAc,EAAE;;EAGrB,IAAA,OAAO,IAAI;EACb,CAAC;;ECJM,MAAM,SAAS,GAA6B,MAAM,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAI;MAC5E,IAAI,QAAQ,EAAE;UACZ,MAAM,SAAS,GAAG,IAAIC,kBAAY,CAAC,EAAE,CAAC,GAAG,CAAC;EAE1C,QAAA,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC;;EAG5B,IAAA,OAAO,IAAI;EACb,CAAC;;ECRM,MAAM,kBAAkB,GAAsC,MAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;EACjG,IAAA,OAAOC,6BAA0B,CAAC,KAAK,EAAE,QAAQ,CAAC;EACpD,CAAC;;ECFM,MAAM,iBAAiB,GAAqC,MAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;EAC/F,IAAA,OAAOC,4BAAyB,CAAC,KAAK,EAAE,QAAQ,CAAC;EACnD,CAAC;;ECFM,MAAM,gBAAgB,GAAoC,MAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;EAC7F,IAAA,OAAOC,2BAAwB,CAAC,KAAK,EAAE,QAAQ,CAAC;EAClD,CAAC;;EClBD;EACA;EAiBO,MAAM,kBAAkB,GAAsC,MAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;EACjG,IAAA,OAAOC,6BAA0B,CAAC,KAAK,EAAE,QAAQ,CAAC;EACpD,CAAC;;ECpBD;EACA;EAiBO,MAAM,oBAAoB,GAAwC,MAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;EACrG,IAAA,OAAOC,+BAA4B,CAAC,KAAK,EAAE,QAAQ,CAAC;EACtD,CAAC;;ECbD;;;;;;EAMG;EACG,SAAU,cAAc,CAC5B,OAA6C,EAC7C,MAAc,EACd,YAA6B,GAAA,EAAE,EAC/B,OAAA,GAA+C,EAAE,EAAA;EAEjD,IAAA,OAAO,qBAAqB,CAAC,OAAO,EAAE,MAAM,EAAE;EAC5C,QAAA,KAAK,EAAE,KAAK;UACZ,YAAY;UACZ,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;EACrD,KAAA,CAAoB;EACvB;;ECqBO,MAAM,UAAU,GAA8B,CAAC,OAAO,EAAE,UAAU,GAAG,KAAK,EAAE,YAAY,GAAG,EAAE,EAAE,OAAO,GAAG,EAAE,KAAK,CAAC,EACtH,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,QAAQ,GAC/B,KAAI;;EACH,IAAA,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;;;EAIlB,IAAA,IAAI,YAAY,CAAC,kBAAkB,KAAK,MAAM,EAAE;UAC9C,MAAM,QAAQ,GAAG,cAAc,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,YAAY,EAAE;cACpE,qBAAqB,EAAE,CAAA,EAAA,GAAA,OAAO,CAAC,qBAAqB,mCAAI,MAAM,CAAC,OAAO,CAAC,kBAAkB;EAC1F,SAAA,CAAC;UAEF,IAAI,QAAQ,EAAE;cACZ,EAAE,CAAC,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC,UAAU,CAAC;;EAErF,QAAA,OAAO,IAAI;;MAGb,IAAI,QAAQ,EAAE;UACZ,EAAE,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC,UAAU,CAAC;;EAG1C,IAAA,OAAO,QAAQ,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,OAAO,EAAE;UAC1E,YAAY;UACZ,qBAAqB,EAAE,CAAA,EAAA,GAAA,OAAO,CAAC,qBAAqB,mCAAI,MAAM,CAAC,OAAO,CAAC,kBAAkB;EAC1F,KAAA,CAAC;EACJ,CAAC;;ECnEe,SAAA,iBAAiB,CAC/B,KAAkB,EAClB,UAA6B,EAAA;MAE7B,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;MAClD,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC,SAAS;MAC3C,MAAM,KAAK,GAAW,EAAE;MAExB,IAAI,KAAK,EAAE;EACT,QAAA,IAAI,KAAK,CAAC,WAAW,EAAE;cACrB,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC;;EAGlC,QAAA,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;;WACvC;UACL,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,IAAG;cACtC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;EAC3B,SAAC,CAAC;;MAGJ,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC;MAErE,IAAI,CAAC,IAAI,EAAE;EACT,QAAA,OAAO,EAAE;;EAGX,IAAA,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE;EAC1B;;EC5BA;;;;;EAKG;EACa,SAAA,uBAAuB,CACrC,MAAuB,EACvB,YAA2B,EAAA;EAE3B,IAAA,MAAMC,WAAS,GAAG,IAAIC,mBAAS,CAAC,MAAM,CAAC;EAEvC,IAAA,YAAY,CAAC,OAAO,CAAC,WAAW,IAAG;EACjC,QAAA,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,IAAG;EAC/B,YAAAD,WAAS,CAAC,IAAI,CAAC,IAAI,CAAC;EACtB,SAAC,CAAC;EACJ,KAAC,CAAC;EAEF,IAAA,OAAOA,WAAS;EAClB;;ECrBA;;;;EAIG;EACG,SAAU,cAAc,CAAC,KAAmB,EAAA;EAChD,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,EAAE;UAC3C,MAAM,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;UAE9B,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE;EAChD,YAAA,OAAO,IAAI;;;EAIf,IAAA,OAAO,IAAI;EACb;;ECbA;;;;;EAKG;EACa,SAAA,YAAY,CAAC,IAAqB,EAAE,SAAoB,EAAA;MACtE,MAAM,YAAY,GAAkB,EAAE;MAEtC,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,EAAE,GAAG,KAAI;EAC9B,QAAA,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE;cACpB,YAAY,CAAC,IAAI,CAAC;EAChB,gBAAA,IAAI,EAAE,KAAK;kBACX,GAAG;EACJ,aAAA,CAAC;;EAEN,KAAC,CAAC;EAEF,IAAA,OAAO,YAAY;EACrB;;ECnBA;;;;;;EAMG;WACa,mBAAmB,CACjC,IAAqB,EACrB,KAAY,EACZ,SAAoB,EAAA;MAEpB,MAAM,YAAY,GAAkB,EAAE;;;;;;;;;;EAatC,IAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,GAAG,KAAI;EACrD,QAAA,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE;cACpB,YAAY,CAAC,IAAI,CAAC;EAChB,gBAAA,IAAI,EAAE,KAAK;kBACX,GAAG;EACJ,aAAA,CAAC;;EAEN,KAAC,CAAC;EAEF,IAAA,OAAO,YAAY;EACrB;;ECnCA;;;;;;;;EAQG;EACa,SAAA,0BAA0B,CACxC,IAAiB,EACjB,SAAoB,EAAA;EASpB,IAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;UACtC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;EAEzB,QAAA,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE;cACnB,OAAO;EACL,gBAAA,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;EAC/B,gBAAA,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;EACpB,gBAAA,KAAK,EAAE,CAAC;kBACR,IAAI;eACL;;;EAGP;;EC/BA;;;;;;;EAOG;EACG,SAAU,cAAc,CAAC,SAAoB,EAAA;EACjD,IAAA,OAAO,CAAC,SAAoB,KAAK,0BAA0B,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC;EACzF;;ECRgB,SAAA,SAAS,CAAC,UAAsB,EAAE,MAAe,EAAA;MAC/D,MAAM,kBAAkB,GAAG,gBAAgB,CAAC,OAAO,CAAC,UAAU,CAAC;EAE/D,IAAA,OAAO,6BAA6B,CAAC,kBAAkB,EAAE,MAAM,CAAC;EAClE;;ECLA;;;;;EAKG;EACa,SAAA,YAAY,CAAC,GAAgB,EAAE,UAAsB,EAAA;EACnE,IAAA,MAAM,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC;MACpC,MAAM,WAAW,GAAGE,UAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC;MAE9C,OAAO,mBAAmB,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC;EACzD;;ECXA;;;;;EAKG;EACa,SAAA,YAAY,CAAC,IAAY,EAAE,UAAsB,EAAA;EAC/D,IAAA,MAAM,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC;EACpC,IAAA,MAAM,GAAG,GAAG,iBAAiB,CAAC,IAAI,CAAC;EAEnC,IAAA,OAAOrB,eAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE;EACzD;;ECZA;;;;;;;;EAQG;EACa,SAAA,OAAO,CACrB,IAAqB,EACrB,OAGC,EAAA;EAED,IAAA,MAAM,KAAK,GAAG;EACZ,QAAA,IAAI,EAAE,CAAC;EACP,QAAA,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;OACtB;MAED,OAAO,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC;EAC7C;;ECpBA;;;;;;EAMG;WACa,YAAY,CAC1B,GAAgB,EAChB,UAAsB,EACtB,OAGC,EAAA;EAED,IAAA,MAAM,EAAE,cAAc,GAAG,MAAM,EAAE,eAAe,GAAG,EAAE,EAAE,GAAG,OAAO,IAAI,EAAE;EACvE,IAAA,MAAM,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC;MACpC,MAAM,WAAW,GAAGqB,UAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC;MAE9C,OAAO,OAAO,CAAC,WAAW,EAAE;UAC1B,cAAc;EACd,QAAA,eAAe,EAAE;cACf,GAAG,4BAA4B,CAAC,MAAM,CAAC;EACvC,YAAA,GAAG,eAAe;EACnB,SAAA;EACF,KAAA,CAAC;EACJ;;EC5BgB,SAAA,iBAAiB,CAC/B,KAAkB,EAClB,UAA6B,EAAA;MAE7B,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;MAClD,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,KAAK,CAAC,SAAS;MACpC,MAAM,KAAK,GAAW,EAAE;MAExB,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,IAAG;EACtC,QAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;EAClB,KAAC,CAAC;MAEF,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC;MAE/E,IAAI,CAAC,IAAI,EAAE;EACT,QAAA,OAAO,EAAE;;EAGX,IAAA,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE;EAC1B;;ECjBA;;;;;EAKG;EACa,SAAA,aAAa,CAC3B,KAAkB,EAClB,UAAwC,EAAA;MAExC,MAAM,UAAU,GAAG,uBAAuB,CACxC,OAAO,UAAU,KAAK,QAAQ,GAAG,UAAU,GAAG,UAAU,CAAC,IAAI,EAC7D,KAAK,CAAC,MAAM,CACb;EAED,IAAA,IAAI,UAAU,KAAK,MAAM,EAAE;EACzB,QAAA,OAAO,iBAAiB,CAAC,KAAK,EAAE,UAAsB,CAAC;;EAGzD,IAAA,IAAI,UAAU,KAAK,MAAM,EAAE;EACzB,QAAA,OAAO,iBAAiB,CAAC,KAAK,EAAE,UAAsB,CAAC;;EAGzD,IAAA,OAAO,EAAE;EACX;;EC/BA;;;EAGG;EACG,SAAU,gBAAgB,CAAI,KAAU,EAAE,EAAE,GAAG,IAAI,CAAC,SAAS,EAAA;MACjE,MAAM,IAAI,GAAqB,EAAE;EAEjC,IAAA,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,IAAG;EACzB,QAAA,MAAM,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC;UAEpB,OAAO,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG;EACnD,cAAE;iBACC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;EACxB,KAAC,CAAC;EACJ;;ECJA;;;EAGG;EACH,SAAS,qBAAqB,CAAC,OAAuB,EAAA;EACpD,IAAA,MAAM,aAAa,GAAG,gBAAgB,CAAC,OAAO,CAAC;EAE/C,IAAA,OAAO,aAAa,CAAC,MAAM,KAAK;EAC9B,UAAE;YACA,aAAa,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,KAAI;EACvC,YAAA,MAAM,IAAI,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC;EAExD,YAAA,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,IAAG;kBAC9B,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,WAAW,CAAC,QAAQ,CAAC;yBAC/C,MAAM,CAAC,QAAQ,CAAC,EAAE,IAAI,WAAW,CAAC,QAAQ,CAAC;yBAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,WAAW,CAAC,QAAQ,CAAC;yBAC7C,MAAM,CAAC,QAAQ,CAAC,EAAE,IAAI,WAAW,CAAC,QAAQ,CAAC,EAAE;EACpD,aAAC,CAAC;EACJ,SAAC,CAAC;EACN;EAEA;;;EAGG;EACG,SAAU,gBAAgB,CAAC,SAAoB,EAAA;EACnD,IAAA,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,SAAS;MACpC,MAAM,OAAO,GAAmB,EAAE;MAElC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,KAAI;UACtC,MAAM,MAAM,GAAY,EAAE;;;;EAK1B,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE;cAC1B,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,KAAK,CAAC,KAAK,CAG/B;cAED,IAAI,IAAI,KAAK,SAAS,IAAI,EAAE,KAAK,SAAS,EAAE;kBAC1C;;cAGF,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;;eACpB;cACL,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,KAAI;kBAC3B,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;EAC3B,aAAC,CAAC;;UAGJ,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,KAAI;EAC9B,YAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;EACnD,YAAA,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;EAC3C,YAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;cACnD,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC;cAE3C,OAAO,CAAC,IAAI,CAAC;EACX,gBAAA,QAAQ,EAAE;EACR,oBAAA,IAAI,EAAE,QAAQ;EACd,oBAAA,EAAE,EAAE,MAAM;EACX,iBAAA;EACD,gBAAA,QAAQ,EAAE;EACR,oBAAA,IAAI,EAAE,QAAQ;EACd,oBAAA,EAAE,EAAE,MAAM;EACX,iBAAA;EACF,aAAA,CAAC;EACJ,SAAC,CAAC;EACJ,KAAC,CAAC;EAEF,IAAA,OAAO,qBAAqB,CAAC,OAAO,CAAC;EACvC;;WCzEgB,YAAY,CAAC,IAAqB,EAAE,WAAW,GAAG,CAAC,EAAA;EACjE,IAAA,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW;MAC5D,MAAM,SAAS,GAAG,SAAS,GAAG,CAAC,GAAG,CAAC;MACnC,MAAM,IAAI,GAAG,WAAW;EACxB,IAAA,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,QAAQ;MAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,IAAG;EAClC,QAAA,MAAM,MAAM,GAAkD;EAC5D,YAAA,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;WACrB;UAED,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE;cAClC,MAAM,CAAC,KAAK,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE;;EAGlC,QAAA,OAAO,MAAM;EACf,KAAC,CAAC;MACF,MAAM,KAAK,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE;EAC/B,IAAA,MAAM,MAAM,GAAqB;EAC/B,QAAA,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;UACpB,IAAI;UACJ,EAAE;OACH;MAED,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE;EAC7B,QAAA,MAAM,CAAC,KAAK,GAAG,KAAK;;EAGtB,IAAA,IAAI,KAAK,CAAC,MAAM,EAAE;EAChB,QAAA,MAAM,CAAC,KAAK,GAAG,KAAK;;EAGtB,IAAA,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;EAC3B,QAAA,MAAM,CAAC,OAAO,GAAG,EAAE;UAEnB,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,MAAM,KAAI;;EAC7B,YAAA,CAAA,EAAA,GAAA,MAAM,CAAC,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAW,GAAG,MAAM,GAAG,SAAS,CAAC,CAAC;EAC7E,SAAC,CAAC;;EAGJ,IAAA,IAAI,IAAI,CAAC,IAAI,EAAE;EACb,QAAA,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;;EAGzB,IAAA,OAAO,MAAM;EACf;;WChDgB,eAAe,CAAC,IAAY,EAAE,EAAU,EAAE,GAAoB,EAAA;MAC5E,MAAM,KAAK,GAAgB,EAAE;;EAG7B,IAAA,IAAI,IAAI,KAAK,EAAE,EAAE;UACf;eACG,OAAO,CAAC,IAAI;EACZ,aAAA,KAAK;eACL,OAAO,CAAC,IAAI,IAAG;cACd,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC;cAC9B,MAAM,KAAK,GAAG,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC;cAE3C,IAAI,CAAC,KAAK,EAAE;kBACV;;cAGF,KAAK,CAAC,IAAI,CAAC;kBACT,IAAI;EACJ,gBAAA,GAAG,KAAK;EACT,aAAA,CAAC;EACJ,SAAC,CAAC;;WACC;EACL,QAAA,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,GAAG,KAAI;EACvC,YAAA,IAAI,CAAC,IAAI,IAAI,CAAA,IAAI,KAAJ,IAAA,IAAA,IAAI,KAAJ,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,IAAI,CAAE,QAAQ,MAAK,SAAS,EAAE;kBACzC;;EAGF,YAAA,KAAK,CAAC,IAAI,CACR,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,KAAK;EACzB,gBAAA,IAAI,EAAE,GAAG;EACT,gBAAA,EAAE,EAAE,GAAG,GAAG,IAAI,CAAC,QAAQ;kBACvB,IAAI;eACL,CAAC,CAAC,CACJ;EACH,SAAC,CAAC;;EAGJ,IAAA,OAAO,KAAK;EACd;;ECxCA;;;;;;;EAOG;AACI,QAAM,iBAAiB,GAAG,CAAC,KAAkB,EAAE,UAA6B,EAAE,GAAW,EAAE,QAAQ,GAAG,EAAE,KAAI;MACjH,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC;MAEnC,IAAI,YAAY,GAAG,QAAQ;MAC3B,IAAI,IAAI,GAAgB,IAAI;MAE5B,OAAO,YAAY,GAAG,CAAC,IAAI,IAAI,KAAK,IAAI,EAAE;UACxC,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC;EAE3C,QAAA,IAAI,CAAA,WAAW,KAAX,IAAA,IAAA,WAAW,KAAX,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,WAAW,CAAE,IAAI,CAAC,IAAI,MAAK,UAAU,EAAE;cACzC,IAAI,GAAG,WAAW;;eACb;cACL,YAAY,IAAI,CAAC;;;EAIrB,IAAA,OAAO,CAAC,IAAI,EAAE,YAAY,CAA0B;EACtD;;EC1BA;;;;;;EAMG;WACa,qBAAqB,CACnC,mBAAyC,EACzC,QAAgB,EAChB,UAA+B,EAAA;EAE/B,IAAA,OAAO,MAAM,CAAC,WAAW,CAAC;WACvB,OAAO,CAAC,UAAU;EAClB,SAAA,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAI;UACjB,MAAM,kBAAkB,GAAG,mBAAmB,CAAC,IAAI,CAAC,IAAI,IAAG;cACzD,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI;EACrD,SAAC,CAAC;UAEF,IAAI,CAAC,kBAAkB,EAAE;EACvB,YAAA,OAAO,KAAK;;EAGd,QAAA,OAAO,kBAAkB,CAAC,SAAS,CAAC,WAAW;OAChD,CAAC,CAAC;EACP;;ECpBM,SAAU,YAAY,CAC1B,KAAkB,EAClB,UAAoC,EACpC,aAAkC,EAAE,EAAA;MAEpC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS;EACzC,IAAA,MAAM,IAAI,GAAG,UAAU,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI;MAEtE,IAAI,KAAK,EAAE;EACT,QAAA,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE;eACzD,MAAM,CAAC,IAAI,IAAG;cACb,IAAI,CAAC,IAAI,EAAE;EACT,gBAAA,OAAO,IAAI;;cAGb,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI;EACrC,SAAC;eACA,IAAI,CAAC,IAAI,IAAI,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;;MAG5E,IAAI,cAAc,GAAG,CAAC;MACtB,MAAM,UAAU,GAAgB,EAAE;MAElC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,KAAI;EAChC,QAAA,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG;EACtB,QAAA,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG;EAElB,QAAA,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,GAAG,KAAI;EAC7C,YAAA,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;kBACtC;;cAGF,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;EACxC,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC;EACpD,YAAA,MAAM,KAAK,GAAG,UAAU,GAAG,YAAY;cAEvC,cAAc,IAAI,KAAK;EAEvB,YAAA,UAAU,CAAC,IAAI,CACb,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,KAAK;kBACzB,IAAI;EACJ,gBAAA,IAAI,EAAE,YAAY;EAClB,gBAAA,EAAE,EAAE,UAAU;eACf,CAAC,CAAC,CACJ;EACH,SAAC,CAAC;EACJ,KAAC,CAAC;EAEF,IAAA,IAAI,cAAc,KAAK,CAAC,EAAE;EACxB,QAAA,OAAO,KAAK;;;MAId,MAAM,YAAY,GAAG;WAClB,MAAM,CAAC,SAAS,IAAG;UAClB,IAAI,CAAC,IAAI,EAAE;EACT,YAAA,OAAO,IAAI;;UAGb,OAAO,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;EAC/C,KAAC;WACA,MAAM,CAAC,SAAS,IAAI,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;WACvF,MAAM,CAAC,CAAC,GAAG,EAAE,SAAS,KAAK,GAAG,GAAG,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;;;MAIrE,MAAM,aAAa,GAAG;WACnB,MAAM,CAAC,SAAS,IAAG;UAClB,IAAI,CAAC,IAAI,EAAE;EACT,YAAA,OAAO,IAAI;;EAGb,QAAA,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;EAC3E,KAAC;WACA,MAAM,CAAC,CAAC,GAAG,EAAE,SAAS,KAAK,GAAG,GAAG,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;;;EAIrE,IAAA,MAAM,KAAK,GAAG,YAAY,GAAG,CAAC,GAAG,YAAY,GAAG,aAAa,GAAG,YAAY;MAE5E,OAAO,KAAK,IAAI,cAAc;EAChC;;EClFM,SAAU,QAAQ,CACtB,KAAkB,EAClB,IAAmB,EACnB,aAAkC,EAAE,EAAA;MAEpC,IAAI,CAAC,IAAI,EAAE;EACT,QAAA,OAAO,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC;;MAGvF,MAAM,UAAU,GAAG,uBAAuB,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC;EAE9D,IAAA,IAAI,UAAU,KAAK,MAAM,EAAE;UACzB,OAAO,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC;;EAG9C,IAAA,IAAI,UAAU,KAAK,MAAM,EAAE;UACzB,OAAO,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC;;EAG9C,IAAA,OAAO,KAAK;EACd;;QCtBa,aAAa,GAAG,CAAC,KAAkB,EAAE,QAAiB,KAAI;MACrE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC,SAAS;MAE/C,IAAI,QAAQ,EAAE;UACZ,MAAM,UAAU,GAAG,cAAc,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC;UAEvF,IAAI,CAAC,UAAU,EAAE;EACf,YAAA,OAAO,KAAK;;EAGd,QAAA,MAAM,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC;UAExD,IAAI,OAAO,CAAC,GAAG,GAAG,CAAC,KAAK,UAAU,CAAC,GAAG,EAAE,EAAE;EACxC,YAAA,OAAO,IAAI;;EAGb,QAAA,OAAO,KAAK;;MAGd,IAAI,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,EAAE;EACvE,QAAA,OAAO,KAAK;;EAGd,IAAA,OAAO,IAAI;EACb;;AC1Ba,QAAA,eAAe,GAAG,CAAC,KAAkB,KAAI;MACpD,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,KAAK,CAAC,SAAS;EAEtC,IAAA,IAAI,KAAK,CAAC,YAAY,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,EAAE;EACnD,QAAA,OAAO,KAAK;;EAGd,IAAA,OAAO,IAAI;EACb;;ECJgB,SAAA,MAAM,CAAC,IAAY,EAAE,UAAsB,EAAA;MACzD,MAAM,EAAE,cAAc,EAAE,GAAG,eAAe,CAAC,UAAU,CAAC;EACtD,IAAA,MAAM,SAAS,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;MAEjE,IAAI,CAAC,SAAS,EAAE;EACd,QAAA,OAAO,KAAK;;EAGd,IAAA,MAAM,OAAO,GAAG;UACd,IAAI,EAAE,SAAS,CAAC,IAAI;UACpB,OAAO,EAAE,SAAS,CAAC,OAAO;UAC1B,OAAO,EAAE,SAAS,CAAC,OAAO;OAC3B;EACD,IAAA,MAAM,KAAK,GAAG,YAAY,CAAC,iBAAiB,CAAsB,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;EAE/F,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;EAC7B,QAAA,OAAO,KAAK;;MAGd,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC;EAC1C;;ECxBA;;EAEG;EACa,SAAA,WAAW,CACzB,IAAqB,EACrB,EACE,aAAa,GAAG,IAAI,EACpB,gBAAgB,GAAG,KAAK,MAUtB,EAAE,EAAA;;MAEN,IAAI,gBAAgB,EAAE;UACpB,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE;;EAElC,YAAA,OAAO,IAAI;;EAEb,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;cACf,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAA,EAAA,GAAA,IAAI,CAAC,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,EAAE,CAAC;;;EAIzC,IAAA,IAAI,IAAI,CAAC,MAAM,EAAE;EACf,QAAA,OAAO,CAAC,IAAI,CAAC,IAAI;;MAGnB,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE;EAC9B,QAAA,OAAO,KAAK;;MAGd,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,CAAC,EAAE;EACjC,QAAA,OAAO,IAAI;;MAGb,IAAI,aAAa,EAAE;UACjB,IAAI,cAAc,GAAG,IAAI;EAEzB,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,IAAG;EAC/B,YAAA,IAAI,cAAc,KAAK,KAAK,EAAE;;kBAE5B;;EAGF,YAAA,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,EAAE,gBAAgB,EAAE,aAAa,EAAE,CAAC,EAAE;kBAChE,cAAc,GAAG,KAAK;;EAE1B,SAAC,CAAC;EAEF,QAAA,OAAO,cAAc;;EAGvB,IAAA,OAAO,KAAK;EACd;;EC3DM,SAAU,eAAe,CAAC,KAAc,EAAA;MAC5C,OAAO,KAAK,YAAYC,mBAAa;EACvC;;WCAgB,YAAY,CAAC,IAAgB,EAAE,IAAY,EAAE,EAAU,EAAA;MACrE,MAAM,MAAM,GAAG,CAAC;MAChB,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI;MAC1C,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC;MACjD,MAAM,WAAW,GAAG,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC;MAC9C,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC;MAC5C,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;EAC7C,IAAA,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC;EACxC,IAAA,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC;EACjD,IAAA,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC;EAC3C,IAAA,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC;EAC9C,IAAA,MAAM,KAAK,GAAG,KAAK,GAAG,IAAI;EAC1B,IAAA,MAAM,MAAM,GAAG,MAAM,GAAG,GAAG;MAC3B,MAAM,CAAC,GAAG,IAAI;MACd,MAAM,CAAC,GAAG,GAAG;EACb,IAAA,MAAM,IAAI,GAAG;UACX,GAAG;UACH,MAAM;UACN,IAAI;UACJ,KAAK;UACL,KAAK;UACL,MAAM;UACN,CAAC;UACD,CAAC;OACF;MAED,OAAO;EACL,QAAA,GAAG,IAAI;EACP,QAAA,MAAM,EAAE,MAAM,IAAI;OACnB;EACH;;ECXA;;EAEG;EACH,SAAS,0BAA0B,CAAC,EAClC,IAAI,EACJ,UAAU,EACV,UAAU,EACV,OAAO,EACP,gBAAgB,GAAG,EAAE,GAOtB,EAAA;EAUC,IAAA,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;UAC3C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAG;EACpC,YAAA,MAAM,IAAI,GAAG,OAAO,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI;EAExD,YAAA,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;EACxB,gBAAA,OAAO,IAAI;;cAGb,gBAAgB,CAAC,IAAI,CAAC;kBACpB,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;EAC1C,gBAAA,WAAW,EAAE,IAAI;EAClB,aAAA,CAAC;;EAEF,YAAA,OAAO,KAAK;EACd,SAAC,CAAC;;EAGJ,IAAA,IAAI,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;EAC/C,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;EACjB,aAAA,GAAG,CACF,KAAK,IAAI,0BAA0B,CAAC;EAClC,YAAA,IAAI,EAAE,KAAK;cACX,UAAU;cACV,UAAU;cACV,OAAO;cACP,gBAAgB;WACjB,CAAC,CAAC,IAAI;EAER,aAAA,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,SAAS,CAAC;;EAG/C,IAAA,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;UAC3C,gBAAgB,CAAC,IAAI,CAAC;cACpB,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;cAC1C,WAAW,EAAE,IAAI,CAAC,IAAI;EACvB,SAAA,CAAC;UAEF,IAAI,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAA,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,mBAAmB,MAAK,KAAK,CAAC,EAAE;;EAE3F,YAAA,IAAI,CAAC,IAAI,GAAG,WAAW;cAEvB,OAAO;kBACL,IAAI;kBACJ,gBAAgB;eACjB;;;UAIH,OAAO;EACL,YAAA,IAAI,EAAE,IAAI;cACV,gBAAgB;WACjB;;EAGH,IAAA,OAAO,EAAE,IAAI,EAAE,gBAAgB,EAAE;EACnC;EAEA;;;EAGG;WACa,qBAAqB;EACnC;;EAEG;EACH,IAAiB;EACjB;;EAEG;EACH,MAAc;EACd;;EAEG;EACH,OAAsC,EAAA;EAoBtC,IAAA,OAAO,0BAA0B,CAAC;UAChC,IAAI;EACJ,QAAA,UAAU,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;EAC9C,QAAA,UAAU,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;UAC9C,OAAO;EACR,KAAA,CAAC;EACJ;;EC9HA,SAAS,UAAU,CAAC,KAAkB,EAAE,EAAe,EAAE,WAAqB,EAAA;;EAC5E,IAAA,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE;MACxB,IAAI,MAAM,GAAuB,IAAI;EAErC,IAAA,IAAI,eAAe,CAAC,SAAS,CAAC,EAAE;EAC9B,QAAA,MAAM,GAAG,SAAS,CAAC,OAAO;;MAG5B,IAAI,MAAM,EAAE;UACV,MAAM,YAAY,GAAG,CAAA,EAAA,GAAA,KAAK,CAAC,WAAW,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,MAAM,CAAC,KAAK,EAAE;;UAGxD,QACE,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,YAAY;EAC/B,eAAA,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;;EAIlE,IAAA,MAAM,EAAE,MAAM,EAAE,GAAG,SAAS;MAE5B,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,KAAI;EACpC,QAAA,IAAI,oBAAoB,GAAG,KAAK,CAAC,KAAK,KAAK;EACzC,cAAE,KAAK,CAAC,GAAG,CAAC,aAAa,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW;gBACpE,KAAK;UAET,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,KAAI;;cAEhE,IAAI,oBAAoB,EAAE;EACxB,gBAAA,OAAO,KAAK;;EAGd,YAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;EACjB,gBAAA,MAAM,oBAAoB,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;kBAC/E,MAAM,yBAAyB,GAAG,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK;EAC7D,uBAAA,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;EAExE,gBAAA,oBAAoB,GAAG,oBAAoB,IAAI,yBAAyB;;cAE1E,OAAO,CAAC,oBAAoB;EAC9B,SAAC,CAAC;EAEF,QAAA,OAAO,oBAAoB;EAC7B,KAAC,CAAC;EACJ;EACO,MAAM,OAAO,GAA2B,CAAC,UAAU,EAAE,UAAU,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;EAC1G,IAAA,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE;EACxB,IAAA,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,SAAS;MACnC,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;MAElD,IAAI,QAAQ,EAAE;UACZ,IAAI,KAAK,EAAE;cACT,MAAM,aAAa,GAAG,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC;EAEpD,YAAA,EAAE,CAAC,aAAa,CACd,IAAI,CAAC,MAAM,CAAC;EACV,gBAAA,GAAG,aAAa;EAChB,gBAAA,GAAG,UAAU;EACd,aAAA,CAAC,CACH;;eACI;EACL,YAAA,MAAM,CAAC,OAAO,CAAC,KAAK,IAAG;EACrB,gBAAA,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG;EAC5B,gBAAA,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG;EAExB,gBAAA,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,GAAG,KAAI;sBAC7C,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC;EACvC,oBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;EACnD,oBAAA,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;;;;sBAK/D,IAAI,WAAW,EAAE;EACf,wBAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,IAAG;EACxB,4BAAA,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;kCACtB,EAAE,CAAC,OAAO,CACR,WAAW,EACX,SAAS,EACT,IAAI,CAAC,MAAM,CAAC;sCACV,GAAG,IAAI,CAAC,KAAK;EACb,oCAAA,GAAG,UAAU;EACd,iCAAA,CAAC,CACH;;EAEL,yBAAC,CAAC;;2BACG;EACL,wBAAA,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;;EAE/D,iBAAC,CAAC;EACJ,aAAC,CAAC;;;MAIN,OAAO,UAAU,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC;EACpC,CAAC;;ECjGM,MAAM,OAAO,GAA2B,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC,EAAE,EAAE,EAAE,KAAI;EACxE,IAAA,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC;EAEtB,IAAA,OAAO,IAAI;EACb,CAAC;;ECFM,MAAM,OAAO,GAA2B,CAAC,UAAU,EAAE,UAAU,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAI;MAC7G,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;EAElD,IAAA,IAAI,gBAAiD;EAErD,IAAA,IAAI,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;;UAE7D,gBAAgB,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK;;;EAIzD,IAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;EACrB,QAAA,OAAO,CAAC,IAAI,CAAC,sEAAsE,CAAC;EAEpF,QAAA,OAAO,KAAK;;MAGd,QACE,KAAK;;EAEF,SAAA,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAE,KAAI;EACxB,QAAA,MAAM,WAAW,GAAGC,uBAAY,CAAC,IAAI,EAAE,EAAE,GAAG,gBAAgB,EAAE,GAAG,UAAU,EAAE,CAAC,CAAC,KAAK,CAAC;UAErF,IAAI,WAAW,EAAE;EACf,YAAA,OAAO,IAAI;;EAGb,QAAA,OAAO,QAAQ,CAAC,UAAU,EAAE;EAC9B,KAAC;WACA,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,KAAI;EACnC,QAAA,OAAOA,uBAAY,CAAC,IAAI,EAAE,EAAE,GAAG,gBAAgB,EAAE,GAAG,UAAU,EAAE,CAAC,CAAC,YAAY,EAAE,QAAQ,CAAC;EAC3F,KAAC;WACA,GAAG,EAAE;EAEZ,CAAC;;ECpCM,MAAM,gBAAgB,GAAoC,QAAQ,IAAI,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAI;MAChG,IAAI,QAAQ,EAAE;EACZ,QAAA,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;EAClB,QAAA,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC;UAClD,MAAM,SAAS,GAAGD,mBAAa,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EAEjD,QAAA,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC;;EAG5B,IAAA,OAAO,IAAI;EACb,CAAC;;ECVM,MAAM,gBAAgB,GAAoC,QAAQ,IAAI,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAI;MAChG,IAAI,QAAQ,EAAE;EACZ,QAAA,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;UAClB,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,OAAO,QAAQ,KAAK,QAAQ,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAG,QAAQ;UAC/F,MAAM,MAAM,GAAG3B,mBAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI;UAC9C,MAAM,MAAM,GAAGA,mBAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE;UAC1C,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC;UACjD,MAAM,WAAW,GAAG,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC;EAC9C,QAAA,MAAM,SAAS,GAAGA,mBAAa,CAAC,MAAM,CAAC,GAAG,EAAE,YAAY,EAAE,WAAW,CAAC;EAEtE,QAAA,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC;;EAG5B,IAAA,OAAO,IAAI;EACb,CAAC;;ECbM,MAAM,YAAY,GAAgC,UAAU,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;MAC7F,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;MAElD,OAAO6B,uBAAoB,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC;EACpD,CAAC;;EChBD,SAAS,WAAW,CAAC,KAAkB,EAAE,eAA0B,EAAA;MACjE,MAAM,KAAK,GAAG,KAAK,CAAC,WAAW,KAAK,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;MAEtG,IAAI,KAAK,EAAE;UACT,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,eAAe,KAAA,IAAA,IAAf,eAAe,KAAf,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,eAAe,CAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAErF,QAAA,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,aAAa,CAAC;;EAEvC;EAgBO,MAAM,UAAU,GAA8B,CAAC,EAAE,SAAS,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,EACnF,EAAE,SAAE9B,OAAK,EAAE,QAAQ,EAAE,MAAM,GAC5B,KAAI;EACH,IAAA,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE;EAC7B,IAAA,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,SAAS;EAChC,IAAA,MAAM,mBAAmB,GAAG,MAAM,CAAC,gBAAgB,CAAC,UAAU;MAC9D,MAAM,aAAa,GAAG,qBAAqB,CACzC,mBAAmB,EACnB,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,EACtB,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CACnB;MAED,IAAI,SAAS,YAAY4B,mBAAa,IAAI,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE;EAChE,QAAA,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,CAACG,kBAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE;EACpD,YAAA,OAAO,KAAK;;UAGd,IAAI,QAAQ,EAAE;cACZ,IAAI,SAAS,EAAE;kBACb,WAAW,CAAC/B,OAAK,EAAE,MAAM,CAAC,gBAAgB,CAAC,eAAe,CAAC;;cAG7D,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE;;EAGtC,QAAA,OAAO,IAAI;;EAGb,IAAA,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE;EACzB,QAAA,OAAO,KAAK;;EAGd,IAAA,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,KAAK,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI;EAE1D,IAAA,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,KAAK;EAC5B,UAAE;YACA,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAEvE,IAAA,IAAI,KAAK,GAAG,KAAK,IAAI;EACnB,UAAE;EACA,YAAA;EACE,gBAAA,IAAI,EAAE,KAAK;EACX,gBAAA,KAAK,EAAE,aAAa;EACrB,aAAA;EACF;YACC,SAAS;MAEb,IAAI,GAAG,GAAG+B,kBAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;EAE/D,IAAA,IACE,CAAC;EACI,WAAA,CAAC;EACD,WAAAA,kBAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,GAAG,SAAS,CAAC,EAC1F;UACA,GAAG,GAAG,IAAI;EACV,QAAA,KAAK,GAAG;EACN,cAAE;EACA,gBAAA;EACE,oBAAA,IAAI,EAAE,KAAK;EACX,oBAAA,KAAK,EAAE,aAAa;EACrB,iBAAA;EACF;gBACC,SAAS;;MAGf,IAAI,QAAQ,EAAE;UACZ,IAAI,GAAG,EAAE;EACP,YAAA,IAAI,SAAS,YAAY9B,mBAAa,EAAE;kBACtC,EAAE,CAAC,eAAe,EAAE;;EAGtB,YAAA,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;EAE7C,YAAA,IAAI,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE;EACzE,gBAAA,MAAM,KAAK,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;kBAC5C,MAAM,MAAM,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC;kBAEpC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,EAAE;EAC5E,oBAAA,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC;;;;UAK7D,IAAI,SAAS,EAAE;cACb,WAAW,CAACD,OAAK,EAAE,MAAM,CAAC,gBAAgB,CAAC,eAAe,CAAC;;UAG7D,EAAE,CAAC,cAAc,EAAE;;EAGrB,IAAA,OAAO,GAAG;EACZ,CAAC;;EClGM,MAAM,aAAa,GAAiC,CAAC,UAAU,EAAE,aAAa,GAAG,EAAE,KAAK,CAAC,EAC9F,EAAE,SAAEA,OAAK,EAAE,QAAQ,EAAE,MAAM,GAC5B,KAAI;;MACH,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAEA,OAAK,CAAC,MAAM,CAAC;MAClD,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAGA,OAAK,CAAC,SAAS;;;EAIpC,IAAA,MAAM,IAAI,GAAoBA,OAAK,CAAC,SAAS,CAAC,IAAI;MAEpD,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;EACvE,QAAA,OAAO,KAAK;;MAGd,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;EAElC,IAAA,IAAI,WAAW,CAAC,IAAI,KAAK,IAAI,EAAE;EAC7B,QAAA,OAAO,KAAK;;EAGd,IAAA,MAAM,mBAAmB,GAAG,MAAM,CAAC,gBAAgB,CAAC,UAAU;EAE9D,IAAA,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,KAAK,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;;;;EAIzF,QAAA,IACE,KAAK,CAAC,KAAK,KAAK;iBACX,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK;EACxB,eAAA,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,EACtD;EACA,YAAA,OAAO,KAAK;;UAGd,IAAI,QAAQ,EAAE;EACZ,YAAA,IAAI,IAAI,GAAGL,cAAQ,CAAC,KAAK;;EAEvB,YAAA,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;;;cAInE,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,KAAK,GAAG,WAAW,EAAE,CAAC,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;EACpE,gBAAA,IAAI,GAAGA,cAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;cAI9C,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC;;EAGpI,YAAA,MAAM,qBAAqB,GAAG;EAC5B,gBAAA,GAAG,qBAAqB,CACtB,mBAAmB,EACnB,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,EACtB,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CACnB;EACD,gBAAA,GAAG,aAAa;eACjB;EACD,YAAA,MAAM,QAAQ,GAAG,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,YAAY,CAAC,WAAW,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,aAAa,CAAC,qBAAqB,CAAC,KAAI,SAAS;cAEjG,IAAI,GAAG,IAAI,CAAC,MAAM,CAACA,cAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,SAAS,CAAC,CAAC;EAElF,YAAA,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC;cAE3D,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,EAAE,IAAIqC,WAAK,CAAC,IAAI,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,CAAC,CAAC;EAEhF,YAAA,IAAI,GAAG,GAAG,CAAC,CAAC;cAEZ,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,GAAG,KAAI;EACzD,gBAAA,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE;EACZ,oBAAA,OAAO,KAAK;;EAGd,gBAAA,IAAI,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,EAAE;EACzC,oBAAA,GAAG,GAAG,GAAG,GAAG,CAAC;;EAEjB,aAAC,CAAC;EAEF,YAAA,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE;EACZ,gBAAA,EAAE,CAAC,YAAY,CAAC/B,mBAAa,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;;cAG1D,EAAE,CAAC,cAAc,EAAE;;EAGrB,QAAA,OAAO,IAAI;;MAGb,MAAM,QAAQ,GAAG,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI;EAE3F,IAAA,MAAM,iBAAiB,GAAG;EACxB,QAAA,GAAG,qBAAqB,CACtB,mBAAmB,EACnB,WAAW,CAAC,IAAI,CAAC,IAAI,EACrB,WAAW,CAAC,KAAK,CAClB;EACD,QAAA,GAAG,aAAa;OACjB;EACD,IAAA,MAAM,qBAAqB,GAAG;EAC5B,QAAA,GAAG,qBAAqB,CACtB,mBAAmB,EACnB,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,EACtB,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CACnB;EACD,QAAA,GAAG,aAAa;OACjB;MAED,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC;MAE7B,MAAM,KAAK,GAAG;EACZ,UAAE;EACA,YAAA,EAAE,IAAI,EAAE,KAAK,EAAE,iBAAiB,EAAE;EAClC,YAAA,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,qBAAqB,EAAE;EACjD;YACC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,iBAAiB,EAAE,CAAC;EAExC,IAAA,IAAI,CAAC8B,kBAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE;EACnC,QAAA,OAAO,KAAK;;MAGd,IAAI,QAAQ,EAAE;EACZ,QAAA,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG/B,OAAK;EACxC,QAAA,MAAM,EAAE,eAAe,EAAE,GAAG,MAAM,CAAC,gBAAgB;EACnD,QAAA,MAAM,KAAK,GAAG,WAAW,KAAK,SAAS,CAAC,GAAG,CAAC,YAAY,IAAI,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;EAEpF,QAAA,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,cAAc,EAAE;EAE9C,QAAA,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE;EACvB,YAAA,OAAO,IAAI;;UAGb,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAEpF,QAAA,EAAE,CAAC,WAAW,CAAC,aAAa,CAAC;;EAG/B,IAAA,OAAO,IAAI;EACb,CAAC;;ECvJD,MAAM,iBAAiB,GAAG,CAAC,EAAe,EAAE,QAAkB,KAAa;EACzE,IAAA,MAAM,IAAI,GAAG,cAAc,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC;MAEzE,IAAI,CAAC,IAAI,EAAE;EACT,QAAA,OAAO,IAAI;;EAGb,IAAA,MAAM,MAAM,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;EAE3E,IAAA,IAAI,MAAM,KAAK,SAAS,EAAE;EACxB,QAAA,OAAO,IAAI;;MAGb,MAAM,UAAU,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC;MACxC,MAAM,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,MAAK,UAAU,KAAV,IAAA,IAAA,UAAU,KAAV,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAU,CAAE,IAAI,CAAA,IAAIiC,iBAAO,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC;MAEzF,IAAI,CAAC,gBAAgB,EAAE;EACrB,QAAA,OAAO,IAAI;;EAGb,IAAA,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;EAEjB,IAAA,OAAO,IAAI;EACb,CAAC;EAED,MAAM,gBAAgB,GAAG,CAAC,EAAe,EAAE,QAAkB,KAAa;EACxE,IAAA,MAAM,IAAI,GAAG,cAAc,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC;MAEzE,IAAI,CAAC,IAAI,EAAE;EACT,QAAA,OAAO,IAAI;;EAGb,IAAA,MAAM,KAAK,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;EAE1D,IAAA,IAAI,KAAK,KAAK,SAAS,EAAE;EACvB,QAAA,OAAO,IAAI;;MAGb,MAAM,SAAS,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC;MACtC,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,MAAK,SAAS,KAAA,IAAA,IAAT,SAAS,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAT,SAAS,CAAE,IAAI,CAAA,IAAIA,iBAAO,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC;MAEpF,IAAI,CAAC,eAAe,EAAE;EACpB,QAAA,OAAO,IAAI;;EAGb,IAAA,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;EAEd,IAAA,OAAO,IAAI;EACb,CAAC;EAkBM,MAAM,UAAU,GAA8B,CAAC,cAAc,EAAE,cAAc,EAAE,SAAS,EAAE,UAAU,GAAG,EAAE,KAAK,CAAC,EACpH,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,GAClD,KAAI;MACH,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,GAAG,MAAM,CAAC,gBAAgB;MAC/D,MAAM,QAAQ,GAAG,WAAW,CAAC,cAAc,EAAE,KAAK,CAAC,MAAM,CAAC;MAC1D,MAAM,QAAQ,GAAG,WAAW,CAAC,cAAc,EAAE,KAAK,CAAC,MAAM,CAAC;EAC1D,IAAA,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,KAAK;EACxC,IAAA,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,SAAS;MAChC,MAAM,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC;EAEnC,IAAA,MAAM,KAAK,GAAG,WAAW,KAAK,SAAS,CAAC,GAAG,CAAC,YAAY,IAAI,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;MAEpF,IAAI,CAAC,KAAK,EAAE;EACV,QAAA,OAAO,KAAK;;MAGd,MAAM,UAAU,GAAG,cAAc,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC;EAExF,IAAA,IAAI,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,UAAU,IAAI,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,IAAI,CAAC,EAAE;;UAEzE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;EACrC,YAAA,OAAO,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC;;;UAIxC,IACE,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU;iBACvC,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO;EAC7C,eAAA,QAAQ,EACb;EACA,YAAA,OAAO,KAAK;mBACT,OAAO,CAAC,MAAK;kBACZ,EAAE,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,EAAE,QAAQ,CAAC;EAE1C,gBAAA,OAAO,IAAI;EACb,aAAC;mBACA,OAAO,CAAC,MAAM,iBAAiB,CAAC,EAAE,EAAE,QAAQ,CAAC;mBAC7C,OAAO,CAAC,MAAM,gBAAgB,CAAC,EAAE,EAAE,QAAQ,CAAC;EAC5C,iBAAA,GAAG,EAAE;;;MAGZ,IAAI,CAAC,SAAS,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE;EAErC,QAAA,OAAO,KAAK;;eAET,OAAO,CAAC,MAAK;cACZ,MAAM,aAAa,GAAG,GAAG,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC;cAE5D,IAAI,aAAa,EAAE;EACjB,gBAAA,OAAO,IAAI;;EAGb,YAAA,OAAO,QAAQ,CAAC,UAAU,EAAE;EAC9B,SAAC;EACA,aAAA,UAAU,CAAC,QAAQ,EAAE,UAAU;eAC/B,OAAO,CAAC,MAAM,iBAAiB,CAAC,EAAE,EAAE,QAAQ,CAAC;eAC7C,OAAO,CAAC,MAAM,gBAAgB,CAAC,EAAE,EAAE,QAAQ,CAAC;EAC5C,aAAA,GAAG,EAAE;;MAGV,QACE,KAAK;;WAEF,OAAO,CAAC,MAAK;UACZ,MAAM,aAAa,GAAG,GAAG,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC;UAE5D,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAEpF,QAAA,EAAE,CAAC,WAAW,CAAC,aAAa,CAAC;UAE7B,IAAI,aAAa,EAAE;EACjB,YAAA,OAAO,IAAI;;EAGb,QAAA,OAAO,QAAQ,CAAC,UAAU,EAAE;EAC9B,KAAC;EACA,SAAA,UAAU,CAAC,QAAQ,EAAE,UAAU;WAC/B,OAAO,CAAC,MAAM,iBAAiB,CAAC,EAAE,EAAE,QAAQ,CAAC;WAC7C,OAAO,CAAC,MAAM,gBAAgB,CAAC,EAAE,EAAE,QAAQ,CAAC;WAC5C,GAAG,EAAE;EAEZ,CAAC;;ECtHM,MAAM,UAAU,GAA8B,CAAC,UAAU,EAAE,UAAU,GAAG,EAAE,EAAE,OAAO,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;EAC1H,IAAA,MAAM,EAAE,oBAAoB,GAAG,KAAK,EAAE,GAAG,OAAO;MAChD,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;MAClD,MAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC;MAEtD,IAAI,QAAQ,EAAE;UACZ,OAAO,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,EAAE,oBAAoB,EAAE,CAAC;;MAG3D,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC;EAC3C,CAAC;;ECvBM,MAAM,UAAU,GAA8B,CAAC,UAAU,EAAE,gBAAgB,EAAE,UAAU,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;MAC9H,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;MAClD,MAAM,UAAU,GAAG,WAAW,CAAC,gBAAgB,EAAE,KAAK,CAAC,MAAM,CAAC;MAC9D,MAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC;EAEtD,IAAA,IAAI,gBAAiD;EAErD,IAAA,IAAI,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;;UAE7D,gBAAgB,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK;;MAGzD,IAAI,QAAQ,EAAE;UACZ,OAAO,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE,gBAAgB,CAAC;;;;EAKvD,IAAA,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,GAAG,gBAAgB,EAAE,GAAG,UAAU,EAAE,CAAC;EACvE,CAAC;;ECxBM,MAAM,UAAU,GAA8B,CAAC,UAAU,EAAE,UAAU,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;MAC5G,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;MAClD,MAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC;MAEtD,IAAI,QAAQ,EAAE;EACZ,QAAA,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;;MAG5B,OAAO,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC;EAC1C,CAAC;;ECfM,MAAM,aAAa,GAAiC,MAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;EACvF,IAAA,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO;EAE7B,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;EAC1C,QAAA,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC;EACzB,QAAA,IAAI,QAAQ;;;EAIZ,QAAA,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,KAAK,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;cACnE,IAAI,QAAQ,EAAE;EACZ,gBAAA,MAAM,EAAE,GAAG,KAAK,CAAC,EAAE;EACnB,gBAAA,MAAM,MAAM,GAAG,QAAQ,CAAC,SAAS;EAEjC,gBAAA,KAAK,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;sBACpD,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGjD,gBAAA,IAAI,QAAQ,CAAC,IAAI,EAAE;EACjB,oBAAA,MAAM,KAAK,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE;sBAEnD,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;;uBAC9E;sBACL,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,CAAC;;;EAIzC,YAAA,OAAO,IAAI;;;EAIf,IAAA,OAAO,KAAK;EACd,CAAC;;EChCM,MAAM,aAAa,GAAiC,MAAM,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAI;EACpF,IAAA,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE;EACxB,IAAA,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,SAAS;MAEnC,IAAI,KAAK,EAAE;EACT,QAAA,OAAO,IAAI;;MAGb,IAAI,QAAQ,EAAE;EACZ,QAAA,MAAM,CAAC,OAAO,CAAC,KAAK,IAAG;EACrB,YAAA,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;EAC/C,SAAC,CAAC;;EAGJ,IAAA,OAAO,IAAI;EACb,CAAC;;ECGM,MAAM,SAAS,GAA6B,CAAC,UAAU,EAAE,OAAO,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;;EAC3G,IAAA,MAAM,EAAE,oBAAoB,GAAG,KAAK,EAAE,GAAG,OAAO;EAChD,IAAA,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE;MACxB,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;MAClD,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,SAAS;MAE1C,IAAI,CAAC,QAAQ,EAAE;EACb,QAAA,OAAO,IAAI;;EAGb,IAAA,IAAI,KAAK,IAAI,oBAAoB,EAAE;EACjC,QAAA,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,SAAS;UAC5B,MAAM,KAAK,GAAG,CAAA,EAAA,GAAA,KAAK,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAK;UACnE,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;UAE9C,IAAI,KAAK,EAAE;EACT,YAAA,IAAI,GAAG,KAAK,CAAC,IAAI;EACjB,YAAA,EAAE,GAAG,KAAK,CAAC,EAAE;;UAGf,EAAE,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC;;WACxB;EACL,QAAA,MAAM,CAAC,OAAO,CAAC,KAAK,IAAG;EACrB,YAAA,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC;EACrD,SAAC,CAAC;;EAGJ,IAAA,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC;EAEzB,IAAA,OAAO,IAAI;EACb,CAAC;;EC5BM,MAAM,gBAAgB,GAAoC,CAAC,UAAU,EAAE,UAAU,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;MAE5H,IAAI,QAAQ,GAAoB,IAAI;MACpC,IAAI,QAAQ,GAAoB,IAAI;MAEpC,MAAM,UAAU,GAAG,uBAAuB,CACxC,OAAO,UAAU,KAAK,QAAQ,GAAG,UAAU,GAAG,UAAU,CAAC,IAAI,EAC7D,KAAK,CAAC,MAAM,CACb;MAED,IAAI,CAAC,UAAU,EAAE;EACf,QAAA,OAAO,KAAK;;EAGd,IAAA,IAAI,UAAU,KAAK,MAAM,EAAE;UACzB,QAAQ,GAAG,WAAW,CAAC,UAAsB,EAAE,KAAK,CAAC,MAAM,CAAC;;EAG9D,IAAA,IAAI,UAAU,KAAK,MAAM,EAAE;UACzB,QAAQ,GAAG,WAAW,CAAC,UAAsB,EAAE,KAAK,CAAC,MAAM,CAAC;;MAG9D,IAAI,QAAQ,EAAE;UACZ,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAqB,KAAI;EAEpD,YAAA,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG;EAC5B,YAAA,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG;EAExB,YAAA,IAAI,OAA2B;EAC/B,YAAA,IAAI,QAA0B;EAC9B,YAAA,IAAI,WAAmB;EACvB,YAAA,IAAI,SAAiB;EAErB,YAAA,IAAI,EAAE,CAAC,SAAS,CAAC,KAAK,EAAE;EACtB,gBAAA,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,IAAU,EAAE,GAAW,KAAI;sBAE3D,IAAI,QAAQ,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,EAAE;0BACtC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC;EACjC,wBAAA,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;0BAC7C,OAAO,GAAG,GAAG;0BACb,QAAQ,GAAG,IAAI;;EAEnB,iBAAC,CAAC;;mBACG;EACL,gBAAA,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,IAAU,EAAE,GAAW,KAAI;EAE3D,oBAAA,IAAI,GAAG,GAAG,IAAI,IAAI,QAAQ,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,EAAE;0BACpD,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC;EACjC,wBAAA,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;0BAC7C,OAAO,GAAG,GAAG;0BACb,QAAQ,GAAG,IAAI;;sBAGjB,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE,EAAE;0BAE5B,IAAI,QAAQ,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,EAAE;EACtC,4BAAA,EAAE,CAAC,aAAa,CAAC,GAAG,EAAE,SAAS,EAAE;kCAC/B,GAAG,IAAI,CAAC,KAAK;EACb,gCAAA,GAAG,UAAU;EACd,6BAAA,CAAC;;0BAGJ,IAAI,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;8BACjC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAU,KAAI;EAEhC,gCAAA,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,EAAE;sCAC1B,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC;EACxC,oCAAA,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;sCAEpD,EAAE,CAAC,OAAO,CACR,YAAY,EACZ,UAAU,EACV,QAAQ,CAAC,MAAM,CAAC;0CACd,GAAG,IAAI,CAAC,KAAK;EACb,wCAAA,GAAG,UAAU;EACd,qCAAA,CAAC,CACH;;EAEL,6BAAC,CAAC;;;EAGR,iBAAC,CAAC;;cAGJ,IAAI,QAAQ,EAAE;EAEZ,gBAAA,IAAI,OAAO,KAAK,SAAS,EAAE;EACzB,oBAAA,EAAE,CAAC,aAAa,CAAC,OAAO,EAAE,SAAS,EAAE;0BACnC,GAAG,QAAQ,CAAC,KAAK;EACjB,wBAAA,GAAG,UAAU;EACd,qBAAA,CAAC;;kBAGJ,IAAI,QAAQ,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE;sBACrC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAU,KAAI;EAEpC,wBAAA,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,EAAE;8BAC1B,EAAE,CAAC,OAAO,CACR,WAAW,EACX,SAAS,EACT,QAAQ,CAAC,MAAM,CAAC;kCACd,GAAG,IAAI,CAAC,KAAK;EACb,gCAAA,GAAG,UAAU;EACd,6BAAA,CAAC,CACH;;EAEL,qBAAC,CAAC;;;EAGR,SAAC,CAAC;;EAGJ,IAAA,OAAO,IAAI;EACb,CAAC;;EC/HM,MAAM,MAAM,GAA0B,CAAC,UAAU,EAAE,UAAU,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;MACpG,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;MAElD,OAAOC,iBAAc,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC;EAC1D,CAAC;;ECJM,MAAM,UAAU,GAA8B,CAAC,UAAU,EAAE,UAAU,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;MAC5G,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;MAElD,OAAOC,qBAAkB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC;EAC9D,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ECnBM,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC;EACvC,IAAA,IAAI,EAAE,UAAU;MAEhB,WAAW,GAAA;UACT,OAAO;EACL,YAAA,GAAG,QAAQ;WACZ;OACF;EACF,CAAA,CAAC;;ECTK,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC;EACnC,IAAA,IAAI,EAAE,MAAM;MAEZ,qBAAqB,GAAA;UACnB,OAAO;EACL,YAAA,IAAIzC,YAAM,CAAC;EACT,gBAAA,GAAG,EAAE,IAAIG,eAAS,CAAC,YAAY,CAAC;EAEhC,gBAAA,KAAK,EAAE;sBACL,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,KAAI;EACjC,wBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE;8BACvB,MAAM,EAAE,IAAI,CAAC,MAAM;EACnB,4BAAA,KAAK,EAAE,CAAC;8BACR,KAAK;8BACL,KAAK;EACN,yBAAA,CAAC;uBACH;EACF,iBAAA;eACF,CAAC;WACH;OACF;EACF,CAAA,CAAC;;ECrBK,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC;EACvC,IAAA,IAAI,EAAE,UAAU;MAEhB,qBAAqB,GAAA;UACnB,OAAO;EACL,YAAA,IAAIH,YAAM,CAAC;EACT,gBAAA,GAAG,EAAE,IAAIG,eAAS,CAAC,UAAU,CAAC;EAC9B,gBAAA,KAAK,EAAE;sBACL,QAAQ,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ;EAC7C,iBAAA;eACF,CAAC;WACH;OACF;EACF,CAAA,CAAC;;ECbK,MAAM,oBAAoB,GAAG,IAAIA,eAAS,CAAC,aAAa,CAAC;EAEzD,MAAM,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC;EAC1C,IAAA,IAAI,EAAE,aAAa;MAEnB,qBAAqB,GAAA;EACnB,QAAA,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI;UAEvB,OAAO;EACL,YAAA,IAAIH,YAAM,CAAC;EACT,gBAAA,GAAG,EAAE,oBAAoB;EACzB,gBAAA,KAAK,EAAE;EACL,oBAAA,eAAe,EAAE;EACf,wBAAA,KAAK,EAAE,CAAC,IAAI,EAAE,KAAY,KAAI;EAC5B,4BAAA,MAAM,CAAC,SAAS,GAAG,IAAI;EAEvB,4BAAA,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC;EAC9B,iCAAA,OAAO,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE;EAC1B,iCAAA,OAAO,CAAC,cAAc,EAAE,KAAK,CAAC;EAEjC,4BAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;EAE1B,4BAAA,OAAO,KAAK;2BACb;EACD,wBAAA,IAAI,EAAE,CAAC,IAAI,EAAE,KAAY,KAAI;EAC3B,4BAAA,MAAM,CAAC,SAAS,GAAG,KAAK;EAExB,4BAAA,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC;EAC9B,iCAAA,OAAO,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE;EACzB,iCAAA,OAAO,CAAC,cAAc,EAAE,KAAK,CAAC;EAEjC,4BAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;EAE1B,4BAAA,OAAO,KAAK;2BACb;EACF,qBAAA;EACF,iBAAA;eACF,CAAC;WACH;OACF;EACF,CAAA,CAAC;;ECnCK,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;EACrC,IAAA,IAAI,EAAE,QAAQ;MAEd,oBAAoB,GAAA;EAClB,QAAA,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,EAAE,KAAK;EACzE,YAAA,MAAM,QAAQ,CAAC,aAAa,EAAE;;cAG9B,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,KAAI;EAChC,gBAAA,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE;EAC7B,gBAAA,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,SAAS;EACpC,gBAAA,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,OAAO;EAC/B,gBAAA,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,WAAW,IAAI,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,OAAO;kBAC5F,MAAM,iBAAiB,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS;kBAE/D,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,YAAY;EAEpD,gBAAA,MAAM,SAAS,GAAG,CAAC,iBAAiB,IAAI,UAAU,CAAC,MAAM,CAAC,UAAU,KAAK,CAAC;EACxE,sBAAE,SAAS,KAAK,OAAO,CAAC;wBACtBU,eAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,GAAG;EAEvC,gBAAA,IACE,CAAC;EACE,uBAAA,CAAC,MAAM,CAAC,IAAI,CAAC;yBACb,MAAM,CAAC,WAAW,CAAC;EACnB,uBAAA,CAAC;EACD,wBAAC,SAAS,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,CAAC;oBAC1D;EACA,oBAAA,OAAO,KAAK;;EAGd,gBAAA,OAAO,QAAQ,CAAC,UAAU,EAAE;EAC9B,aAAC,CAAC;EAEF,YAAA,MAAM,QAAQ,CAAC,eAAe,EAAE;EAChC,YAAA,MAAM,QAAQ,CAAC,YAAY,EAAE;EAC7B,YAAA,MAAM,QAAQ,CAAC,kBAAkB,EAAE;EACpC,SAAA,CAAC;EAEF,QAAA,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,EAAE,KAAK;EACtE,YAAA,MAAM,QAAQ,CAAC,eAAe,EAAE;EAChC,YAAA,MAAM,QAAQ,CAAC,iBAAiB,EAAE;EAClC,YAAA,MAAM,QAAQ,CAAC,WAAW,EAAE;EAC5B,YAAA,MAAM,QAAQ,CAAC,iBAAiB,EAAE;EACnC,SAAA,CAAC;EAEF,QAAA,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,EAAE,KAAK;EACrE,YAAA,MAAM,QAAQ,CAAC,aAAa,EAAE;EAC9B,YAAA,MAAM,QAAQ,CAAC,mBAAmB,EAAE;EACpC,YAAA,MAAM,QAAQ,CAAC,cAAc,EAAE;EAC/B,YAAA,MAAM,QAAQ,CAAC,UAAU,EAAE;EAC5B,SAAA,CAAC;EAEF,QAAA,MAAM,UAAU,GAAG;EACjB,YAAA,KAAK,EAAE,WAAW;cAClB,WAAW,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE;EAClD,YAAA,SAAS,EAAE,eAAe;EAC1B,YAAA,eAAe,EAAE,eAAe;EAChC,YAAA,iBAAiB,EAAE,eAAe;EAClC,YAAA,MAAM,EAAE,YAAY;EACpB,YAAA,YAAY,EAAE,YAAY;cAC1B,OAAO,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE;WAChD;EAED,QAAA,MAAM,QAAQ,GAAG;EACf,YAAA,GAAG,UAAU;WACd;EAED,QAAA,MAAM,SAAS,GAAG;EAChB,YAAA,GAAG,UAAU;EACb,YAAA,QAAQ,EAAE,eAAe;EACzB,YAAA,eAAe,EAAE,eAAe;EAChC,YAAA,QAAQ,EAAE,YAAY;EACtB,YAAA,oBAAoB,EAAE,YAAY;EAClC,YAAA,YAAY,EAAE,YAAY;EAC1B,YAAA,OAAO,EAAE,YAAY;cACrB,QAAQ,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,oBAAoB,EAAE;cAC3D,QAAQ,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,kBAAkB,EAAE;WAC1D;EAED,QAAA,IAAI,KAAK,EAAE,IAAI,OAAO,EAAE,EAAE;EACxB,YAAA,OAAO,SAAS;;EAGlB,QAAA,OAAO,QAAQ;OAChB;MAED,qBAAqB,GAAA;UACnB,OAAO;;;;;;EAML,YAAA,IAAIV,YAAM,CAAC;EACT,gBAAA,GAAG,EAAE,IAAIG,eAAS,CAAC,eAAe,CAAC;kBACnC,iBAAiB,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,QAAQ,KAAI;EACtD,oBAAA,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE;0BACtD;;EAGF,oBAAA,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,UAAU;6BACrE,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC;EAEnC,oBAAA,MAAM,QAAQ,GAAG,YAAY,CAAC,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;EAE9F,oBAAA,IAAI,CAAC,UAAU,IAAI,QAAQ,EAAE;0BAC3B;;sBAGF,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,QAAQ,CAAC,SAAS;EAC9C,oBAAA,MAAM,OAAO,GAAGO,eAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI;EACpD,oBAAA,MAAM,MAAM,GAAGA,eAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;sBAC/C,MAAM,cAAc,GAAG,IAAI,KAAK,OAAO,IAAI,EAAE,KAAK,MAAM;EAExD,oBAAA,IAAI,KAAK,IAAI,CAAC,cAAc,EAAE;0BAC5B;;sBAGF,MAAM,OAAO,GAAG,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC;sBAEzC,IAAI,CAAC,OAAO,EAAE;0BACZ;;EAGF,oBAAA,MAAM,EAAE,GAAG,QAAQ,CAAC,EAAE;sBACtB,MAAMJ,OAAK,GAAG,oBAAoB,CAAC;EACjC,wBAAA,KAAK,EAAE,QAAQ;EACf,wBAAA,WAAW,EAAE,EAAE;EAChB,qBAAA,CAAC;EACF,oBAAA,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,cAAc,CAAC;0BACtC,MAAM,EAAE,IAAI,CAAC,MAAM;iCACnBA,OAAK;EACN,qBAAA,CAAC;sBAEF,QAAQ,CAAC,UAAU,EAAE;EAErB,oBAAA,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE;0BACpB;;EAGF,oBAAA,OAAO,EAAE;mBACV;eACF,CAAC;WACH;OACF;EACF,CAAA,CAAC;;ECvJK,MAAM,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC;EACpC,IAAA,IAAI,EAAE,OAAO;MAEb,qBAAqB,GAAA;UAEnB,OAAO;EACL,YAAA,IAAIN,YAAM,CAAC;EACT,gBAAA,GAAG,EAAE,IAAIG,eAAS,CAAC,aAAa,CAAC;EAEjC,gBAAA,KAAK,EAAE;sBACL,WAAW,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,KAAI;EAC/B,wBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;8BACxB,MAAM,EAAE,IAAI,CAAC,MAAM;EACnB,4BAAA,KAAK,EAAE,CAAC;8BACR,KAAK;EACN,yBAAA,CAAC;uBACH;EACF,iBAAA;eACF,CAAC;WACH;OACF;EACF,CAAA,CAAC;;ECrBK,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC;EACvC,IAAA,IAAI,EAAE,UAAU;MAEhB,qBAAqB,GAAA;UACnB,OAAO;EACL,YAAA,IAAIH,YAAM,CAAC;EACT,gBAAA,GAAG,EAAE,IAAIG,eAAS,CAAC,UAAU,CAAC;EAC9B,gBAAA,KAAK,EAAE;sBACL,UAAU,EAAE,OAAoC,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EACjG,iBAAA;eACF,CAAC;WACH;OACF;EACF,CAAA,CAAC;;;;;;;;;;;;;;;QCVW,OAAO,CAAA;EAOlB,IAAA,IAAY,IAAI,GAAA;EACd,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;;MAG5B,WAAY,CAAA,GAAgB,EAAE,MAAc,EAAE,OAAO,GAAG,KAAK,EAAE,IAAA,GAAoB,IAAI,EAAA;UAO/E,IAAW,CAAA,WAAA,GAAgB,IAAI;UAUhC,IAAW,CAAA,WAAA,GAAkB,IAAI;EAhBtC,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO;EACtB,QAAA,IAAI,CAAC,WAAW,GAAG,GAAG;EACtB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM;EACpB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI;;EAKzB,IAAA,IAAI,IAAI,GAAA;UACN,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;;EAGpD,IAAA,IAAI,OAAO,GAAA;EACT,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAmB;;EAKhE,IAAA,IAAI,KAAK,GAAA;;UACP,OAAO,CAAA,EAAA,GAAA,IAAI,CAAC,WAAW,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,IAAI,CAAC,WAAW,CAAC,KAAK;;EAGnD,IAAA,IAAI,GAAG,GAAA;EACL,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG;;EAG7B,IAAA,IAAI,OAAO,GAAA;EACT,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO;;MAG1B,IAAI,OAAO,CAAC,OAAgB,EAAA;EAC1B,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI;EACpB,QAAA,IAAI,EAAE,GAAG,IAAI,CAAC,EAAE;EAEhB,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;cAChB,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,EAAE;EAC3B,gBAAA,OAAO,CAAC,KAAK,CAAC,CAAA,+DAAA,EAAkE,IAAI,CAAC,IAAI,CAAA,IAAA,EAAO,IAAI,CAAC,GAAG,CAAA,CAAE,CAAC;kBAC3G;;EAGF,YAAA,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC;EACpB,YAAA,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC;;EAGlB,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC;;EAG7D,IAAA,IAAI,UAAU,GAAA;EACZ,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK;;EAGxB,IAAA,IAAI,WAAW,GAAA;EACb,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW;;EAG9B,IAAA,IAAI,IAAI,GAAA;EACN,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ;;EAG3B,IAAA,IAAI,IAAI,GAAA;EACN,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;cAChB,OAAO,IAAI,CAAC,GAAG;;EAGjB,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;;EAGvD,IAAA,IAAI,KAAK,GAAA;UACP,OAAO;cACL,IAAI,EAAE,IAAI,CAAC,IAAI;cACf,EAAE,EAAE,IAAI,CAAC,EAAE;WACZ;;EAGH,IAAA,IAAI,EAAE,GAAA;EACJ,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;EAChB,YAAA,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI;;EAG7B,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;;EAGlF,IAAA,IAAI,MAAM,GAAA;EACR,QAAA,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE;EACpB,YAAA,OAAO,IAAI;;EAGb,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC;EACpE,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC;UAEpD,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;;EAGvC,IAAA,IAAI,MAAM,GAAA;EACR,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;UAE3E,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;EAC7B,YAAA,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;;UAGpD,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;;EAGvC,IAAA,IAAI,KAAK,GAAA;EACP,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;UAEzE,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;EAC7B,YAAA,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;;UAGlD,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;;EAGvC,IAAA,IAAI,QAAQ,GAAA;UACV,MAAM,QAAQ,GAAc,EAAE;EAE9B,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,MAAM,KAAI;cACzC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW;cACjD,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM;EAEjD,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,IAAI,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;EAC7D,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC;cAEpD,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,EAAE;kBACxC;;cAGF,MAAM,YAAY,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC;cAEnF,IAAI,OAAO,EAAE;kBACX,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC;;cAG3C,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;EAC/E,SAAC,CAAC;EAEF,QAAA,OAAO,QAAQ;;EAGjB,IAAA,IAAI,UAAU,GAAA;UACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI;;EAGjC,IAAA,IAAI,SAAS,GAAA;EACX,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ;UAE9B,OAAO,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI;;EAG9C,IAAA,OAAO,CAAC,QAAgB,EAAE,UAAA,GAAqC,EAAE,EAAA;UAC/D,IAAI,IAAI,GAAmB,IAAI;EAC/B,QAAA,IAAI,WAAW,GAAG,IAAI,CAAC,MAAM;EAE7B,QAAA,OAAO,WAAW,IAAI,CAAC,IAAI,EAAE;cAC3B,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;kBAC3C,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;EACtC,oBAAA,MAAM,cAAc,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK;sBAC7C,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;EAExC,oBAAA,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,QAAQ,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,EAAE;EACvD,wBAAA,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC;0BAE3B,IAAI,cAAc,CAAC,GAAG,CAAC,KAAK,UAAU,CAAC,GAAG,CAAC,EAAE;8BAC3C;;;;uBAGC;sBACL,IAAI,GAAG,WAAW;;;EAItB,YAAA,WAAW,GAAG,WAAW,CAAC,MAAM;;EAGlC,QAAA,OAAO,IAAI;;EAGb,IAAA,aAAa,CAAC,QAAgB,EAAE,UAAA,GAAqC,EAAE,EAAA;EACrE,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;;MAGrE,gBAAgB,CAAC,QAAgB,EAAE,UAAA,GAAqC,EAAE,EAAE,aAAa,GAAG,KAAK,EAAA;UAC/F,IAAI,KAAK,GAAc,EAAE;EAEzB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;EAChD,YAAA,OAAO,KAAK;;UAEd,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;EAExC;;;EAGG;EACH,QAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,IAAG;;cAE/B,IAAI,aAAa,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;kBACrC;;cAGF,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;kBACxC,MAAM,sBAAsB,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;kBAElG,IAAI,sBAAsB,EAAE;EAC1B,oBAAA,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;;;;cAKxB,IAAI,aAAa,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;kBACrC;;EAGF,YAAA,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;EACtF,SAAC,CAAC;EAEF,QAAA,OAAO,KAAK;;EAGd,IAAA,YAAY,CAAC,UAAkC,EAAA;UAC7C,MAAM,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK;UAEhC,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE;EACrC,YAAA,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK;EAClB,YAAA,GAAG,UAAU;EACd,SAAA,CAAC;UAEF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;;EAEhC;;ECvPM,MAAM,KAAK,GAAG,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA0EnB;;WC1Ec,cAAc,CAAC,KAAa,EAAE,KAAc,EAAE,MAAe,EAAA;MAC3E,MAAM,cAAc,GAAsB,QAAQ,CAAC,aAAa,CAAC,CAAA,uBAAA,EAA0B,MAAM,GAAG,CAAI,CAAA,EAAA,MAAM,EAAE,GAAG,EAAE,CAAA,CAAA,CAAG,CAAE;EAE1H,IAAA,IAAI,cAAc,KAAK,IAAI,EAAE;EAC3B,QAAA,OAAO,cAAc;;MAGvB,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;MAEjD,IAAI,KAAK,EAAE;EACT,QAAA,SAAS,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC;;EAGxC,IAAA,SAAS,CAAC,YAAY,CAAC,oBAAoB,MAAM,GAAG,CAAA,CAAA,EAAI,MAAM,CAAE,CAAA,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5E,IAAA,SAAS,CAAC,SAAS,GAAG,KAAK;EAC3B,IAAA,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC;EAE/D,IAAA,OAAO,SAAS;EAClB;;EC8BM,MAAO,MAAO,SAAQ,YAA0B,CAAA;EAiDpD,IAAA,WAAA,CAAY,UAAkC,EAAE,EAAA;EAC9C,QAAA,KAAK,EAAE;UAvCF,IAAS,CAAA,SAAA,GAAG,KAAK;EAExB;;EAEG;UACI,IAAa,CAAA,aAAA,GAAG,KAAK;UAErB,IAAgB,CAAA,gBAAA,GAAwB,EAAE;EAE1C,QAAA,IAAA,CAAA,OAAO,GAAkB;EAC9B,YAAA,OAAO,EAAE,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;EACtC,YAAA,OAAO,EAAE,EAAE;EACX,YAAA,SAAS,EAAE,IAAI;EACf,YAAA,WAAW,EAAE,SAAS;EACtB,YAAA,UAAU,EAAE,EAAE;EACd,YAAA,SAAS,EAAE,KAAK;EAChB,YAAA,QAAQ,EAAE,IAAI;EACd,YAAA,WAAW,EAAE,EAAE;EACf,YAAA,YAAY,EAAE,EAAE;EAChB,YAAA,oBAAoB,EAAE,EAAE;EACxB,YAAA,gBAAgB,EAAE,IAAI;EACtB,YAAA,gBAAgB,EAAE,IAAI;EACtB,YAAA,oBAAoB,EAAE,IAAI;EAC1B,YAAA,kBAAkB,EAAE,KAAK;EACzB,YAAA,gBAAgB,EAAE,KAAK;EACvB,YAAA,cAAc,EAAE,MAAM,IAAI;EAC1B,YAAA,QAAQ,EAAE,MAAM,IAAI;EACpB,YAAA,QAAQ,EAAE,MAAM,IAAI;EACpB,YAAA,iBAAiB,EAAE,MAAM,IAAI;EAC7B,YAAA,aAAa,EAAE,MAAM,IAAI;EACzB,YAAA,OAAO,EAAE,MAAM,IAAI;EACnB,YAAA,MAAM,EAAE,MAAM,IAAI;EAClB,YAAA,SAAS,EAAE,MAAM,IAAI;EACrB,YAAA,cAAc,EAAE,CAAC,EAAE,KAAK,EAAE,KAAI,EAAG,MAAM,KAAK,CAAA,EAAE;EAC9C,YAAA,OAAO,EAAE,MAAM,IAAI;EACnB,YAAA,MAAM,EAAE,MAAM,IAAI;WACnB;UAgUM,IAAsB,CAAA,sBAAA,GAAG,KAAK;UAE7B,IAAmB,CAAA,mBAAA,GAAuB,IAAI;EA9TpD,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;UACxB,IAAI,CAAC,sBAAsB,EAAE;UAC7B,IAAI,CAAC,oBAAoB,EAAE;UAC3B,IAAI,CAAC,YAAY,EAAE;UACnB,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;UACpD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;UAC3C,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;UACpD,IAAI,CAAC,UAAU,EAAE;UACjB,IAAI,CAAC,SAAS,EAAE;UAChB,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;UACxC,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;UACxC,IAAI,CAAC,EAAE,CAAC,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC;UAC1D,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;UAClD,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;UACtC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;UACpC,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;EAC1C,QAAA,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;UACtF,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;EAE1E,QAAA,MAAM,CAAC,UAAU,CAAC,MAAK;EACrB,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;kBACpB;;cAGF,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;cAC3C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;EACrC,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI;WAC1B,EAAE,CAAC,CAAC;;EAGP;;EAEG;EACH,IAAA,IAAW,OAAO,GAAA;UAChB,OAAO,IAAI,CAAC,gBAAgB;;EAG9B;;EAEG;EACH,IAAA,IAAW,QAAQ,GAAA;EACjB,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,QAAQ;;EAGrC;;EAEG;MACI,KAAK,GAAA;EACV,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;;EAGpC;;EAEG;MACI,GAAG,GAAA;EACR,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE;;EAGlC;;EAEG;MACK,SAAS,GAAA;UACf,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,QAAQ,EAAE;EACtC,YAAA,IAAI,CAAC,GAAG,GAAG,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;;;EAI9D;;;;EAIG;MACI,UAAU,CAAC,UAAkC,EAAE,EAAA;UACpD,IAAI,CAAC,OAAO,GAAG;cACb,GAAG,IAAI,CAAC,OAAO;EACf,YAAA,GAAG,OAAO;WACX;EAED,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,WAAW,EAAE;cACjD;;EAGF,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;cAC5B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;;UAG9C,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;;EAGnC;;EAEG;EACI,IAAA,WAAW,CAAC,QAAiB,EAAE,UAAU,GAAG,IAAI,EAAA;EACrD,QAAA,IAAI,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,CAAC;UAE7B,IAAI,UAAU,EAAE;EACd,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC;;;EAIrE;;EAEG;EACH,IAAA,IAAW,UAAU,GAAA;;;;EAInB,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ;;EAGjE;;EAEG;EACH,IAAA,IAAW,KAAK,GAAA;EACd,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK;;EAGxB;;;;;;EAMG;MACI,cAAc,CACnB,MAAc,EACd,aAAkE,EAAA;EAElE,QAAA,MAAM,OAAO,GAAG,UAAU,CAAC,aAAa;EACtC,cAAE,aAAa,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;gBAC7C,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC;EAEnC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE,CAAC;EAEjD,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;EAE5B,QAAA,OAAO,KAAK;;EAGd;;;;;EAKG;EACI,IAAA,gBAAgB,CAAC,uBAAoE,EAAA;EAC1F,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;EACpB,YAAA,OAAO,SAAS;;EAGlB,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO;UACtC,IAAI,OAAO,GAAG,WAAW;UAExB,EAA6B,CAAC,MAAM,CAAC,uBAAuB,CAAC,CAAC,OAAO,CAAC,eAAe,IAAG;;EAEvF,YAAA,MAAM,IAAI,GAAG,OAAO,eAAe,KAAK,QAAQ,GAAG,CAAG,EAAA,eAAe,GAAG,GAAG,eAAe,CAAC,GAAG;;EAG9F,YAAA,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;EAClE,SAAC,CAAC;UAEF,IAAI,WAAW,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,EAAE;;EAEzC,YAAA,OAAO,SAAS;;EAGlB,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;cACnC,OAAO;EACR,SAAA,CAAC;EAEF,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;EAE5B,QAAA,OAAO,KAAK;;EAGd;;EAEG;MACK,sBAAsB,GAAA;;UAE5B,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,GAAG;cACzD,QAAQ;cACR,uBAAuB,CAAC,SAAS,CAAC;kBAChC,cAAc,EAAE,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,oBAAoB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,uBAAuB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,cAAc;eAC3F,CAAC;cACF,QAAQ;cACR,WAAW;cACX,MAAM;cACN,QAAQ;cACR,IAAI;cACJ,KAAK;EACN,SAAA,CAAC,MAAM,CAAC,GAAG,IAAG;cACb,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,KAAK,QAAQ,EAAE;EACzD,gBAAA,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAsD,CAAC,KAAK,KAAK;;EAEhH,YAAA,OAAO,IAAI;EACb,SAAC,CAAC,GAAG,EAAE;EACP,QAAA,MAAM,aAAa,GAAG,CAAC,GAAG,cAAc,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,SAAS,IAAG;EACvF,YAAA,OAAO,CAAC,WAAW,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,SAAS,aAAT,SAAS,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAT,SAAS,CAAE,IAAI,CAAC;EAChE,SAAC,CAAC;UAEF,IAAI,CAAC,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC;;EAGnE;;EAEG;MACK,oBAAoB,GAAA;EAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,CAAC;EACvC,YAAA,MAAM,EAAE,IAAI;EACb,SAAA,CAAC;;EAGJ;;EAEG;MACK,YAAY,GAAA;UAClB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM;;EAG5C;;EAEG;MACK,UAAU,GAAA;;EAChB,QAAA,IAAI,GAAoB;EAExB,QAAA,IAAI;EACF,YAAA,GAAG,GAAG,cAAc,CAClB,IAAI,CAAC,OAAO,CAAC,OAAO,EACpB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,OAAO,CAAC,YAAY,EACzB,EAAE,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAC3D;;UACD,OAAO,CAAC,EAAE;cACV,IAAI,EAAE,CAAC,YAAY,KAAK,CAAC,IAAI,CAAC,CAAC,sCAAsC,EAAE,sCAAsC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE;;EAElI,gBAAA,MAAM,CAAC;;EAET,YAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;EACxB,gBAAA,MAAM,EAAE,IAAI;EACZ,gBAAA,KAAK,EAAE,CAAU;kBACjB,oBAAoB,EAAE,MAAK;EACzB,oBAAA,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;0BAC9B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,GAAG,IAAI;;;sBAG9C,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,KAAK,eAAe,CAAC;;sBAGzG,IAAI,CAAC,sBAAsB,EAAE;mBAC9B;EACF,aAAA,CAAC;;cAGF,GAAG,GAAG,cAAc,CAClB,IAAI,CAAC,OAAO,CAAC,OAAO,EACpB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,OAAO,CAAC,YAAY,EACzB,EAAE,qBAAqB,EAAE,KAAK,EAAE,CACjC;;EAEH,QAAA,MAAM,SAAS,GAAG,oBAAoB,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;UAEnE,IAAI,CAAC,IAAI,GAAG,IAAIuC,eAAU,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;EAC/C,YAAA,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW;EAC3B,YAAA,UAAU,EAAE;;EAEV,gBAAA,IAAI,EAAE,SAAS;EACf,gBAAA,GAAG,MAAA,IAAI,CAAC,OAAO,CAAC,WAAW,0CAAE,UAAU;EACxC,aAAA;cACD,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC;EACxD,YAAA,KAAK,EAAEC,iBAAW,CAAC,MAAM,CAAC;kBACxB,GAAG;kBACH,SAAS,EAAE,SAAS,IAAI,SAAS;eAClC,CAAC;EACH,SAAA,CAAC;;;EAIF,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;EACtC,YAAA,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO;EACvC,SAAA,CAAC;EAEF,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;UAE/B,IAAI,CAAC,eAAe,EAAE;UACtB,IAAI,CAAC,YAAY,EAAE;;;;EAKnB,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAA8B;EAEpD,QAAA,GAAG,CAAC,MAAM,GAAG,IAAI;;EAGnB;;EAEG;MACI,eAAe,GAAA;EACpB,QAAA,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;cACzB;;EAGF,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;EACjB,YAAA,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,SAAS;EAC3C,SAAA,CAAC;;EAGJ;;EAEG;MACI,YAAY,GAAA;EACjB,QAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,CAAA,OAAA,EAAU,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE;;EAOxD,IAAA,kBAAkB,CAAC,EAAc,EAAA;EACtC,QAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI;EAClC,QAAA,EAAE,EAAE;EACJ,QAAA,IAAI,CAAC,sBAAsB,GAAG,KAAK;EAEnC,QAAA,MAAM,EAAE,GAAG,IAAI,CAAC,mBAAmB;EAEnC,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI;EAE/B,QAAA,OAAO,EAAE;;EAGX;;;;EAIG;EACK,IAAA,mBAAmB,CAAC,WAAwB,EAAA;;;EAGlD,QAAA,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;cACzB;;EAGF,QAAA,IAAI,IAAI,CAAC,sBAAsB,EAAE;EAC/B,YAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;EAC7B,gBAAA,IAAI,CAAC,mBAAmB,GAAG,WAAW;kBAEtC;;cAGF,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,IAAG,EAAA,IAAA,EAAA,CAAA,CAAC,OAAA,CAAA,EAAA,GAAA,IAAI,CAAC,mBAAmB,0CAAE,IAAI,CAAC,IAAI,CAAC,CAAA,EAAA,CAAC;cAEvE;;UAGF,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC;EAC3C,QAAA,MAAM,mBAAmB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC;EAErE,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;EAC7B,YAAA,MAAM,EAAE,IAAI;cACZ,WAAW;EACX,YAAA,SAAS,EAAE,KAAK;EACjB,SAAA,CAAC;EACF,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;EAC5B,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;EACvB,YAAA,MAAM,EAAE,IAAI;cACZ,WAAW;EACZ,SAAA,CAAC;UAEF,IAAI,mBAAmB,EAAE;EACvB,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;EAC3B,gBAAA,MAAM,EAAE,IAAI;kBACZ,WAAW;EACZ,aAAA,CAAC;;UAGJ,MAAM,KAAK,GAAG,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC;UAC1C,MAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC;UAExC,IAAI,KAAK,EAAE;EACT,YAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;EACjB,gBAAA,MAAM,EAAE,IAAI;kBACZ,KAAK,EAAE,KAAK,CAAC,KAAK;kBAClB,WAAW;EACZ,aAAA,CAAC;;UAGJ,IAAI,IAAI,EAAE;EACR,YAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;EAChB,gBAAA,MAAM,EAAE,IAAI;kBACZ,KAAK,EAAE,IAAI,CAAC,KAAK;kBACjB,WAAW;EACZ,aAAA,CAAC;;EAGJ,QAAA,IAAI,CAAC,WAAW,CAAC,UAAU,IAAI,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE;cACnE;;EAGF,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;EAClB,YAAA,MAAM,EAAE,IAAI;cACZ,WAAW;EACZ,SAAA,CAAC;;EAGJ;;EAEG;EACI,IAAA,aAAa,CAAC,UAAwC,EAAA;UAC3D,OAAO,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC;;MAWvC,QAAQ,CAAC,gBAAwB,EAAE,qBAA0B,EAAA;EAClE,QAAA,MAAM,IAAI,GAAG,OAAO,gBAAgB,KAAK,QAAQ,GAAG,gBAAgB,GAAG,IAAI;EAE3E,QAAA,MAAM,UAAU,GAAG,OAAO,gBAAgB,KAAK,QAAQ,GAAG,qBAAqB,GAAG,gBAAgB;UAElG,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC;;EAG/C;;EAEG;MACI,OAAO,GAAA;UACZ,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE;;EAGhC;;EAEG;MACI,OAAO,GAAA;EACZ,QAAA,OAAO,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC;;EAGjE;;EAEG;EACI,IAAA,OAAO,CAAC,OAGd,EAAA;EACC,QAAA,MAAM,EAAE,cAAc,GAAG,MAAM,EAAE,eAAe,GAAG,EAAE,EAAE,GAAG,OAAO,IAAI,EAAE;EAEvE,QAAA,OAAO,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;cAC7B,cAAc;EACd,YAAA,eAAe,EAAE;EACf,gBAAA,GAAG,4BAA4B,CAAC,IAAI,CAAC,MAAM,CAAC;EAC5C,gBAAA,GAAG,eAAe;EACnB,aAAA;EACF,SAAA,CAAC;;EAGJ;;EAEG;EACH,IAAA,IAAW,OAAO,GAAA;UAChB,OAAO,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;;EAGpC;;;;EAIG;MACI,iBAAiB,GAAA;EACtB,QAAA,OAAO,CAAC,IAAI,CACV,6HAA6H,CAC9H;UAED,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC;;EAGxC;;EAEG;MACI,OAAO,GAAA;EACZ,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;EAEpB,QAAA,IAAI,IAAI,CAAC,IAAI,EAAE;;;EAGb,YAAA,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAA8B;EAEpD,YAAA,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE;kBACrB,OAAO,GAAG,CAAC,MAAM;;EAEnB,YAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;;UAGrB,IAAI,CAAC,kBAAkB,EAAE;;EAG3B;;EAEG;EACH,IAAA,IAAW,WAAW,GAAA;;;UAEpB,OAAO,EAAC,CAAA,EAAA,GAAA,IAAI,CAAC,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,CAAA;;MAGrB,KAAK,CAAC,QAAgB,EAAE,UAAmC,EAAA;;EAChE,QAAA,OAAO,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,IAAI,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,aAAa,CAAC,QAAQ,EAAE,UAAU,CAAC,KAAI,IAAI;;MAGxD,MAAM,CAAC,QAAgB,EAAE,UAAmC,EAAA;;EACjE,QAAA,OAAO,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,IAAI,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,gBAAgB,CAAC,QAAQ,EAAE,UAAU,CAAC,KAAI,IAAI;;EAG3D,IAAA,IAAI,CAAC,GAAW,EAAA;EACrB,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC;EAExC,QAAA,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;;EAGhC,IAAA,IAAI,IAAI,GAAA;EACN,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEtB;;EC1mBD;;;;EAIG;EACG,SAAU,aAAa,CAAC,MAQ7B,EAAA;MACC,OAAO,IAAI,SAAS,CAAC;UACnB,IAAI,EAAE,MAAM,CAAC,IAAI;UACjB,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAI;EACnC,YAAA,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,aAAa,EAAE,SAAS,EAAE,KAAK,CAAC;cAEvE,IAAI,UAAU,KAAK,KAAK,IAAI,UAAU,KAAK,IAAI,EAAE;EAC/C,gBAAA,OAAO,IAAI;;EAGb,YAAA,MAAM,EAAE,EAAE,EAAE,GAAG,KAAK;cACpB,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;EAC5C,YAAA,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC;cAE1B,IAAI,YAAY,EAAE;kBAChB,MAAM,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;EAC1C,gBAAA,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC,OAAO,CAAC,YAAY,CAAC;EAC9D,gBAAA,MAAM,OAAO,GAAG,SAAS,GAAG,YAAY,CAAC,MAAM;EAE/C,gBAAA,MAAM,aAAa,GAAG,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,GAAG;uBAClE,MAAM,CAAC,IAAI,IAAG;;sBAEb,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAsB;sBAEtD,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;EAC/E,iBAAC;uBACA,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC;EAEtC,gBAAA,IAAI,aAAa,CAAC,MAAM,EAAE;EACxB,oBAAA,OAAO,IAAI;;EAGb,gBAAA,IAAI,OAAO,GAAG,KAAK,CAAC,EAAE,EAAE;sBACtB,EAAE,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC;;EAG9B,gBAAA,IAAI,SAAS,GAAG,KAAK,CAAC,IAAI,EAAE;sBAC1B,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,WAAW,EAAE,SAAS,CAAC;;kBAGhD,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,GAAG,WAAW,GAAG,YAAY,CAAC,MAAM;kBAE9D,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,WAAW,EAAE,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC;EAEnF,gBAAA,EAAE,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC;;WAEnC;EACF,KAAA,CAAC;EACJ;;EC9DA;;;;EAIG;EACG,SAAU,aAAa,CAAC,MAoB7B,EAAA;MACC,OAAO,IAAI,SAAS,CAAC;UACnB,IAAI,EAAE,MAAM,CAAC,IAAI;UACjB,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAI;EACnC,YAAA,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,aAAa,EAAE,SAAS,EAAE,KAAK,CAAC,IAAI,EAAE;EAC7E,YAAA,MAAM,EAAE,EAAE,EAAE,GAAG,KAAK;EACpB,YAAA,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI;EACxB,YAAA,IAAI,GAAG,GAAG,KAAK,CAAC,EAAE;cAElB,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;EAE9C,YAAA,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;EACZ,gBAAA,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC7C,gBAAA,IAAI,UAAU,GAAG,KAAK,GAAG,MAAM;EAE/B,gBAAA,IAAI,UAAU,GAAG,GAAG,EAAE;sBACpB,UAAU,GAAG,GAAG;;uBACX;sBACL,GAAG,GAAG,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM;;;EAIpC,gBAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;EAE9C,gBAAA,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;;kBAGpD,EAAE,CAAC,WAAW,CAAC,UAAU,EAAE,GAAG,EAAE,OAAO,CAAC;;EACnC,iBAAA,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;EACnB,gBAAA,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,KAAK,GAAG,KAAK,GAAG,CAAC;EAE/D,gBAAA,EAAE,CAAC,MAAM,CAAC,cAAc,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAC9D,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EACrB,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CACpB;;cAGH,EAAE,CAAC,cAAc,EAAE;WACpB;EACF,KAAA,CAAC;EACJ;;ECjEA;;;;;;EAMG;EACG,SAAU,sBAAsB,CAAC,MAQtC,EAAA;MACC,OAAO,IAAI,SAAS,CAAC;UACnB,IAAI,EAAE,MAAM,CAAC,IAAI;UACjB,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAI;EACnC,YAAA,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;EAC5C,YAAA,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,aAAa,EAAE,SAAS,EAAE,KAAK,CAAC,IAAI,EAAE;EAE7E,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;EACzF,gBAAA,OAAO,IAAI;;EAGb,YAAA,KAAK,CAAC;mBACH,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;EAC3B,iBAAA,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC;WACjE;EACF,KAAA,CAAC;EACJ;;ECnCA;;;;EAIG;EACG,SAAU,aAAa,CAAC,MAG7B,EAAA;MACC,OAAO,IAAI,SAAS,CAAC;UACnB,IAAI,EAAE,MAAM,CAAC,IAAI;UACjB,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAI;EACnC,YAAA,IAAI,MAAM,GAAG,MAAM,CAAC,OAAO;EAC3B,YAAA,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI;EACtB,YAAA,MAAM,GAAG,GAAG,KAAK,CAAC,EAAE;EAEpB,YAAA,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;EACZ,gBAAA,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EAE7C,gBAAA,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;kBAClD,KAAK,IAAI,MAAM;EAEf,gBAAA,MAAM,MAAM,GAAG,KAAK,GAAG,GAAG;EAE1B,gBAAA,IAAI,MAAM,GAAG,CAAC,EAAE;EACd,oBAAA,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,EAAE,MAAM,CAAC,GAAG,MAAM;sBACzD,KAAK,GAAG,GAAG;;;cAIf,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC;WACxC;EACF,KAAA,CAAC;EACJ;;EC3BA;;;;;;;;;;;;;;EAcG;EACG,SAAU,iBAAiB,CAAC,MAajC,EAAA;MACC,OAAO,IAAI,SAAS,CAAC;UACnB,IAAI,EAAE,MAAM,CAAC,IAAI;EACjB,QAAA,OAAO,EAAE,CAAC,EACR,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,GAC3B,KAAI;EACH,YAAA,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,aAAa,EAAE,SAAS,EAAE,KAAK,CAAC,IAAI,EAAE;EAC7E,YAAA,MAAM,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;EAChD,YAAA,MAAM,MAAM,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;EACzC,YAAA,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,EAAE;EACtC,YAAA,MAAM,QAAQ,GAAG,UAAU,IAAIC,sBAAY,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC;cAEhF,IAAI,CAAC,QAAQ,EAAE;EACb,gBAAA,OAAO,IAAI;;EAGb,YAAA,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC;cAE7B,IAAI,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,MAAM,EAAE;EACrC,gBAAA,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,KAAK;kBACxC,MAAM,EAAE,eAAe,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB;EAC1D,gBAAA,MAAM,KAAK,GAAG,WAAW,KAAK,SAAS,CAAC,GAAG,CAAC,YAAY,IAAI,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;kBAEpF,IAAI,KAAK,EAAE;sBACT,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAEpF,oBAAA,EAAE,CAAC,WAAW,CAAC,aAAa,CAAC;;;EAGjC,YAAA,IAAI,MAAM,CAAC,cAAc,EAAE;;kBAEzB,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,aAAa,GAAG,UAAU,GAAG,UAAU;kBAElH,KAAK,EAAE,CAAC,gBAAgB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,GAAG,EAAE;;EAGtD,YAAA,MAAM,MAAM,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,UAAU;EAExD,YAAA,IACE;EACG,mBAAA,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC;qBACvBL,iBAAO,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,GAAG,CAAC;EAC9B,oBAAC,CAAC,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,EACjE;kBACA,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;;WAE1B;EACF,KAAA,CAAC;EACJ;;ECipBA;;;EAGG;QACU,IAAI,CAAA;EAkBf,IAAA,WAAA,CAAY,SAAgD,EAAE,EAAA;UAjB9D,IAAI,CAAA,IAAA,GAAG,MAAM;UAEb,IAAI,CAAA,IAAA,GAAG,MAAM;UAEb,IAAM,CAAA,MAAA,GAAgB,IAAI;UAE1B,IAAK,CAAA,KAAA,GAAgB,IAAI;EAMzB,QAAA,IAAA,CAAA,MAAM,GAAe;cACnB,IAAI,EAAE,IAAI,CAAC,IAAI;EACf,YAAA,cAAc,EAAE,EAAE;WACnB;UAGC,IAAI,CAAC,MAAM,GAAG;cACZ,GAAG,IAAI,CAAC,MAAM;EACd,YAAA,GAAG,MAAM;WACV;UAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;EAE5B,QAAA,IAAI,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;cAC1E,OAAO,CAAC,IAAI,CACV,CAAA,sHAAA,EAAyH,IAAI,CAAC,IAAI,CAAI,EAAA,CAAA,CACvI;;;UAIH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc;EAEzC,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;cAC1B,IAAI,CAAC,OAAO,GAAG,YAAY,CACzB,iBAAiB,CAA0B,IAAI,EAAE,YAAY,EAAE;kBAC7D,IAAI,EAAE,IAAI,CAAC,IAAI;EAChB,aAAA,CAAC,CACH;;UAGH,IAAI,CAAC,OAAO,GAAG,YAAY,CACzB,iBAAiB,CAA0B,IAAI,EAAE,YAAY,EAAE;cAC7D,IAAI,EAAE,IAAI,CAAC,IAAI;cACf,OAAO,EAAE,IAAI,CAAC,OAAO;WACtB,CAAC,CACH,IAAI,EAAE;;EAGT,IAAA,OAAO,MAAM,CAAmB,MAAA,GAAoC,EAAE,EAAA;EACpE,QAAA,OAAO,IAAI,IAAI,CAAO,MAAM,CAAC;;MAG/B,SAAS,CAAC,UAA4B,EAAE,EAAA;;;EAGtC,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAmB;cAC9C,GAAG,IAAI,CAAC,MAAM;cACd,UAAU,EAAE,MAAK;kBACf,OAAO,SAAS,CAAC,IAAI,CAAC,OAA8B,EAAE,OAAO,CAAY;eAC1E;EACF,SAAA,CAAC;;EAGF,QAAA,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;;EAE1B,QAAA,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;EAE9B,QAAA,OAAO,SAAS;;MAGlB,MAAM,CACJ,iBAAwE,EAAE,EAAA;EAE1E,QAAA,MAAM,SAAS,GAAG,IAAI,IAAI,CAAmC,cAAc,CAAC;EAE5E,QAAA,SAAS,CAAC,MAAM,GAAG,IAAI;EAEvB,QAAA,IAAI,CAAC,KAAK,GAAG,SAAS;UAEtB,SAAS,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI;EAElF,QAAA,IAAI,cAAc,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;cAC1F,OAAO,CAAC,IAAI,CACV,CAAA,sHAAA,EAAyH,SAAS,CAAC,IAAI,CAAI,EAAA,CAAA,CAC5I;;UAGH,SAAS,CAAC,OAAO,GAAG,YAAY,CAC9B,iBAAiB,CAA0B,SAAS,EAAE,YAAY,EAAE;cAClE,IAAI,EAAE,SAAS,CAAC,IAAI;EACrB,SAAA,CAAC,CACH;UAED,SAAS,CAAC,OAAO,GAAG,YAAY,CAC9B,iBAAiB,CAA0B,SAAS,EAAE,YAAY,EAAE;cAClE,IAAI,EAAE,SAAS,CAAC,IAAI;cACpB,OAAO,EAAE,SAAS,CAAC,OAAO;EAC3B,SAAA,CAAC,CACH;EAED,QAAA,OAAO,SAAS;;EAEnB;;EC10BD;;;EAGG;QACU,QAAQ,CAAA;EA2BnB,IAAA,WAAA,CAAY,SAAoB,EAAE,KAA4B,EAAE,OAA0B,EAAA;UAF1F,IAAU,CAAA,UAAA,GAAG,KAAK;EAGhB,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS;EAC1B,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAoB;UACxC,IAAI,CAAC,OAAO,GAAG;EACb,YAAA,SAAS,EAAE,IAAI;EACf,YAAA,cAAc,EAAE,IAAI;EACpB,YAAA,GAAG,OAAO;WACA;EACZ,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS;EAChC,QAAA,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI;EACtB,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,WAAmC;EAC5D,QAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,gBAAgB;EAC9C,QAAA,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI;EACtB,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,cAAc;EAC1C,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM;UAC1B,IAAI,CAAC,KAAK,EAAE;;MAGd,KAAK,GAAA;;UAEH;;EAGF,IAAA,IAAI,GAAG,GAAA;EACL,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAkB;;EAG5C,IAAA,IAAI,UAAU,GAAA;EACZ,QAAA,OAAO,IAAI;;EAGb,IAAA,WAAW,CAAC,KAAgB,EAAA;;EAC1B,QAAA,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM;EAC5B,QAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAAqB;;;EAI1C,QAAA,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,KAAK;gBACnC,MAAA,MAAM,CAAC,aAAa,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,CAAC,oBAAoB;EACpD,cAAE,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC;EAExC,QAAA,IAAI,CAAC,IAAI,CAAC,GAAG,KAAI,CAAA,EAAA,GAAA,IAAI,CAAC,UAAU,0CAAE,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAI,CAAC,UAAU,EAAE;cACjE;;UAGF,IAAI,CAAC,GAAG,CAAC;UACT,IAAI,CAAC,GAAG,CAAC;;EAGT,QAAA,IAAI,IAAI,CAAC,GAAG,KAAK,UAAU,EAAE;cAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,qBAAqB,EAAE;EAC/C,YAAA,MAAM,SAAS,GAAG,UAAU,CAAC,qBAAqB,EAAE;;EAGpD,YAAA,MAAM,OAAO,GAAG,CAAA,EAAA,GAAA,KAAK,CAAC,OAAO,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,CAAA,EAAA,GAAC,KAAa,CAAC,WAAW,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO;EACpE,YAAA,MAAM,OAAO,GAAG,CAAA,EAAA,GAAA,KAAK,CAAC,OAAO,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,CAAA,EAAA,GAAC,KAAa,CAAC,WAAW,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO;cAEpE,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,OAAO;cACpC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,OAAO;;UAGtC,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAgB;EAE1D,QAAA,CAAA,EAAA,GAAA,KAAK,CAAC,YAAY,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,YAAY,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;EAElD,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE;EAEzB,QAAA,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;cAC3B;;;;EAIF,QAAA,MAAM,SAAS,GAAGL,mBAAa,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC;EAC3D,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC;EAEzD,QAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;;EAG5B,IAAA,SAAS,CAAC,KAAY,EAAA;;EACpB,QAAA,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;EACb,YAAA,OAAO,KAAK;;UAGd,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,UAAU,EAAE;cAChD,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,CAAC;;EAG1C,QAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAAqB;UAC1C,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAC,CAAA,EAAA,GAAA,IAAI,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,QAAQ,CAAC,MAAM,CAAC,CAAA;;UAGnF,IAAI,CAAC,WAAW,EAAE;EAChB,YAAA,OAAO,KAAK;;UAGd,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;EACjD,QAAA,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,KAAK,MAAM;UACzC,MAAM,OAAO,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,iBAAiB;;UAG9G,IAAI,OAAO,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,EAAE;EAC3C,YAAA,OAAO,IAAI;;EAGb,QAAA,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,MAAM;EAClC,QAAA,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI;EAC3B,QAAA,MAAM,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS;UACnD,MAAM,YAAY,GAAGA,mBAAa,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;EAC1D,QAAA,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,KAAK,MAAM;EACzC,QAAA,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,KAAK,OAAO;EAC3C,QAAA,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,KAAK,KAAK;EACvC,QAAA,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,KAAK,WAAW;;;;EAK/C,QAAA,IAAI,CAAC,WAAW,IAAI,YAAY,IAAI,WAAW,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,GAAG,EAAE;cAC5E,KAAK,CAAC,cAAc,EAAE;;EAGxB,QAAA,IAAI,WAAW,IAAI,WAAW,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,GAAG,EAAE;cAC1E,KAAK,CAAC,cAAc,EAAE;EACtB,YAAA,OAAO,KAAK;;;UAId,IAAI,WAAW,IAAI,UAAU,IAAI,CAAC,UAAU,IAAI,YAAY,EAAE;cAC5D,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC;cACvD,MAAM,iBAAiB,GAAG,UAAU,KAAK,IAAI,CAAC,GAAG,KAAK,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;cAElG,IAAI,iBAAiB,EAAE;EACrB,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI;EAEtB,gBAAA,QAAQ,CAAC,gBAAgB,CACvB,SAAS,EACT,MAAK;EACH,oBAAA,IAAI,CAAC,UAAU,GAAG,KAAK;EACzB,iBAAC,EACD,EAAE,IAAI,EAAE,IAAI,EAAE,CACf;EAED,gBAAA,QAAQ,CAAC,gBAAgB,CACvB,MAAM,EACN,MAAK;EACH,oBAAA,IAAI,CAAC,UAAU,GAAG,KAAK;EACzB,iBAAC,EACD,EAAE,IAAI,EAAE,IAAI,EAAE,CACf;EAED,gBAAA,QAAQ,CAAC,gBAAgB,CACvB,SAAS,EACT,MAAK;EACH,oBAAA,IAAI,CAAC,UAAU,GAAG,KAAK;EACzB,iBAAC,EACD,EAAE,IAAI,EAAE,IAAI,EAAE,CACf;;;;EAKL,QAAA,IACE;iBACG;iBACA;iBACA;iBACA;EACA,gBAAC,YAAY,IAAI,YAAY,CAAC,EACjC;EACA,YAAA,OAAO,KAAK;;EAGd,QAAA,OAAO,IAAI;;EAGb;;;;EAIG;EACH,IAAA,cAAc,CAAC,QAA4B,EAAA;UACzC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;EACjC,YAAA,OAAO,IAAI;;UAGb,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,KAAK,UAAU,EAAE;cACrD,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,QAAQ,EAAE,CAAC;;;;EAKlD,QAAA,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;EACxC,YAAA,OAAO,IAAI;;;EAIb,QAAA,IAAI,QAAQ,CAAC,IAAI,KAAK,WAAW,EAAE;EACjC,YAAA,OAAO,KAAK;;;;;;;UAQd,IACE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM;iBAC9B,QAAQ,CAAC,IAAI,KAAK;EAClB,gBAAC,KAAK,EAAE,IAAI,SAAS,EAAE;EACvB,eAAA,IAAI,CAAC,MAAM,CAAC,SAAS,EACxB;EACA,YAAA,MAAM,YAAY,GAAG;EACnB,gBAAA,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;EAClC,gBAAA,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;eACpB;;;EAIlB,YAAA,IAAI,YAAY,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,iBAAiB,CAAC,EAAE;EACtD,gBAAA,OAAO,KAAK;;;;;EAMhB,QAAA,IAAI,IAAI,CAAC,UAAU,KAAK,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,YAAY,EAAE;EACzE,YAAA,OAAO,IAAI;;;UAIb,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;EAC7C,YAAA,OAAO,KAAK;;EAGd,QAAA,OAAO,IAAI;;EAGb;;EAEG;EACH,IAAA,gBAAgB,CAAC,UAA+B,EAAA;EAC9C,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,KAAI;EACtC,YAAA,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE;EAEzB,YAAA,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;EAC3B,gBAAA,OAAO,KAAK;;EAGd,YAAA,EAAE,CAAC,aAAa,CAAC,GAAG,EAAE,SAAS,EAAE;EAC/B,gBAAA,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK;EAClB,gBAAA,GAAG,UAAU;EACd,aAAA,CAAC;EAEF,YAAA,OAAO,IAAI;EACb,SAAC,CAAC;;EAGJ;;EAEG;MACH,UAAU,GAAA;EACR,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE;EAE1B,QAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;cAC5B;;UAEF,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ;EAEpC,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;;EAEjD;;EC7SD;;;;EAIG;EACG,SAAU,aAAa,CAAC,MAQ7B,EAAA;MACC,OAAO,IAAI,SAAS,CAAC;UACnB,IAAI,EAAE,MAAM,CAAC,IAAI;EACjB,QAAA,OAAO,EAAE,CAAC,EACR,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,GAChC,KAAI;EACH,YAAA,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,aAAa,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,CAAC;cAEnF,IAAI,UAAU,KAAK,KAAK,IAAI,UAAU,KAAK,IAAI,EAAE;EAC/C,gBAAA,OAAO,IAAI;;EAGb,YAAA,MAAM,EAAE,EAAE,EAAE,GAAG,KAAK;cACpB,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;EAC5C,YAAA,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC;EAC1B,YAAA,IAAI,OAAO,GAAG,KAAK,CAAC,EAAE;cAEtB,IAAI,YAAY,EAAE;kBAChB,MAAM,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;EAC1C,gBAAA,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC,OAAO,CAAC,YAAY,CAAC;EAC9D,gBAAA,MAAM,OAAO,GAAG,SAAS,GAAG,YAAY,CAAC,MAAM;EAE/C,gBAAA,MAAM,aAAa,GAAG,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,GAAG;uBAClE,MAAM,CAAC,IAAI,IAAG;;sBAEb,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAsB;sBAEtD,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;EAC/E,iBAAC;uBACA,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC;EAEtC,gBAAA,IAAI,aAAa,CAAC,MAAM,EAAE;EACxB,oBAAA,OAAO,IAAI;;EAGb,gBAAA,IAAI,OAAO,GAAG,KAAK,CAAC,EAAE,EAAE;sBACtB,EAAE,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC;;EAG9B,gBAAA,IAAI,SAAS,GAAG,KAAK,CAAC,IAAI,EAAE;sBAC1B,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,WAAW,EAAE,SAAS,CAAC;;kBAGhD,OAAO,GAAG,KAAK,CAAC,IAAI,GAAG,WAAW,GAAG,YAAY,CAAC,MAAM;kBAExD,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,WAAW,EAAE,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC;EAEnF,gBAAA,EAAE,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC;;WAEnC;EACF,KAAA,CAAC;EACJ;;ECvEA;EACM,SAAU,cAAc,CAAC,MAAc,EAAA;MAC3C,OAAO,MAAM,CAAC,OAAO,CAAC,uBAAuB,EAAE,MAAM,CAAC;EACxD;;ECHM,SAAU,QAAQ,CAAC,KAAU,EAAA;EACjC,IAAA,OAAO,OAAO,KAAK,KAAK,QAAQ;EAClC;;ECIA;;;;EAIG;EACG,SAAU,aAAa,CAAC,MAa7B,EAAA;MACC,OAAO,IAAI,SAAS,CAAC;UACnB,IAAI,EAAE,MAAM,CAAC,IAAI;UACjB,OAAO,CAAC,EACN,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,GAChC,EAAA;EACC,YAAA,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,aAAa,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,CAAC;EACnF,YAAA,MAAM,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC,UAAU,EAAE,SAAS,EAAE,UAAU,CAAC;cAEtE,IAAI,UAAU,KAAK,KAAK,IAAI,UAAU,KAAK,IAAI,EAAE;EAC/C,gBAAA,OAAO,IAAI;;EAGb,YAAA,MAAM,IAAI,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAiB;cAEzE,IAAI,OAAO,EAAE;EACX,gBAAA,IAAI,CAAC,OAAO,GAAG,OAAO;;EAGxB,YAAA,IAAI,KAAK,CAAC,KAAK,EAAE;EACf,gBAAA,KAAK,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;;WAE/D;EACF,KAAA,CAAC;EACJ;;EC9CA;;;;EAIG;EACG,SAAU,aAAa,CAAC,MAG7B,EAAA;MACC,OAAO,IAAI,SAAS,CAAC;UACnB,IAAI,EAAE,MAAM,CAAC,IAAI;UACjB,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAI;EACnC,YAAA,IAAI,MAAM,GAAG,MAAM,CAAC,OAAO;EAC3B,YAAA,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI;EACtB,YAAA,MAAM,GAAG,GAAG,KAAK,CAAC,EAAE;EAEpB,YAAA,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;EACZ,gBAAA,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EAE7C,gBAAA,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;kBAClD,KAAK,IAAI,MAAM;EAEf,gBAAA,MAAM,MAAM,GAAG,KAAK,GAAG,GAAG;EAE1B,gBAAA,IAAI,MAAM,GAAG,CAAC,EAAE;EACd,oBAAA,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,EAAE,MAAM,CAAC,GAAG,MAAM;sBACzD,KAAK,GAAG,GAAG;;;cAIf,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC;WACxC;EACF,KAAA,CAAC;EACJ;;QC5Ba,OAAO,CAAA;EAKlB,IAAA,WAAA,CAAY,WAAwB,EAAA;EAClC,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW;UAC9B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM;;EAGlD,IAAA,GAAG,CAAC,QAAgB,EAAA;UAClB,IAAI,OAAO,GAAG,KAAK;EAEnB,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC;EACrC,aAAA,KAAK,CAAC,IAAI,CAAC,WAAW;EACtB,aAAA,MAAM,CAAC,CAAC,WAAW,EAAE,IAAI,KAAI;cAC5B,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC;EAEtD,YAAA,IAAI,SAAS,CAAC,OAAO,EAAE;kBACrB,OAAO,GAAG,IAAI;;cAGhB,OAAO,SAAS,CAAC,GAAG;WACrB,EAAE,QAAQ,CAAC;UAEd,OAAO;EACL,YAAA,QAAQ,EAAE,cAAc;cACxB,OAAO;WACR;;EAEJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}