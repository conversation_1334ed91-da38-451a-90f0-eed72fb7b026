import { Router } from "express";
import {
  getBusinessIdea,
  saveBusinessIdea,
  addCollaborator,
  removeCollaborator,
  saveNote,
  deleteNote,
  getLearningMaterials,
  updateLearningMaterial,
  getGreeting,
  updateGreeting,
  getCompanyTeamMembers,
} from "../../controllers/core/businessIdea.controller";
import { authenticateToken } from "../../middleware/authenticateToken";

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Business idea routes
router.get("/company/:companyId", getBusinessIdea);
router.post("/company/:companyId", saveBusinessIdea);

// Collaborator routes
router.post("/company/:companyId/collaborators", addCollaborator);
router.delete(
  "/company/:companyId/collaborators/:collaboratorId",
  removeCollaborator
);
router.get("/company/:companyId/team-members", getCompanyTeamMembers);

// Notes routes
router.post("/company/:companyId/notes", saveNote);
router.delete("/company/:companyId/notes/:noteId", deleteNote);

// Learning materials routes
router.get("/learning-materials", getLearningMaterials);
router.put("/learning-materials/:materialId", updateLearningMaterial);

// Greeting routes
router.get("/greeting", getGreeting);
router.put("/greeting", updateGreeting);

export default router;
