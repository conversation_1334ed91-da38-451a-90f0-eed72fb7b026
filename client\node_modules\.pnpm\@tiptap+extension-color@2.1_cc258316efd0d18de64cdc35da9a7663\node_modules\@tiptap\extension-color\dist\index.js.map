{"version": 3, "file": "index.js", "sources": ["../src/color.ts"], "sourcesContent": ["import '@tiptap/extension-text-style'\n\nimport { Extension } from '@tiptap/core'\n\nexport type ColorOptions = {\n  /**\n   * The types where the color can be applied\n   * @default ['textStyle']\n   * @example ['heading', 'paragraph']\n  */\n  types: string[],\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    color: {\n      /**\n       * Set the text color\n       * @param color The color to set\n       * @example editor.commands.setColor('red')\n       */\n      setColor: (color: string) => ReturnType,\n\n      /**\n       * Unset the text color\n       * @example editor.commands.unsetColor()\n       */\n      unsetColor: () => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to color your text.\n * @see https://tiptap.dev/api/extensions/color\n */\nexport const Color = Extension.create<ColorOptions>({\n  name: 'color',\n\n  addOptions() {\n    return {\n      types: ['textStyle'],\n    }\n  },\n\n  addGlobalAttributes() {\n    return [\n      {\n        types: this.options.types,\n        attributes: {\n          color: {\n            default: null,\n            parseHTML: element => element.style.color?.replace(/['\"]+/g, ''),\n            renderHTML: attributes => {\n              if (!attributes.color) {\n                return {}\n              }\n\n              return {\n                style: `color: ${attributes.color}`,\n              }\n            },\n          },\n        },\n      },\n    ]\n  },\n\n  addCommands() {\n    return {\n      setColor: color => ({ chain }) => {\n        return chain()\n          .setMark('textStyle', { color })\n          .run()\n      },\n      unsetColor: () => ({ chain }) => {\n        return chain()\n          .setMark('textStyle', { color: null })\n          .removeEmptyTextStyle()\n          .run()\n      },\n    }\n  },\n})\n"], "names": [], "mappings": ";;;AAgCA;;;AAGG;AACU,MAAA,KAAK,GAAG,SAAS,CAAC,MAAM,CAAe;AAClD,IAAA,IAAI,EAAE,OAAO;IAEb,UAAU,GAAA;QACR,OAAO;YACL,KAAK,EAAE,CAAC,WAAW,CAAC;SACrB;KACF;IAED,mBAAmB,GAAA;QACjB,OAAO;AACL,YAAA;AACE,gBAAA,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;AACzB,gBAAA,UAAU,EAAE;AACV,oBAAA,KAAK,EAAE;AACL,wBAAA,OAAO,EAAE,IAAI;wBACb,SAAS,EAAE,OAAO,cAAI,OAAA,CAAA,EAAA,GAAA,OAAO,CAAC,KAAK,CAAC,KAAK,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAA,EAAA;wBAChE,UAAU,EAAE,UAAU,IAAG;AACvB,4BAAA,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;AACrB,gCAAA,OAAO,EAAE;;4BAGX,OAAO;AACL,gCAAA,KAAK,EAAE,CAAA,OAAA,EAAU,UAAU,CAAC,KAAK,CAAE,CAAA;6BACpC;yBACF;AACF,qBAAA;AACF,iBAAA;AACF,aAAA;SACF;KACF;IAED,WAAW,GAAA;QACT,OAAO;YACL,QAAQ,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,KAAI;AAC/B,gBAAA,OAAO,KAAK;AACT,qBAAA,OAAO,CAAC,WAAW,EAAE,EAAE,KAAK,EAAE;AAC9B,qBAAA,GAAG,EAAE;aACT;YACD,UAAU,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,KAAI;AAC9B,gBAAA,OAAO,KAAK;qBACT,OAAO,CAAC,WAAW,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;AACpC,qBAAA,oBAAoB;AACpB,qBAAA,GAAG,EAAE;aACT;SACF;KACF;AACF,CAAA;;;;"}