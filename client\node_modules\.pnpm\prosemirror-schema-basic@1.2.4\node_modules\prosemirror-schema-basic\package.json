{"name": "prosemirror-schema-basic", "version": "1.2.4", "description": "Basic schema elements for ProseMirror", "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "sideEffects": false, "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://marijnhaverbeke.nl"}], "repository": {"type": "git", "url": "git://github.com/prosemirror/prosemirror-schema-basic.git"}, "dependencies": {"prosemirror-model": "^1.25.0"}, "devDependencies": {"@prosemirror/buildhelper": "^0.1.5", "prosemirror-test-builder": "^1.0.0"}, "scripts": {"test": "pm-runtests", "prepare": "pm-buildhelper src/schema-basic.ts"}}