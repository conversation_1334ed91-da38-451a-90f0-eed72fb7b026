{"version": 3, "file": "wrappingInputRule.d.ts", "sourceRoot": "", "sources": ["../../src/inputRules/wrappingInputRule.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,IAAI,eAAe,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAA;AAGpE,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAA;AACrC,OAAO,EAAE,SAAS,EAAE,eAAe,EAAE,MAAM,iBAAiB,CAAA;AAC5D,OAAO,EAAE,wBAAwB,EAAE,MAAM,aAAa,CAAA;AAGtD;;;;;;;;;;;;;;GAcG;AACH,wBAAgB,iBAAiB,CAAC,MAAM,EAAE;IACxC,IAAI,EAAE,eAAe,CAAC;IACtB,IAAI,EAAE,QAAQ,CAAC;IACf,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB,MAAM,CAAC,EAAE,MAAM,CAAA;IACf,aAAa,CAAC,EACZ,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GACnB,CAAC,CAAC,KAAK,EAAE,wBAAwB,KAAK,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAC1D,KAAK,GACL,IAAI,CACL;IACD,aAAa,CAAC,EAAE,CAAC,KAAK,EAAE,wBAAwB,EAAE,IAAI,EAAE,eAAe,KAAK,OAAO,CAAC;CACrF,aAgDA"}