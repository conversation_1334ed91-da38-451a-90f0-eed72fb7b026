<template>
  <div class="flex min-h-screen w-full items-center justify-center">
    <div class="w-full max-w-md text-center">
      <div v-if="isLoading" class="flex flex-col items-center">
        <BaseLoading size="lg" />
        <BaseParagraph class="mt-4">
          {{ t("auth.confirm_email.verifying") }}
        </BaseParagraph>
      </div>

      <div v-else-if="success" class="flex flex-col items-center">
        <div class="mb-6 flex justify-center">
          <div
            class="flex items-center justify-center rounded-full bg-success-100 dark:bg-success-900"
          >
            <Icon
              name="ph:check-circle-duotone"
              class="text-success-500 size-10"
            />
          </div>
        </div>
        <BaseHeading as="h1" size="2xl" weight="medium" class="mb-2">
          {{ t("auth.email_verified.title") }}
        </BaseHeading>
        <BaseParagraph
          size="sm"
          class="text-muted-400 dark:text-muted-300 mb-8"
        >
          <span v-if="alreadyConfirmed">{{
            t("auth.confirm_email.already_confirmed")
          }}</span>
          <span v-else>{{
            t("auth.email_verified.description", { email })
          }}</span>
        </BaseParagraph>
        <div class="flex flex-col gap-4">
          <BaseButton to="/auth" variant="primary" rounded="lg" class="w-full">
            {{ t("auth.email_verified.login_button") }}
          </BaseButton>
        </div>
      </div>

      <div v-else class="flex flex-col items-center">
        <div class="mb-6 flex justify-center">
          <div
            class="flex h-16 w-16 items-center justify-center rounded-full bg-danger-100 dark:bg-danger-900"
          >
            <Icon name="ph:x-circle-duotone" class="text-danger-500 size-10" />
          </div>
        </div>
        <BaseHeading as="h1" size="2xl" weight="medium" class="mb-2">
          {{ t("auth.confirm_email.error.title") }}
        </BaseHeading>
        <BaseParagraph
          size="sm"
          class="text-muted-400 dark:text-muted-300 mb-8"
        >
          {{ errorMessage || t("auth.confirm_email.error.general") }}
        </BaseParagraph>
        <div class="flex flex-col gap-4">
          <BaseButton to="/" variant="primary" rounded="lg" class="w-full">
            {{ t("auth.confirm_email.back_to_login") }}
          </BaseButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { useRoute, useRouter } from "vue-router";

const { t } = useI18n();
const route = useRoute();
const router = useRouter();

const isLoading = ref(false);
const success = ref(false);
const errorMessage = ref("");
const email = ref("");
const alreadyConfirmed = ref(false);

// Process email confirmation status from query parameters
onMounted(() => {
  // Get parameters from URL
  const successParam = route.query.success as string;
  const errorParam = route.query.error as string;
  const emailParam = route.query.email as string;
  const alreadyConfirmedParam = route.query.alreadyConfirmed as string;

  // Set component state based on URL parameters
  success.value = successParam === "true";
  errorMessage.value = errorParam ? decodeURIComponent(errorParam) : "";
  email.value = emailParam ? decodeURIComponent(emailParam) : "";
  alreadyConfirmed.value = alreadyConfirmedParam === "true";

  // If no parameters, redirect to login
  if (!successParam && !errorParam) {
    router.push("/");
  }
});

definePageMeta({
  layout: false,
  title: "Confirm Email",
});
</script>
