<template>
  <div class="tiptap-editor border border-muted-200 dark:border-muted-800 rounded-md">
    <!-- Toolbar -->
    <div class="flex flex-wrap items-center gap-1 p-2 border-b border-muted-200 dark:border-muted-800 bg-muted-50 dark:bg-muted-900/50">
      <!-- Text Formatting -->
      <button
        @click="editor?.chain().focus().toggleBold().run()"
        :class="{ 'bg-primary-500 text-white': editor?.isActive('bold') }"
        class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors"
        type="button"
      >
        <Icon name="ph:text-b-bold" class="h-4 w-4" />
      </button>

      <button
        @click="editor?.chain().focus().toggleItalic().run()"
        :class="{ 'bg-primary-500 text-white': editor?.isActive('italic') }"
        class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors"
        type="button"
      >
        <Icon name="ph:text-italic" class="h-4 w-4" />
      </button>

      <button
        @click="editor?.chain().focus().toggleUnderline().run()"
        :class="{ 'bg-primary-500 text-white': editor?.isActive('underline') }"
        class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors"
        type="button"
      >
        <Icon name="ph:text-underline" class="h-4 w-4" />
      </button>

      <div class="w-px h-6 bg-muted-200 dark:bg-muted-700 mx-1"></div>

      <!-- Headings -->
      <button
        @click="editor?.chain().focus().toggleHeading({ level: 1 }).run()"
        :class="{ 'bg-primary-500 text-white': editor?.isActive('heading', { level: 1 }) }"
        class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors text-xs font-bold"
        type="button"
      >
        H1
      </button>

      <button
        @click="editor?.chain().focus().toggleHeading({ level: 2 }).run()"
        :class="{ 'bg-primary-500 text-white': editor?.isActive('heading', { level: 2 }) }"
        class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors text-xs font-bold"
        type="button"
      >
        H2
      </button>

      <button
        @click="editor?.chain().focus().toggleHeading({ level: 3 }).run()"
        :class="{ 'bg-primary-500 text-white': editor?.isActive('heading', { level: 3 }) }"
        class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors text-xs font-bold"
        type="button"
      >
        H3
      </button>

      <div class="w-px h-6 bg-muted-200 dark:bg-muted-700 mx-1"></div>

      <!-- Lists -->
      <button
        @click="editor?.chain().focus().toggleBulletList().run()"
        :class="{ 'bg-primary-500 text-white': editor?.isActive('bulletList') }"
        class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors"
        type="button"
      >
        <Icon name="ph:list-bullets" class="h-4 w-4" />
      </button>

      <button
        @click="editor?.chain().focus().toggleOrderedList().run()"
        :class="{ 'bg-primary-500 text-white': editor?.isActive('orderedList') }"
        class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors"
        type="button"
      >
        <Icon name="ph:list-numbers" class="h-4 w-4" />
      </button>

      <div class="w-px h-6 bg-muted-200 dark:bg-muted-700 mx-1"></div>

      <!-- Text Colors -->
      <div class="flex items-center gap-1">
        <label class="text-xs text-muted-600 dark:text-muted-400">Color:</label>
        <input
          type="color"
          @input="editor?.chain().focus().setColor($event.target.value).run()"
          class="w-6 h-6 rounded border border-muted-200 dark:border-muted-700 cursor-pointer"
        />
      </div>

      <div class="w-px h-6 bg-muted-200 dark:bg-muted-700 mx-1"></div>

      <!-- Raw HTML Toggle -->
      <button
        @click="toggleRawMode"
        :class="{ 'bg-primary-500 text-white': isRawMode }"
        class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors text-xs"
        type="button"
        title="Toggle Raw HTML Mode"
      >
        HTML
      </button>
    </div>

    <!-- Editor Content -->
    <div v-if="!isRawMode" class="prose prose-sm max-w-none p-4 min-h-[200px] focus-within:outline-none">
      <EditorContent :editor="editor" />
    </div>

    <!-- Raw HTML Editor -->
    <div v-else class="p-4">
      <textarea
        v-model="rawContent"
        @input="updateFromRaw"
        class="w-full min-h-[200px] p-3 border border-muted-200 dark:border-muted-700 rounded-md bg-white dark:bg-muted-900 text-muted-800 dark:text-muted-200 font-mono text-sm"
        placeholder="Enter raw HTML content..."
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onBeforeUnmount } from 'vue'
import { useEditor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import TextStyle from '@tiptap/extension-text-style'
import Color from '@tiptap/extension-color'
import TextAlign from '@tiptap/extension-text-align'
import Underline from '@tiptap/extension-underline'
import { Node } from '@tiptap/core'

interface Props {
  modelValue: string
  placeholder?: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Start typing...'
})

const emit = defineEmits<Emits>()

// Raw mode state
const isRawMode = ref(false)
const rawContent = ref(props.modelValue)

// Custom Div extension to support div elements with style attributes
const CustomDiv = Node.create({
  name: 'customDiv',
  group: 'block',
  content: 'block*',
  parseHTML() {
    return [
      {
        tag: 'div',
        getAttrs: (node) => {
          if (typeof node === 'string') return false
          return {}
        },
      },
    ]
  },
  renderHTML({ HTMLAttributes }) {
    return ['div', HTMLAttributes, 0]
  },
  addAttributes() {
    return {
      style: {
        default: null,
        parseHTML: element => element.getAttribute('style'),
        renderHTML: attributes => {
          if (!attributes.style) return {}
          return { style: attributes.style }
        },
      },
      class: {
        default: null,
        parseHTML: element => element.getAttribute('class'),
        renderHTML: attributes => {
          if (!attributes.class) return {}
          return { class: attributes.class }
        },
      },
    }
  },
})

const editor = useEditor({
  content: props.modelValue,
  extensions: [
    StarterKit,
    TextStyle,
    Color,
    TextAlign.configure({
      types: ['heading', 'paragraph'],
    }),
    Underline,
    CustomDiv,
  ],
  onUpdate: ({ editor }) => {
    const html = editor.getHTML()
    rawContent.value = html
    emit('update:modelValue', html)
  },
  editorProps: {
    attributes: {
      class: 'prose prose-sm max-w-none focus:outline-none',
    },
  },
  parseOptions: {
    preserveWhitespace: 'full',
  },
})

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  if (editor.value && editor.value.getHTML() !== newValue) {
    editor.value.commands.setContent(newValue, false)
    rawContent.value = newValue
  }
})

// Toggle between visual and raw HTML mode
const toggleRawMode = () => {
  if (isRawMode.value) {
    // Switching from raw to visual
    if (editor.value) {
      editor.value.commands.setContent(rawContent.value, false)
    }
  } else {
    // Switching from visual to raw
    if (editor.value) {
      rawContent.value = editor.value.getHTML()
    }
  }
  isRawMode.value = !isRawMode.value
}

// Update editor content from raw HTML
const updateFromRaw = () => {
  emit('update:modelValue', rawContent.value)
}

onBeforeUnmount(() => {
  editor.value?.destroy()
})
</script>

<style>
.tiptap-editor .ProseMirror {
  outline: none;
}

.tiptap-editor .ProseMirror p.is-editor-empty:first-child::before {
  color: #adb5bd;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

/* Ensure div elements are properly displayed */
.tiptap-editor .ProseMirror div {
  display: block;
}
</style>
