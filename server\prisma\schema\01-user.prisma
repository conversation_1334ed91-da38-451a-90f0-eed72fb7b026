model User {
  id                      Int           @id @default(autoincrement())
  email                   String        @unique
  password                String?
  firstName               String
  lastName                String
  birthdate               DateTime?
  phone                   String?
  userRoles               UserRole[]
  address                 String?
  address2                String?
  street                  String?
  city                    String?
  postalCode              String?
  state                   String?
  country                 String?
  notes                   String?
  avatar                  String?       // Profile picture URL
  coverImage              String?       // Cover photo URL
  createdAt               DateTime      @default(now())
  updatedAt               DateTime      @updatedAt
  passwordChangedAt       DateTime?     // Track when password was last changed
  emailConfirmationToken  String?
  emailConfirmed          <PERSON><PERSON><PERSON>       @default(false)
  phoneConfirmed          <PERSON>olean       @default(false)
  sessionLogs             SessionLog[]
  referredByUserId        Int?
  referredBy              User?         @relation("UserReferrals", fields: [referredByUserId], references: [id])
  referrals               User[]        @relation("UserReferrals")
  googleId                String?       @unique
  barcode                 String        @unique
  qrCode                  String        @unique
  stripeCustomerId String?
  companies               UserCompany[]
  // New subscription relations
  legacySubscriptions     LegacySubscription[] @relation("LegacySubscriptions")
  subscriptions           Subscription[]      @relation("Subscriptions")

  // Company join requests
  companyJoinRequests     CompanyJoinRequest[]

  // Added opposite relation fields
  incomes                 Income[]      @relation("UserIncomes")    // For Income.user
  expenses                Expense[]     @relation("UserExpenses")   // For Expense.user
  assets                  Asset[]       @relation("UserAssets")     // For Asset.user

  CommunicationEndpoint CommunicationEndpoint[]

  ChatMessage ChatMessage[]

  ChatParticipant ChatParticipant[]

  // Production module relations
  taskAssignments TaskAssignment[]
  createdChecklists QualityChecklist[] @relation("ChecklistCreator")
  reviewedChecklists QualityChecklist[] @relation("ChecklistReviewer")
  photoEvidences PhotoEvidence[]
  projectDocuments ProjectDocument[]
  equipmentMaintenances EquipmentMaintenance[]
  materialTransactions MaterialTransaction[]
  resourceAllocations ResourceAllocation[]
  productionReports ProductionReport[]

  // Budget module relations
  budgets Budget[] @relation("UserBudgets")
  transactions Transaction[] @relation("UserTransactions")
  budgetGoals BudgetGoal[] @relation("UserBudgetGoals")

  // Sales module relations
  assignedLeads Lead[]
  assignedDeals Deal[]
  assignedActivities Activity[]
  createdCampaigns Campaign[]
  createdDealNotes DealNote[]
  createdQuotations Quotation[]
  salesGoals SalesGoal[]

  // Time management module relations
  timeEntries TimeEntry[]
  timesheets Timesheet[]
  workAssignments WorkAssignment[]
  taskSuggestions TaskSuggestion[]
  payrollEntries PayrollEntry[]
  clientAccesses ClientAccess[] @relation("ClientAccess")

  EmailAccount EmailAccount[]

  // HR relations
  managedEmployees Employee[] @relation("EmployeeManager")
  createdEmployees Employee[] @relation("EmployeeCreator")
  approvedLeaves LeaveRequest[]
  processedPayrolls Payroll[]
  approvedBonuses Bonus[]
  performanceReviews PerformanceReview[]
  mentorCareerPaths CareerPath[]
  approvedBonusesBasic BonusBasic[]
  mentorshipsBasic CareerPathBasic[]
  uploadedHrDocuments HrDocument[]

  Employee Employee[]

  // Company transition relation
  companyTransitions CompanyTransition[]

  // Supporter payments relation
  supporterPayments Payment[] @relation("SupporterPayments")

  // Recruitment relations
  createdJobs JobPosting[]
  jobApplications JobApplication[]
  reviewedApplications JobApplication[] @relation("ApplicationReviewer")
  interviews Interview[]
  createdAssessments Assessment[]
  assessmentResults AssessmentResult[]

  // Calendar relations
  createdEvents CalendarEvent[]
  eventParticipations EventParticipant[]
  calendarSettings CalendarSettings?
  eventReminders EventReminder[]

  // Map and location tracking relations
  workerLocations     WorkerLocation[] @relation("WorkerLocations")
  assetAssignments    TrackableAssetAssignment[] @relation("AssetAssignments")
  assetUnassignments  TrackableAssetAssignment[] @relation("AssetUnassignments")

  // Payment relations
  paymentCards        PaymentCard[]
  paymentTransactions PaymentTransaction[]

  // System Health relations
  acknowledgedAlerts  SystemAlert[] @relation("AlertAcknowledgedBy")
  resolvedAlerts      SystemAlert[] @relation("AlertResolvedBy")

  // Activity Log relations
  activityLogs        ActivityLog[]

  // Business Idea relations
  createdBusinessIdeas BusinessIdea[] @relation("BusinessIdeaCreator")
  updatedBusinessIdeas BusinessIdea[] @relation("BusinessIdeaUpdater")
  businessIdeaCollaborations BusinessIdeaCollaborator[] @relation("BusinessIdeaCollaborators")
  addedCollaborators BusinessIdeaCollaborator[] @relation("BusinessIdeaCollaboratorAdder")
  businessIdeaNotes BusinessIdeaNote[] @relation("BusinessIdeaNotes")
  createdLearningMaterials LearningMaterial[] @relation("LearningMaterialCreator")
  updatedLearningMaterials LearningMaterial[] @relation("LearningMaterialUpdater")
  createdGreetings BusinessIdeaGreeting[] @relation("GreetingCreator")
  updatedGreetings BusinessIdeaGreeting[] @relation("GreetingUpdater")
}

model SessionLog {
  id        Int       @id @default(autoincrement())
  userId    Int
  user      User      @relation(fields: [userId], references: [id])
  loginAt   DateTime  @default(now())
  logoutAt  DateTime?
}

model UserRole {
  id        Int      @id @default(autoincrement())
  userId    Int
  user      User     @relation(fields: [userId], references: [id])
  role      Role
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Map layer access
  mapLayers MapLayer[] @relation("MapLayerRoles")

  @@unique([userId, role])
}

model UserCompany {
  id        Int      @id @default(autoincrement())
  user      User     @relation(fields: [userId], references: [id])
  userId    Int
  company   Company  @relation(fields: [companyId], references: [id])
  companyId Int
  role      String
  status    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, companyId])
}
