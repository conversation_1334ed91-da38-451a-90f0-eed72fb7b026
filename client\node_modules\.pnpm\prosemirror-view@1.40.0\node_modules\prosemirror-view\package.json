{"name": "prosemirror-view", "version": "1.40.0", "description": "ProseMirror's view component", "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "./style/prosemirror.css": "./style/prosemirror.css"}, "sideEffects": ["./style/prosemirror.css"], "style": "style/prosemirror.css", "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://marijnhaverbeke.nl"}], "repository": {"type": "git", "url": "git://github.com/prosemirror/prosemirror-view.git"}, "dependencies": {"prosemirror-model": "^1.20.0", "prosemirror-state": "^1.0.0", "prosemirror-transform": "^1.1.0"}, "devDependencies": {"@prosemirror/buildhelper": "^0.1.5", "prosemirror-test-builder": "^1.0.0"}, "scripts": {"test": "pm-runtests", "prepare": "pm-buildhelper src/index.ts"}}