@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\npm-run-all@4.1.5\node_modules\npm-run-all\bin\run-s\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\npm-run-all@4.1.5\node_modules\npm-run-all\bin\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\npm-run-all@4.1.5\node_modules\npm-run-all\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\npm-run-all@4.1.5\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\npm-run-all@4.1.5\node_modules\npm-run-all\bin\run-s\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\npm-run-all@4.1.5\node_modules\npm-run-all\bin\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\npm-run-all@4.1.5\node_modules\npm-run-all\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\npm-run-all@4.1.5\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\npm-run-all\bin\run-s\index.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\npm-run-all\bin\run-s\index.js" %*
)
