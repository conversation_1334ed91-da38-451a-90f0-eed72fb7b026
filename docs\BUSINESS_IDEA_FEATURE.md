# Business Idea Feature

## Overview

The Business Idea feature is a comprehensive module designed to help entrepreneurs and companies develop, refine, and collaborate on their business concepts. It provides educational materials, collaborative tools, and a structured approach to business idea development.

## Features

### 🎯 Core Functionality

- **Business Idea Editor**: Rich text editor for documenting business concepts
- **Learning Materials**: Educational content with videos to guide idea development
- **Collaboration**: Team member collaboration with role-based access
- **Post-it Notes**: Quick note-taking with auto-save functionality
- **Document Upload**: Upload and parse business documents
- **Admin Controls**: Content management for ADMIN/SUPERADMIN users

### 📚 Learning Materials

Pre-seeded educational content covering:
- What is a Business Idea?
- Market Research Fundamentals
- Identifying Your Target Audience
- Competitive Analysis
- Creating Your Value Proposition
- Business Model Basics

### 👥 Collaboration Features

- Add team members as collaborators
- Real-time note sharing
- Role-based content editing
- Activity logging for all changes

## Technical Implementation

### Database Schema

#### Core Models
- `BusinessIdea`: Main business idea content per company
- `BusinessIdeaCollaborator`: Team member collaboration
- `BusinessIdeaNote`: Post-it style notes with auto-save
- `LearningMaterial`: Educational content (admin-editable)
- `BusinessIdeaGreeting`: Welcome message (admin-editable)

#### Key Features
- Company-scoped business ideas (one per company)
- User-specific notes with auto-save
- Rich text content with limited formatting
- YouTube video integration for learning materials

### API Endpoints

```
GET    /api/v1/core/business-idea/company/:companyId
POST   /api/v1/core/business-idea/company/:companyId
POST   /api/v1/core/business-idea/company/:companyId/collaborators
DELETE /api/v1/core/business-idea/company/:companyId/collaborators/:id
GET    /api/v1/core/business-idea/company/:companyId/team-members
POST   /api/v1/core/business-idea/company/:companyId/notes
DELETE /api/v1/core/business-idea/company/:companyId/notes/:id
GET    /api/v1/core/business-idea/learning-materials
PUT    /api/v1/core/business-idea/learning-materials/:id (ADMIN only)
GET    /api/v1/core/business-idea/greeting
PUT    /api/v1/core/business-idea/greeting (ADMIN only)
```

### Frontend Components

#### Pages
- `client/layers/core/pages/core/business/idea.vue`: Main business idea page

#### Components
- `PostItNotes.vue`: Auto-saving note-taking component
- `CollaboratorSelector.vue`: Team member management
- `RichTextEditor.vue`: Limited rich text editing
- `EditLearningMaterialModal.vue`: Admin content editing
- `EditGreetingModal.vue`: Admin greeting editing

### Role-Based Access Control

#### All Users
- View and edit their company's business idea
- Add and manage personal notes
- View learning materials
- Add/remove collaborators from company team

#### ADMIN/SUPERADMIN Only
- Edit learning materials content
- Edit welcome greeting
- Manage YouTube video links in materials

## Setup Instructions

### 1. Database Setup

```bash
# Generate Prisma client
pnpm prisma generate

# Push schema changes to database
pnpm prisma db push
```

### 2. Seed Initial Data

```bash
# Run the setup script
cd server
npx ts-node scripts/setupBusinessIdea.ts
```

Or manually:

```bash
# Seed learning materials and greeting
npx ts-node scripts/seedBusinessIdeaData.ts
```

### 3. Navigation

The page is automatically added to the navigation under:
**Business Documentation > Business Idea**

## Usage Guide

### For Regular Users

1. **Navigate** to `/core/business/idea`
2. **Read** the welcome message and learning materials
3. **Write** your business idea in the main editor
4. **Add notes** using the post-it style notes on the right
5. **Invite collaborators** from your team
6. **Upload documents** to supplement your idea

### For Administrators

1. **Edit Learning Materials**: Click edit buttons next to each accordion item
2. **Modify Welcome Message**: Click edit button in the greeting section
3. **Add YouTube Videos**: Include up to 3 videos per learning material
4. **Rich Text Editing**: Use the toolbar for formatting (H1-H3, bold, italic, colors, lists)

## File Structure

```
client/layers/core/
├── pages/core/business/idea.vue
├── components/business/
│   ├── PostItNotes.vue
│   ├── CollaboratorSelector.vue
│   ├── RichTextEditor.vue
│   ├── EditLearningMaterialModal.vue
│   └── EditGreetingModal.vue

server/
├── controllers/core/businessIdea.controller.ts
├── routes/core/businessIdea.routes.ts
├── prisma/schema/business-idea.prisma
└── scripts/
    ├── seedBusinessIdeaData.ts
    └── setupBusinessIdea.ts
```

## Internationalization

Translations are available in English and Estonian:
- `client/.app/i18n/locales/en-US.yaml`
- `client/.app/i18n/locales/et-EE.yaml`

Key translation sections:
- `business_idea.*`: All business idea related texts
- `navigation.business_idea`: Navigation label

## Security Features

- **Authentication**: All endpoints require valid user authentication
- **Authorization**: Company-scoped access control
- **Role-based editing**: Admin-only content management
- **Input validation**: Rich text content sanitization
- **Activity logging**: All changes are logged for audit trail

## Future Enhancements

- [ ] Business plan template generation
- [ ] AI-powered idea analysis
- [ ] Export to PDF functionality
- [ ] Version history for business ideas
- [ ] Integration with document library
- [ ] Business idea sharing between companies
- [ ] Advanced collaboration features (comments, suggestions)

## Troubleshooting

### Common Issues

1. **Database errors**: Ensure Prisma schema is up to date
2. **Missing learning materials**: Run the seed script
3. **Permission errors**: Check user roles and company associations
4. **Component not loading**: Verify all dependencies are installed

### Debug Mode

Enable debug logging by setting environment variable:
```bash
DEBUG=business-idea:*
```
