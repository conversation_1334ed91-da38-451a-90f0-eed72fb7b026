hoistPattern:
  - '*'
hoistedDependencies:
  .app:
    app: private
  .demo:
    demo: private
  '@antfu/install-pkg@1.1.0':
    '@antfu/install-pkg': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/parser@7.27.4':
    '@babel/parser': private
  '@babel/types@7.27.3':
    '@babel/types': private
  '@clack/core@0.4.2':
    '@clack/core': private
  '@clack/prompts@0.10.1':
    '@clack/prompts': private
  '@dprint/formatter@0.3.0':
    '@dprint/formatter': private
  '@dprint/markdown@0.17.8':
    '@dprint/markdown': private
  '@dprint/toml@0.6.4':
    '@dprint/toml': private
  '@es-joy/jsdoccomment@0.50.2':
    '@es-joy/jsdoccomment': private
  '@eslint-community/eslint-plugin-eslint-comments@4.5.0(eslint@9.24.0(jiti@2.4.2))':
    '@eslint-community/eslint-plugin-eslint-comments': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.24.0(jiti@2.4.2))':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/compat@1.2.9(eslint@9.24.0(jiti@2.4.2))':
    '@eslint/compat': private
  '@eslint/config-array@0.20.0':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.2.2':
    '@eslint/config-helpers': private
  '@eslint/core@0.12.0':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/js@9.24.0':
    '@eslint/js': private
  '@eslint/markdown@6.4.0':
    '@eslint/markdown': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.2.8':
    '@eslint/plugin-kit': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@pkgr/core@0.1.2':
    '@pkgr/core': private
  '@popperjs/core@2.11.8':
    '@popperjs/core': private
  '@remirror/core-constants@3.0.0':
    '@remirror/core-constants': private
  '@stylistic/eslint-plugin@4.4.0(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)':
    '@stylistic/eslint-plugin': private
  '@tiptap/core@2.14.0(@tiptap/pm@2.14.0)':
    '@tiptap/core': private
  '@tiptap/extension-blockquote@2.14.0(@tiptap/core@2.14.0(@tiptap/pm@2.14.0))':
    '@tiptap/extension-blockquote': private
  '@tiptap/extension-bold@2.14.0(@tiptap/core@2.14.0(@tiptap/pm@2.14.0))':
    '@tiptap/extension-bold': private
  '@tiptap/extension-bubble-menu@2.14.0(@tiptap/core@2.14.0(@tiptap/pm@2.14.0))(@tiptap/pm@2.14.0)':
    '@tiptap/extension-bubble-menu': private
  '@tiptap/extension-bullet-list@2.14.0(@tiptap/core@2.14.0(@tiptap/pm@2.14.0))':
    '@tiptap/extension-bullet-list': private
  '@tiptap/extension-code-block@2.14.0(@tiptap/core@2.14.0(@tiptap/pm@2.14.0))(@tiptap/pm@2.14.0)':
    '@tiptap/extension-code-block': private
  '@tiptap/extension-code@2.14.0(@tiptap/core@2.14.0(@tiptap/pm@2.14.0))':
    '@tiptap/extension-code': private
  '@tiptap/extension-document@2.14.0(@tiptap/core@2.14.0(@tiptap/pm@2.14.0))':
    '@tiptap/extension-document': private
  '@tiptap/extension-dropcursor@2.14.0(@tiptap/core@2.14.0(@tiptap/pm@2.14.0))(@tiptap/pm@2.14.0)':
    '@tiptap/extension-dropcursor': private
  '@tiptap/extension-floating-menu@2.14.0(@tiptap/core@2.14.0(@tiptap/pm@2.14.0))(@tiptap/pm@2.14.0)':
    '@tiptap/extension-floating-menu': private
  '@tiptap/extension-gapcursor@2.14.0(@tiptap/core@2.14.0(@tiptap/pm@2.14.0))(@tiptap/pm@2.14.0)':
    '@tiptap/extension-gapcursor': private
  '@tiptap/extension-hard-break@2.14.0(@tiptap/core@2.14.0(@tiptap/pm@2.14.0))':
    '@tiptap/extension-hard-break': private
  '@tiptap/extension-heading@2.14.0(@tiptap/core@2.14.0(@tiptap/pm@2.14.0))':
    '@tiptap/extension-heading': private
  '@tiptap/extension-history@2.14.0(@tiptap/core@2.14.0(@tiptap/pm@2.14.0))(@tiptap/pm@2.14.0)':
    '@tiptap/extension-history': private
  '@tiptap/extension-horizontal-rule@2.14.0(@tiptap/core@2.14.0(@tiptap/pm@2.14.0))(@tiptap/pm@2.14.0)':
    '@tiptap/extension-horizontal-rule': private
  '@tiptap/extension-italic@2.14.0(@tiptap/core@2.14.0(@tiptap/pm@2.14.0))':
    '@tiptap/extension-italic': private
  '@tiptap/extension-list-item@2.14.0(@tiptap/core@2.14.0(@tiptap/pm@2.14.0))':
    '@tiptap/extension-list-item': private
  '@tiptap/extension-ordered-list@2.14.0(@tiptap/core@2.14.0(@tiptap/pm@2.14.0))':
    '@tiptap/extension-ordered-list': private
  '@tiptap/extension-paragraph@2.14.0(@tiptap/core@2.14.0(@tiptap/pm@2.14.0))':
    '@tiptap/extension-paragraph': private
  '@tiptap/extension-strike@2.14.0(@tiptap/core@2.14.0(@tiptap/pm@2.14.0))':
    '@tiptap/extension-strike': private
  '@tiptap/extension-text@2.14.0(@tiptap/core@2.14.0(@tiptap/pm@2.14.0))':
    '@tiptap/extension-text': private
  '@tiptap/pm@2.14.0':
    '@tiptap/pm': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/estree@1.0.7':
    '@types/estree': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/linkify-it@5.0.0':
    '@types/linkify-it': private
  '@types/markdown-it@14.1.2':
    '@types/markdown-it': private
  '@types/mdast@4.0.4':
    '@types/mdast': private
  '@types/mdurl@2.0.0':
    '@types/mdurl': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/unist@3.0.3':
    '@types/unist': private
  '@typescript-eslint/eslint-plugin@8.33.0(@typescript-eslint/parser@8.33.0(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.33.0(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/project-service@8.33.0(typescript@5.8.3)':
    '@typescript-eslint/project-service': private
  '@typescript-eslint/scope-manager@8.33.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/tsconfig-utils@8.33.0(typescript@5.8.3)':
    '@typescript-eslint/tsconfig-utils': private
  '@typescript-eslint/type-utils@8.33.0(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.33.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.33.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.33.0(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.33.0':
    '@typescript-eslint/visitor-keys': private
  '@unrs/resolver-binding-darwin-arm64@1.7.8':
    '@unrs/resolver-binding-darwin-arm64': private
  '@unrs/resolver-binding-darwin-x64@1.7.8':
    '@unrs/resolver-binding-darwin-x64': private
  '@unrs/resolver-binding-freebsd-x64@1.7.8':
    '@unrs/resolver-binding-freebsd-x64': private
  '@unrs/resolver-binding-linux-arm-gnueabihf@1.7.8':
    '@unrs/resolver-binding-linux-arm-gnueabihf': private
  '@unrs/resolver-binding-linux-arm-musleabihf@1.7.8':
    '@unrs/resolver-binding-linux-arm-musleabihf': private
  '@unrs/resolver-binding-linux-arm64-gnu@1.7.8':
    '@unrs/resolver-binding-linux-arm64-gnu': private
  '@unrs/resolver-binding-linux-arm64-musl@1.7.8':
    '@unrs/resolver-binding-linux-arm64-musl': private
  '@unrs/resolver-binding-linux-ppc64-gnu@1.7.8':
    '@unrs/resolver-binding-linux-ppc64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-gnu@1.7.8':
    '@unrs/resolver-binding-linux-riscv64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-musl@1.7.8':
    '@unrs/resolver-binding-linux-riscv64-musl': private
  '@unrs/resolver-binding-linux-s390x-gnu@1.7.8':
    '@unrs/resolver-binding-linux-s390x-gnu': private
  '@unrs/resolver-binding-linux-x64-gnu@1.7.8':
    '@unrs/resolver-binding-linux-x64-gnu': private
  '@unrs/resolver-binding-linux-x64-musl@1.7.8':
    '@unrs/resolver-binding-linux-x64-musl': private
  '@unrs/resolver-binding-wasm32-wasi@1.7.8':
    '@unrs/resolver-binding-wasm32-wasi': private
  '@unrs/resolver-binding-win32-arm64-msvc@1.7.8':
    '@unrs/resolver-binding-win32-arm64-msvc': private
  '@unrs/resolver-binding-win32-ia32-msvc@1.7.8':
    '@unrs/resolver-binding-win32-ia32-msvc': private
  '@unrs/resolver-binding-win32-x64-msvc@1.7.8':
    '@unrs/resolver-binding-win32-x64-msvc': private
  '@vitest/eslint-plugin@1.2.1(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)':
    '@vitest/eslint-plugin': private
  '@vue/compiler-core@3.5.16':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.16':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.5.16':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.5.16':
    '@vue/compiler-ssr': private
  '@vue/reactivity@3.5.16':
    '@vue/reactivity': private
  '@vue/runtime-core@3.5.16':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.5.16':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.5.16(vue@3.5.16(typescript@5.8.3))':
    '@vue/server-renderer': private
  '@vue/shared@3.5.16':
    '@vue/shared': private
  acorn-jsx@5.3.2(acorn@8.14.1):
    acorn-jsx: private
  acorn@8.14.1:
    acorn: private
  ajv@6.12.6:
    ajv: private
  ansi-escapes@7.0.0:
    ansi-escapes: private
  ansi-regex@6.1.0:
    ansi-regex: private
  ansi-styles@3.2.1:
    ansi-styles: private
  ansis@4.1.0:
    ansis: private
  are-docs-informative@0.0.2:
    are-docs-informative: private
  argparse@2.0.1:
    argparse: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  async-function@1.0.0:
    async-function: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  balanced-match@1.0.2:
    balanced-match: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.0:
    browserslist: private
  builtin-modules@5.0.0:
    builtin-modules: private
  cac@6.7.14:
    cac: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  caniuse-lite@1.0.30001720:
    caniuse-lite: private
  ccount@2.0.1:
    ccount: private
  chalk@4.1.2:
    chalk: private
  character-entities@2.0.2:
    character-entities: private
  ci-info@4.2.0:
    ci-info: private
  clean-regexp@1.0.0:
    clean-regexp: private
  cli-cursor@5.0.0:
    cli-cursor: private
  cli-truncate@4.0.0:
    cli-truncate: private
  color-convert@1.9.3:
    color-convert: private
  color-name@1.1.3:
    color-name: private
  colorette@2.0.20:
    colorette: private
  commander@13.1.0:
    commander: private
  comment-parser@1.4.1:
    comment-parser: private
  concat-map@0.0.1:
    concat-map: private
  confbox@0.2.2:
    confbox: private
  core-js-compat@3.42.0:
    core-js-compat: private
  crelt@1.0.6:
    crelt: private
  cross-spawn@7.0.6:
    cross-spawn: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  debug@4.4.1:
    debug: private
  decode-named-character-reference@1.1.0:
    decode-named-character-reference: private
  deep-is@0.1.4:
    deep-is: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  dequal@2.0.3:
    dequal: private
  devlop@1.1.0:
    devlop: private
  dunder-proto@1.0.1:
    dunder-proto: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  electron-to-chromium@1.5.161:
    electron-to-chromium: private
  emoji-regex@10.4.0:
    emoji-regex: private
  enhanced-resolve@5.18.1:
    enhanced-resolve: private
  entities@4.5.0:
    entities: private
  environment@1.1.0:
    environment: private
  error-ex@1.3.2:
    error-ex: private
  es-abstract@1.24.0:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-compat-utils@0.6.5(eslint@9.24.0(jiti@2.4.2)):
    eslint-compat-utils: private
  eslint-config-flat-gitignore@2.1.0(eslint@9.24.0(jiti@2.4.2)):
    eslint-config-flat-gitignore: private
  eslint-flat-config-utils@2.1.0:
    eslint-flat-config-utils: private
  eslint-formatting-reporter@0.0.0(eslint@9.24.0(jiti@2.4.2)):
    eslint-formatting-reporter: private
  eslint-import-context@0.1.6(unrs-resolver@1.7.8):
    eslint-import-context: private
  eslint-json-compat-utils@0.2.1(eslint@9.24.0(jiti@2.4.2))(jsonc-eslint-parser@2.4.0):
    eslint-json-compat-utils: private
  eslint-merge-processors@2.0.0(eslint@9.24.0(jiti@2.4.2)):
    eslint-merge-processors: private
  eslint-parser-plain@0.1.1:
    eslint-parser-plain: private
  eslint-plugin-antfu@3.1.1(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-antfu: private
  eslint-plugin-command@3.2.1(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-command: private
  eslint-plugin-es-x@7.8.0(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-es-x: private
  eslint-plugin-import-x@4.15.0(@typescript-eslint/utils@8.33.0(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-import-x: private
  eslint-plugin-jsdoc@50.7.1(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-jsdoc: private
  eslint-plugin-jsonc@2.20.1(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-jsonc: private
  eslint-plugin-n@17.19.0(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3):
    eslint-plugin-n: private
  eslint-plugin-no-only-tests@3.3.0:
    eslint-plugin-no-only-tests: private
  eslint-plugin-perfectionist@4.13.0(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3):
    eslint-plugin-perfectionist: private
  eslint-plugin-pnpm@0.3.1(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-pnpm: private
  eslint-plugin-regexp@2.7.0(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-regexp: private
  eslint-plugin-toml@0.12.0(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-toml: private
  eslint-plugin-unicorn@59.0.1(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-unicorn: private
  eslint-plugin-unused-imports@4.1.4(@typescript-eslint/eslint-plugin@8.33.0(@typescript-eslint/parser@8.33.0(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-unused-imports: private
  eslint-plugin-vue@10.1.0(eslint@9.24.0(jiti@2.4.2))(vue-eslint-parser@10.1.3(eslint@9.24.0(jiti@2.4.2))):
    eslint-plugin-vue: private
  eslint-plugin-yml@1.18.0(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-yml: private
  eslint-processor-vue-blocks@2.0.0(@vue/compiler-sfc@3.5.16)(eslint@9.24.0(jiti@2.4.2)):
    eslint-processor-vue-blocks: private
  eslint-scope@8.3.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.0:
    eslint-visitor-keys: private
  espree@10.3.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  eventemitter3@5.0.1:
    eventemitter3: private
  execa@8.0.1:
    execa: private
  exsolve@1.0.5:
    exsolve: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-diff@1.3.0:
    fast-diff: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fault@2.0.1:
    fault: private
  fdir@6.4.5(picomatch@4.0.2):
    fdir: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up-simple@1.0.1:
    find-up-simple: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  format@0.2.2:
    format: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  get-east-asian-width@1.3.0:
    get-east-asian-width: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@8.0.1:
    get-stream: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@11.0.2:
    glob: private
  globals@16.2.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@3.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  hosted-git-info@2.8.9:
    hosted-git-info: private
  human-signals@5.0.0:
    human-signals: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@5.0.0:
    indent-string: private
  internal-slot@1.1.0:
    internal-slot: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-builtin-module@5.0.0:
    is-builtin-module: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@4.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-map@2.0.3:
    is-map: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@3.0.0:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  isarray@2.0.5:
    isarray: private
  isexe@2.0.0:
    isexe: private
  jackspeak@4.1.1:
    jackspeak: private
  jiti@2.4.2:
    jiti: private
  js-yaml@4.1.0:
    js-yaml: private
  jsdoc-type-pratt-parser@4.1.0:
    jsdoc-type-pratt-parser: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-better-errors@1.0.2:
    json-parse-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  jsonc-eslint-parser@2.4.0:
    jsonc-eslint-parser: private
  keyv@4.5.4:
    keyv: private
  layers/accounting:
    '@comanager/accounting-layer': private
  layers/budget:
    budget-layer: private
  layers/communication:
    '@comanager/communication-layer': private
  layers/companies:
    '@comanager/companies-layer': private
  layers/core:
    core-layer: private
  layers/hr:
    '@comanager/hr-layer': private
  layers/production:
    '@comanager/production-layer': private
  layers/recruitment:
    '@comanager/recruitment-layer': private
  layers/sales:
    '@comanager/sales-layer': private
  layers/tairo:
    '@cssninja/tairo': private
  layers/timemanagement:
    '@comanager/timemanagement-layer': private
  levn@0.4.1:
    levn: private
  lilconfig@3.1.3:
    lilconfig: private
  linkify-it@5.0.0:
    linkify-it: private
  listr2@8.3.3:
    listr2: private
  load-json-file@4.0.0:
    load-json-file: private
  local-pkg@1.1.1:
    local-pkg: private
  locate-path@6.0.0:
    locate-path: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash@4.17.21:
    lodash: private
  log-update@6.1.0:
    log-update: private
  longest-streak@3.1.0:
    longest-streak: private
  lru-cache@11.1.0:
    lru-cache: private
  magic-string@0.30.17:
    magic-string: private
  markdown-it@14.1.0:
    markdown-it: private
  markdown-table@3.0.4:
    markdown-table: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mdast-util-find-and-replace@3.0.2:
    mdast-util-find-and-replace: private
  mdast-util-from-markdown@2.0.2:
    mdast-util-from-markdown: private
  mdast-util-frontmatter@2.0.1:
    mdast-util-frontmatter: private
  mdast-util-gfm-autolink-literal@2.0.1:
    mdast-util-gfm-autolink-literal: private
  mdast-util-gfm-footnote@2.1.0:
    mdast-util-gfm-footnote: private
  mdast-util-gfm-strikethrough@2.0.0:
    mdast-util-gfm-strikethrough: private
  mdast-util-gfm-table@2.0.0:
    mdast-util-gfm-table: private
  mdast-util-gfm-task-list-item@2.0.0:
    mdast-util-gfm-task-list-item: private
  mdast-util-gfm@3.1.0:
    mdast-util-gfm: private
  mdast-util-phrasing@4.1.0:
    mdast-util-phrasing: private
  mdast-util-to-markdown@2.1.2:
    mdast-util-to-markdown: private
  mdast-util-to-string@4.0.0:
    mdast-util-to-string: private
  mdurl@2.0.0:
    mdurl: private
  memorystream@0.3.1:
    memorystream: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  micromark-core-commonmark@2.0.3:
    micromark-core-commonmark: private
  micromark-extension-frontmatter@2.0.0:
    micromark-extension-frontmatter: private
  micromark-extension-gfm-autolink-literal@2.1.0:
    micromark-extension-gfm-autolink-literal: private
  micromark-extension-gfm-footnote@2.1.0:
    micromark-extension-gfm-footnote: private
  micromark-extension-gfm-strikethrough@2.1.0:
    micromark-extension-gfm-strikethrough: private
  micromark-extension-gfm-table@2.1.1:
    micromark-extension-gfm-table: private
  micromark-extension-gfm-tagfilter@2.0.0:
    micromark-extension-gfm-tagfilter: private
  micromark-extension-gfm-task-list-item@2.1.0:
    micromark-extension-gfm-task-list-item: private
  micromark-extension-gfm@3.0.0:
    micromark-extension-gfm: private
  micromark-factory-destination@2.0.1:
    micromark-factory-destination: private
  micromark-factory-label@2.0.1:
    micromark-factory-label: private
  micromark-factory-space@2.0.1:
    micromark-factory-space: private
  micromark-factory-title@2.0.1:
    micromark-factory-title: private
  micromark-factory-whitespace@2.0.1:
    micromark-factory-whitespace: private
  micromark-util-character@2.1.1:
    micromark-util-character: private
  micromark-util-chunked@2.0.1:
    micromark-util-chunked: private
  micromark-util-classify-character@2.0.1:
    micromark-util-classify-character: private
  micromark-util-combine-extensions@2.0.1:
    micromark-util-combine-extensions: private
  micromark-util-decode-numeric-character-reference@2.0.2:
    micromark-util-decode-numeric-character-reference: private
  micromark-util-decode-string@2.0.1:
    micromark-util-decode-string: private
  micromark-util-encode@2.0.1:
    micromark-util-encode: private
  micromark-util-html-tag-name@2.0.1:
    micromark-util-html-tag-name: private
  micromark-util-normalize-identifier@2.0.1:
    micromark-util-normalize-identifier: private
  micromark-util-resolve-all@2.0.1:
    micromark-util-resolve-all: private
  micromark-util-sanitize-uri@2.0.1:
    micromark-util-sanitize-uri: private
  micromark-util-subtokenize@2.1.0:
    micromark-util-subtokenize: private
  micromark-util-symbol@2.0.1:
    micromark-util-symbol: private
  micromark-util-types@2.0.2:
    micromark-util-types: private
  micromark@4.0.2:
    micromark: private
  micromatch@4.0.8:
    micromatch: private
  mimic-fn@4.0.0:
    mimic-fn: private
  mimic-function@5.0.1:
    mimic-function: private
  min-indent@1.0.1:
    min-indent: private
  minimatch@3.1.2:
    minimatch: private
  minipass@7.1.2:
    minipass: private
  mlly@1.7.4:
    mlly: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  napi-postinstall@0.2.4:
    napi-postinstall: private
  natural-compare@1.4.0:
    natural-compare: private
  natural-orderby@5.0.0:
    natural-orderby: private
  nice-try@1.0.5:
    nice-try: private
  node-releases@2.0.19:
    node-releases: private
  normalize-package-data@2.5.0:
    normalize-package-data: private
  npm-run-path@5.3.0:
    npm-run-path: private
  nth-check@2.1.1:
    nth-check: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  onetime@6.0.0:
    onetime: private
  optionator@0.9.4:
    optionator: private
  orderedmap@2.1.1:
    orderedmap: private
  own-keys@1.0.1:
    own-keys: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  package-manager-detector@1.3.0:
    package-manager-detector: private
  parent-module@1.0.1:
    parent-module: private
  parse-gitignore@2.0.0:
    parse-gitignore: private
  parse-imports-exports@0.2.4:
    parse-imports-exports: private
  parse-json@4.0.0:
    parse-json: private
  parse-statements@1.0.11:
    parse-statements: private
  path-exists@4.0.0:
    path-exists: private
  path-key@2.0.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@2.0.0:
    path-scurry: private
  path-type@3.0.0:
    path-type: private
  pathe@2.0.3:
    pathe: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  pidtree@0.6.0:
    pidtree: private
  pify@3.0.0:
    pify: private
  pkg-types@2.1.0:
    pkg-types: private
  pluralize@8.0.0:
    pluralize: private
  pnpm-workspace-yaml@0.3.1:
    pnpm-workspace-yaml: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss@8.5.4:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prettier-linter-helpers@1.0.0:
    prettier-linter-helpers: private
  prettier@3.5.3:
    prettier: private
  prosemirror-changeset@2.3.1:
    prosemirror-changeset: private
  prosemirror-collab@1.3.1:
    prosemirror-collab: private
  prosemirror-commands@1.7.1:
    prosemirror-commands: private
  prosemirror-dropcursor@1.8.2:
    prosemirror-dropcursor: private
  prosemirror-gapcursor@1.3.2:
    prosemirror-gapcursor: private
  prosemirror-history@1.4.1:
    prosemirror-history: private
  prosemirror-inputrules@1.5.0:
    prosemirror-inputrules: private
  prosemirror-keymap@1.2.3:
    prosemirror-keymap: private
  prosemirror-markdown@1.13.2:
    prosemirror-markdown: private
  prosemirror-menu@1.2.5:
    prosemirror-menu: private
  prosemirror-model@1.25.1:
    prosemirror-model: private
  prosemirror-schema-basic@1.2.4:
    prosemirror-schema-basic: private
  prosemirror-schema-list@1.5.1:
    prosemirror-schema-list: private
  prosemirror-state@1.4.3:
    prosemirror-state: private
  prosemirror-tables@1.7.1:
    prosemirror-tables: private
  prosemirror-trailing-node@3.0.0(prosemirror-model@1.25.1)(prosemirror-state@1.4.3)(prosemirror-view@1.40.0):
    prosemirror-trailing-node: private
  prosemirror-transform@1.10.4:
    prosemirror-transform: private
  prosemirror-view@1.40.0:
    prosemirror-view: private
  punycode.js@2.3.1:
    punycode.js: private
  punycode@2.3.1:
    punycode: private
  quansync@0.2.10:
    quansync: private
  queue-microtask@1.2.3:
    queue-microtask: private
  read-pkg@3.0.0:
    read-pkg: private
  refa@0.12.1:
    refa: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regexp-ast-analysis@0.7.1:
    regexp-ast-analysis: private
  regexp-tree@0.1.27:
    regexp-tree: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  regjsparser@0.12.0:
    regjsparser: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve@1.22.10:
    resolve: private
  restore-cursor@5.1.0:
    restore-cursor: private
  reusify@1.1.0:
    reusify: private
  rfdc@1.4.1:
    rfdc: private
  rope-sequence@1.3.4:
    rope-sequence: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  scslre@0.3.0:
    scslre: private
  semver@5.7.2:
    semver: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  shebang-command@1.2.0:
    shebang-command: private
  shebang-regex@1.0.0:
    shebang-regex: private
  shell-quote@1.8.3:
    shell-quote: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@4.1.0:
    signal-exit: private
  sisteransi@1.0.5:
    sisteransi: private
  slice-ansi@5.0.0:
    slice-ansi: private
  source-map-js@1.2.1:
    source-map-js: private
  spdx-correct@3.2.0:
    spdx-correct: private
  spdx-exceptions@2.5.0:
    spdx-exceptions: private
  spdx-expression-parse@4.0.0:
    spdx-expression-parse: private
  spdx-license-ids@3.0.21:
    spdx-license-ids: private
  stable-hash@0.0.5:
    stable-hash: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  string-argv@0.3.2:
    string-argv: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@7.2.0:
    string-width: private
  string.prototype.padend@3.1.6:
    string.prototype.padend: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  strip-ansi@6.0.1:
    strip-ansi-cjs: private
  strip-ansi@7.1.0:
    strip-ansi: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-final-newline@3.0.0:
    strip-final-newline: private
  strip-indent@4.0.0:
    strip-indent: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  supports-color@5.5.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  synckit@0.9.3:
    synckit: private
  tairo-component-meta:
    '@cssninja/tairo-component-meta': private
  tapable@2.2.2:
    tapable: private
  tinyexec@1.0.1:
    tinyexec: private
  tinyglobby@0.2.14:
    tinyglobby: private
  tippy.js@6.3.7:
    tippy.js: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toml-eslint-parser@0.10.0:
    toml-eslint-parser: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  ts-declaration-location@1.0.7(typescript@5.8.3):
    ts-declaration-location: private
  tslib@2.8.1:
    tslib: private
  type-check@0.4.0:
    type-check: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  typescript@5.8.3:
    typescript: private
  uc.micro@2.1.0:
    uc.micro: private
  ufo@1.6.1:
    ufo: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  unist-util-is@6.0.0:
    unist-util-is: private
  unist-util-stringify-position@4.0.0:
    unist-util-stringify-position: private
  unist-util-visit-parents@6.0.1:
    unist-util-visit-parents: private
  unist-util-visit@5.0.0:
    unist-util-visit: private
  unrs-resolver@1.7.8:
    unrs-resolver: private
  update-browserslist-db@1.1.3(browserslist@4.25.0):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  util-deprecate@1.0.2:
    util-deprecate: private
  validate-npm-package-license@3.0.4:
    validate-npm-package-license: private
  vue-eslint-parser@10.1.3(eslint@9.24.0(jiti@2.4.2)):
    vue-eslint-parser: private
  vue@3.5.16(typescript@5.8.3):
    vue: private
  w3c-keyname@2.2.8:
    w3c-keyname: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@1.3.1:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@9.0.0:
    wrap-ansi: private
  xml-name-validator@4.0.0:
    xml-name-validator: private
  yaml-eslint-parser@1.3.0:
    yaml-eslint-parser: private
  yaml@2.8.0:
    yaml: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zwitch@2.0.4:
    zwitch: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.5.2
pendingBuilds:
  - better-sqlite3@11.9.1
  - esbuild@0.19.11
  - esbuild@0.25.3
  - '@parcel/watcher@2.5.1'
  - vue-demi@0.14.10(vue@3.5.13(typescript@5.8.3))
prunedAt: Sun, 15 Jun 2025 08:29:08 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/core@1.4.3'
  - '@emnapi/runtime@1.4.3'
  - '@emnapi/wasi-threads@1.0.2'
  - '@esbuild/aix-ppc64@0.19.11'
  - '@esbuild/aix-ppc64@0.25.3'
  - '@esbuild/aix-ppc64@0.25.4'
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.19.11'
  - '@esbuild/android-arm64@0.25.3'
  - '@esbuild/android-arm64@0.25.4'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.19.11'
  - '@esbuild/android-arm@0.25.3'
  - '@esbuild/android-arm@0.25.4'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.19.11'
  - '@esbuild/android-x64@0.25.3'
  - '@esbuild/android-x64@0.25.4'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-arm64@0.19.11'
  - '@esbuild/darwin-arm64@0.25.3'
  - '@esbuild/darwin-arm64@0.25.4'
  - '@esbuild/darwin-arm64@0.25.5'
  - '@esbuild/darwin-x64@0.19.11'
  - '@esbuild/darwin-x64@0.25.3'
  - '@esbuild/darwin-x64@0.25.4'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.19.11'
  - '@esbuild/freebsd-arm64@0.25.3'
  - '@esbuild/freebsd-arm64@0.25.4'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.19.11'
  - '@esbuild/freebsd-x64@0.25.3'
  - '@esbuild/freebsd-x64@0.25.4'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.19.11'
  - '@esbuild/linux-arm64@0.25.3'
  - '@esbuild/linux-arm64@0.25.4'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.19.11'
  - '@esbuild/linux-arm@0.25.3'
  - '@esbuild/linux-arm@0.25.4'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.19.11'
  - '@esbuild/linux-ia32@0.25.3'
  - '@esbuild/linux-ia32@0.25.4'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.19.11'
  - '@esbuild/linux-loong64@0.25.3'
  - '@esbuild/linux-loong64@0.25.4'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.19.11'
  - '@esbuild/linux-mips64el@0.25.3'
  - '@esbuild/linux-mips64el@0.25.4'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.19.11'
  - '@esbuild/linux-ppc64@0.25.3'
  - '@esbuild/linux-ppc64@0.25.4'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.19.11'
  - '@esbuild/linux-riscv64@0.25.3'
  - '@esbuild/linux-riscv64@0.25.4'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.19.11'
  - '@esbuild/linux-s390x@0.25.3'
  - '@esbuild/linux-s390x@0.25.4'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/linux-x64@0.19.11'
  - '@esbuild/linux-x64@0.25.3'
  - '@esbuild/linux-x64@0.25.4'
  - '@esbuild/linux-x64@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.3'
  - '@esbuild/netbsd-arm64@0.25.4'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.19.11'
  - '@esbuild/netbsd-x64@0.25.3'
  - '@esbuild/netbsd-x64@0.25.4'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.3'
  - '@esbuild/openbsd-arm64@0.25.4'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.19.11'
  - '@esbuild/openbsd-x64@0.25.3'
  - '@esbuild/openbsd-x64@0.25.4'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.19.11'
  - '@esbuild/sunos-x64@0.25.3'
  - '@esbuild/sunos-x64@0.25.4'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.19.11'
  - '@esbuild/win32-arm64@0.25.3'
  - '@esbuild/win32-arm64@0.25.4'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.19.11'
  - '@esbuild/win32-ia32@0.25.3'
  - '@esbuild/win32-ia32@0.25.4'
  - '@esbuild/win32-ia32@0.25.5'
  - '@img/sharp-darwin-arm64@0.34.0'
  - '@img/sharp-darwin-x64@0.34.0'
  - '@img/sharp-libvips-darwin-arm64@1.1.0'
  - '@img/sharp-libvips-darwin-x64@1.1.0'
  - '@img/sharp-libvips-linux-arm64@1.1.0'
  - '@img/sharp-libvips-linux-arm@1.1.0'
  - '@img/sharp-libvips-linux-ppc64@1.1.0'
  - '@img/sharp-libvips-linux-s390x@1.1.0'
  - '@img/sharp-libvips-linux-x64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-arm64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-x64@1.1.0'
  - '@img/sharp-linux-arm64@0.34.0'
  - '@img/sharp-linux-arm@0.34.0'
  - '@img/sharp-linux-s390x@0.34.0'
  - '@img/sharp-linux-x64@0.34.0'
  - '@img/sharp-linuxmusl-arm64@0.34.0'
  - '@img/sharp-linuxmusl-x64@0.34.0'
  - '@img/sharp-wasm32@0.34.0'
  - '@img/sharp-win32-ia32@0.34.0'
  - '@napi-rs/wasm-runtime@0.2.10'
  - '@napi-rs/wasm-runtime@0.2.9'
  - '@oxc-parser/binding-darwin-arm64@0.56.5'
  - '@oxc-parser/binding-darwin-arm64@0.61.2'
  - '@oxc-parser/binding-darwin-arm64@0.70.0'
  - '@oxc-parser/binding-darwin-x64@0.56.5'
  - '@oxc-parser/binding-darwin-x64@0.61.2'
  - '@oxc-parser/binding-darwin-x64@0.70.0'
  - '@oxc-parser/binding-freebsd-x64@0.70.0'
  - '@oxc-parser/binding-linux-arm-gnueabihf@0.56.5'
  - '@oxc-parser/binding-linux-arm-gnueabihf@0.61.2'
  - '@oxc-parser/binding-linux-arm-gnueabihf@0.70.0'
  - '@oxc-parser/binding-linux-arm-musleabihf@0.70.0'
  - '@oxc-parser/binding-linux-arm64-gnu@0.56.5'
  - '@oxc-parser/binding-linux-arm64-gnu@0.61.2'
  - '@oxc-parser/binding-linux-arm64-gnu@0.70.0'
  - '@oxc-parser/binding-linux-arm64-musl@0.56.5'
  - '@oxc-parser/binding-linux-arm64-musl@0.61.2'
  - '@oxc-parser/binding-linux-arm64-musl@0.70.0'
  - '@oxc-parser/binding-linux-riscv64-gnu@0.70.0'
  - '@oxc-parser/binding-linux-s390x-gnu@0.70.0'
  - '@oxc-parser/binding-linux-x64-gnu@0.56.5'
  - '@oxc-parser/binding-linux-x64-gnu@0.61.2'
  - '@oxc-parser/binding-linux-x64-gnu@0.70.0'
  - '@oxc-parser/binding-linux-x64-musl@0.56.5'
  - '@oxc-parser/binding-linux-x64-musl@0.61.2'
  - '@oxc-parser/binding-linux-x64-musl@0.70.0'
  - '@oxc-parser/binding-wasm32-wasi@0.56.5'
  - '@oxc-parser/binding-wasm32-wasi@0.61.2'
  - '@oxc-parser/binding-wasm32-wasi@0.70.0'
  - '@oxc-parser/binding-win32-arm64-msvc@0.56.5'
  - '@oxc-parser/binding-win32-arm64-msvc@0.61.2'
  - '@oxc-parser/binding-win32-arm64-msvc@0.70.0'
  - '@parcel/watcher-android-arm64@2.5.1'
  - '@parcel/watcher-darwin-arm64@2.5.1'
  - '@parcel/watcher-darwin-x64@2.5.1'
  - '@parcel/watcher-freebsd-x64@2.5.1'
  - '@parcel/watcher-linux-arm-glibc@2.5.1'
  - '@parcel/watcher-linux-arm-musl@2.5.1'
  - '@parcel/watcher-linux-arm64-glibc@2.5.1'
  - '@parcel/watcher-linux-arm64-musl@2.5.1'
  - '@parcel/watcher-linux-x64-glibc@2.5.1'
  - '@parcel/watcher-linux-x64-musl@2.5.1'
  - '@parcel/watcher-win32-arm64@2.5.1'
  - '@parcel/watcher-win32-ia32@2.5.1'
  - '@resvg/resvg-js-android-arm-eabi@2.6.2'
  - '@resvg/resvg-js-android-arm64@2.6.2'
  - '@resvg/resvg-js-darwin-arm64@2.6.2'
  - '@resvg/resvg-js-darwin-x64@2.6.2'
  - '@resvg/resvg-js-linux-arm-gnueabihf@2.6.2'
  - '@resvg/resvg-js-linux-arm64-gnu@2.6.2'
  - '@resvg/resvg-js-linux-arm64-musl@2.6.2'
  - '@resvg/resvg-js-linux-x64-gnu@2.6.2'
  - '@resvg/resvg-js-linux-x64-musl@2.6.2'
  - '@resvg/resvg-js-win32-arm64-msvc@2.6.2'
  - '@resvg/resvg-js-win32-ia32-msvc@2.6.2'
  - '@rollup/rollup-android-arm-eabi@4.40.1'
  - '@rollup/rollup-android-arm-eabi@4.40.2'
  - '@rollup/rollup-android-arm-eabi@4.41.1'
  - '@rollup/rollup-android-arm64@4.40.1'
  - '@rollup/rollup-android-arm64@4.40.2'
  - '@rollup/rollup-android-arm64@4.41.1'
  - '@rollup/rollup-darwin-arm64@4.40.1'
  - '@rollup/rollup-darwin-arm64@4.40.2'
  - '@rollup/rollup-darwin-arm64@4.41.1'
  - '@rollup/rollup-darwin-x64@4.40.1'
  - '@rollup/rollup-darwin-x64@4.40.2'
  - '@rollup/rollup-darwin-x64@4.41.1'
  - '@rollup/rollup-freebsd-arm64@4.40.1'
  - '@rollup/rollup-freebsd-arm64@4.40.2'
  - '@rollup/rollup-freebsd-arm64@4.41.1'
  - '@rollup/rollup-freebsd-x64@4.40.1'
  - '@rollup/rollup-freebsd-x64@4.40.2'
  - '@rollup/rollup-freebsd-x64@4.41.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.40.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.40.2'
  - '@rollup/rollup-linux-arm-gnueabihf@4.41.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.40.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.40.2'
  - '@rollup/rollup-linux-arm-musleabihf@4.41.1'
  - '@rollup/rollup-linux-arm64-gnu@4.40.1'
  - '@rollup/rollup-linux-arm64-gnu@4.40.2'
  - '@rollup/rollup-linux-arm64-gnu@4.41.1'
  - '@rollup/rollup-linux-arm64-musl@4.40.1'
  - '@rollup/rollup-linux-arm64-musl@4.40.2'
  - '@rollup/rollup-linux-arm64-musl@4.41.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.40.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.40.2'
  - '@rollup/rollup-linux-loongarch64-gnu@4.41.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.40.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.40.2'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.41.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.40.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.40.2'
  - '@rollup/rollup-linux-riscv64-gnu@4.41.1'
  - '@rollup/rollup-linux-riscv64-musl@4.40.1'
  - '@rollup/rollup-linux-riscv64-musl@4.40.2'
  - '@rollup/rollup-linux-riscv64-musl@4.41.1'
  - '@rollup/rollup-linux-s390x-gnu@4.40.1'
  - '@rollup/rollup-linux-s390x-gnu@4.40.2'
  - '@rollup/rollup-linux-s390x-gnu@4.41.1'
  - '@rollup/rollup-linux-x64-gnu@4.40.1'
  - '@rollup/rollup-linux-x64-gnu@4.40.2'
  - '@rollup/rollup-linux-x64-gnu@4.41.1'
  - '@rollup/rollup-linux-x64-musl@4.40.1'
  - '@rollup/rollup-linux-x64-musl@4.40.2'
  - '@rollup/rollup-linux-x64-musl@4.41.1'
  - '@rollup/rollup-win32-arm64-msvc@4.40.1'
  - '@rollup/rollup-win32-arm64-msvc@4.40.2'
  - '@rollup/rollup-win32-arm64-msvc@4.41.1'
  - '@rollup/rollup-win32-ia32-msvc@4.40.1'
  - '@rollup/rollup-win32-ia32-msvc@4.40.2'
  - '@rollup/rollup-win32-ia32-msvc@4.41.1'
  - '@tailwindcss/oxide-android-arm64@4.0.0-beta.8'
  - '@tailwindcss/oxide-android-arm64@4.1.5'
  - '@tailwindcss/oxide-android-arm64@4.1.6'
  - '@tailwindcss/oxide-android-arm64@4.1.7'
  - '@tailwindcss/oxide-android-arm64@4.1.8'
  - '@tailwindcss/oxide-darwin-arm64@4.0.0-beta.8'
  - '@tailwindcss/oxide-darwin-arm64@4.1.5'
  - '@tailwindcss/oxide-darwin-arm64@4.1.6'
  - '@tailwindcss/oxide-darwin-arm64@4.1.7'
  - '@tailwindcss/oxide-darwin-arm64@4.1.8'
  - '@tailwindcss/oxide-darwin-x64@4.0.0-beta.8'
  - '@tailwindcss/oxide-darwin-x64@4.1.5'
  - '@tailwindcss/oxide-darwin-x64@4.1.6'
  - '@tailwindcss/oxide-darwin-x64@4.1.7'
  - '@tailwindcss/oxide-darwin-x64@4.1.8'
  - '@tailwindcss/oxide-freebsd-x64@4.0.0-beta.8'
  - '@tailwindcss/oxide-freebsd-x64@4.1.5'
  - '@tailwindcss/oxide-freebsd-x64@4.1.6'
  - '@tailwindcss/oxide-freebsd-x64@4.1.7'
  - '@tailwindcss/oxide-freebsd-x64@4.1.8'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.0.0-beta.8'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.5'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.6'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.7'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.8'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.0.0-beta.8'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.5'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.6'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.7'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.8'
  - '@tailwindcss/oxide-linux-arm64-musl@4.0.0-beta.8'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.5'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.6'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.7'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.8'
  - '@tailwindcss/oxide-linux-x64-gnu@4.0.0-beta.8'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.5'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.6'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.7'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.8'
  - '@tailwindcss/oxide-linux-x64-musl@4.0.0-beta.8'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.5'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.6'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.7'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.8'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.5'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.6'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.7'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.8'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.0.0-beta.8'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.5'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.6'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.7'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.8'
  - '@tybys/wasm-util@0.9.0'
  - '@unrs/resolver-binding-darwin-arm64@1.7.2'
  - '@unrs/resolver-binding-darwin-arm64@1.7.8'
  - '@unrs/resolver-binding-darwin-x64@1.7.2'
  - '@unrs/resolver-binding-darwin-x64@1.7.8'
  - '@unrs/resolver-binding-freebsd-x64@1.7.2'
  - '@unrs/resolver-binding-freebsd-x64@1.7.8'
  - '@unrs/resolver-binding-linux-arm-gnueabihf@1.7.2'
  - '@unrs/resolver-binding-linux-arm-gnueabihf@1.7.8'
  - '@unrs/resolver-binding-linux-arm-musleabihf@1.7.2'
  - '@unrs/resolver-binding-linux-arm-musleabihf@1.7.8'
  - '@unrs/resolver-binding-linux-arm64-gnu@1.7.2'
  - '@unrs/resolver-binding-linux-arm64-gnu@1.7.8'
  - '@unrs/resolver-binding-linux-arm64-musl@1.7.2'
  - '@unrs/resolver-binding-linux-arm64-musl@1.7.8'
  - '@unrs/resolver-binding-linux-ppc64-gnu@1.7.2'
  - '@unrs/resolver-binding-linux-ppc64-gnu@1.7.8'
  - '@unrs/resolver-binding-linux-riscv64-gnu@1.7.2'
  - '@unrs/resolver-binding-linux-riscv64-gnu@1.7.8'
  - '@unrs/resolver-binding-linux-riscv64-musl@1.7.2'
  - '@unrs/resolver-binding-linux-riscv64-musl@1.7.8'
  - '@unrs/resolver-binding-linux-s390x-gnu@1.7.2'
  - '@unrs/resolver-binding-linux-s390x-gnu@1.7.8'
  - '@unrs/resolver-binding-linux-x64-gnu@1.7.2'
  - '@unrs/resolver-binding-linux-x64-gnu@1.7.8'
  - '@unrs/resolver-binding-linux-x64-musl@1.7.2'
  - '@unrs/resolver-binding-linux-x64-musl@1.7.8'
  - '@unrs/resolver-binding-wasm32-wasi@1.7.2'
  - '@unrs/resolver-binding-wasm32-wasi@1.7.8'
  - '@unrs/resolver-binding-win32-arm64-msvc@1.7.2'
  - '@unrs/resolver-binding-win32-arm64-msvc@1.7.8'
  - '@unrs/resolver-binding-win32-ia32-msvc@1.7.2'
  - '@unrs/resolver-binding-win32-ia32-msvc@1.7.8'
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.29.2
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-darwin-x64@1.29.2
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.29.2
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.29.2
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.29.2
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.29.2
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.29.2
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.29.2
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.29.2
  - lightningcss-win32-arm64-msvc@1.30.1
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\comanager\client\node_modules\.pnpm
virtualStoreDirMaxLength: 60
