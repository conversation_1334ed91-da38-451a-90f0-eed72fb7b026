@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\@antfu+eslint-config@4.13.2_0a5d0db20f5f05dc348abf1de93aa216\node_modules\@antfu\eslint-config\bin\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\@antfu+eslint-config@4.13.2_0a5d0db20f5f05dc348abf1de93aa216\node_modules\@antfu\eslint-config\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\@antfu+eslint-config@4.13.2_0a5d0db20f5f05dc348abf1de93aa216\node_modules\@antfu\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\@antfu+eslint-config@4.13.2_0a5d0db20f5f05dc348abf1de93aa216\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\@antfu+eslint-config@4.13.2_0a5d0db20f5f05dc348abf1de93aa216\node_modules\@antfu\eslint-config\bin\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\@antfu+eslint-config@4.13.2_0a5d0db20f5f05dc348abf1de93aa216\node_modules\@antfu\eslint-config\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\@antfu+eslint-config@4.13.2_0a5d0db20f5f05dc348abf1de93aa216\node_modules\@antfu\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\@antfu+eslint-config@4.13.2_0a5d0db20f5f05dc348abf1de93aa216\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\@antfu\eslint-config\bin\index.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\@antfu\eslint-config\bin\index.js" %*
)
