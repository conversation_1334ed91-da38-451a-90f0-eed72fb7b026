import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

const learningMaterials = [
  {
    key: "what_is_business_idea",
    title: "What is a Business Idea?",
    content: `
      <h3>Understanding Business Ideas</h3>
      <p>A business idea is a concept that can be used for financial gain that is usually centered on a product or service that can be offered for money. An idea is the base of the pyramid when it comes to the business as a whole.</p>
      
      <h4>Key Components of a Strong Business Idea:</h4>
      <ul>
        <li><strong>Problem Identification:</strong> What specific problem does your idea solve?</li>
        <li><strong>Target Market:</strong> Who are your potential customers?</li>
        <li><strong>Unique Value Proposition:</strong> What makes your solution different?</li>
        <li><strong>Feasibility:</strong> Can this idea be realistically implemented?</li>
        <li><strong>Scalability:</strong> Can the business grow over time?</li>
      </ul>
      
      <p>Remember, a good business idea doesn't have to be completely original. Many successful businesses are improvements on existing ideas or serve underserved markets.</p>
    `,
    videoUrls: [
      "https://www.youtube.com/embed/dQw4w9WgXcQ", // Replace with actual educational videos
    ],
    order: 1,
  },
  {
    key: "market_research",
    title: "Market Research Fundamentals",
    content: `
      <h3>Why Market Research Matters</h3>
      <p>Market research is the process of gathering information about your target market and customers. It helps validate your business idea and understand the competitive landscape.</p>
      
      <h4>Types of Market Research:</h4>
      <ul>
        <li><strong>Primary Research:</strong> Surveys, interviews, focus groups</li>
        <li><strong>Secondary Research:</strong> Industry reports, competitor analysis, online research</li>
      </ul>
      
      <h4>Key Questions to Answer:</h4>
      <ul>
        <li>How big is your target market?</li>
        <li>Who are your main competitors?</li>
        <li>What are current market trends?</li>
        <li>What are customers willing to pay?</li>
        <li>What are the barriers to entry?</li>
      </ul>
      
      <p>Use tools like Google Trends, industry reports, and customer surveys to gather valuable insights.</p>
    `,
    videoUrls: [],
    order: 2,
  },
  {
    key: "target_audience",
    title: "Identifying Your Target Audience",
    content: `
      <h3>Defining Your Ideal Customer</h3>
      <p>Your target audience is the specific group of people most likely to buy your product or service. Understanding them deeply is crucial for business success.</p>
      
      <h4>Creating Customer Personas:</h4>
      <ul>
        <li><strong>Demographics:</strong> Age, gender, income, education, location</li>
        <li><strong>Psychographics:</strong> Values, interests, lifestyle, personality</li>
        <li><strong>Behavioral:</strong> Buying habits, brand loyalty, usage patterns</li>
        <li><strong>Pain Points:</strong> Problems they face that your product solves</li>
      </ul>
      
      <h4>Research Methods:</h4>
      <ul>
        <li>Customer surveys and interviews</li>
        <li>Social media analytics</li>
        <li>Website analytics</li>
        <li>Competitor customer analysis</li>
      </ul>
      
      <p>The more specific you can be about your target audience, the more effective your marketing and product development will be.</p>
    `,
    videoUrls: [],
    order: 3,
  },
  {
    key: "competitive_analysis",
    title: "Competitive Analysis",
    content: `
      <h3>Understanding Your Competition</h3>
      <p>Competitive analysis involves identifying and evaluating your competitors to understand their strengths, weaknesses, and strategies.</p>
      
      <h4>Types of Competitors:</h4>
      <ul>
        <li><strong>Direct Competitors:</strong> Offer the same product/service to the same market</li>
        <li><strong>Indirect Competitors:</strong> Offer different products that solve the same problem</li>
        <li><strong>Substitute Competitors:</strong> Alternative solutions customers might choose</li>
      </ul>
      
      <h4>What to Analyze:</h4>
      <ul>
        <li>Products and services offered</li>
        <li>Pricing strategies</li>
        <li>Marketing and sales tactics</li>
        <li>Customer reviews and feedback</li>
        <li>Market share and positioning</li>
        <li>Strengths and weaknesses</li>
      </ul>
      
      <p>Use this analysis to identify gaps in the market and opportunities for differentiation.</p>
    `,
    videoUrls: [],
    order: 4,
  },
  {
    key: "value_proposition",
    title: "Creating Your Value Proposition",
    content: `
      <h3>What is a Value Proposition?</h3>
      <p>A value proposition is a clear statement that explains how your product solves customers' problems, delivers specific benefits, and tells the ideal customer why they should buy from you instead of the competition.</p>
      
      <h4>Components of a Strong Value Proposition:</h4>
      <ul>
        <li><strong>Headline:</strong> What you do and for whom</li>
        <li><strong>Sub-headline:</strong> Specific benefits or outcomes</li>
        <li><strong>Key Benefits:</strong> List of key benefits or features</li>
        <li><strong>Visual Element:</strong> Image or video that reinforces your message</li>
      </ul>
      
      <h4>Value Proposition Canvas:</h4>
      <ul>
        <li>Customer jobs (what customers are trying to accomplish)</li>
        <li>Pain points (problems and frustrations)</li>
        <li>Gain creators (how you create customer gains)</li>
        <li>Pain relievers (how you alleviate pains)</li>
      </ul>
      
      <p>Your value proposition should be clear, specific, and focused on customer benefits rather than features.</p>
    `,
    videoUrls: [],
    order: 5,
  },
  {
    key: "business_model",
    title: "Business Model Basics",
    content: `
      <h3>Understanding Business Models</h3>
      <p>A business model describes how your company creates, delivers, and captures value. It's the foundation of how you'll make money.</p>
      
      <h4>Key Components (Business Model Canvas):</h4>
      <ul>
        <li><strong>Value Propositions:</strong> What value you deliver to customers</li>
        <li><strong>Customer Segments:</strong> Who you serve</li>
        <li><strong>Channels:</strong> How you reach and deliver to customers</li>
        <li><strong>Customer Relationships:</strong> How you interact with customers</li>
        <li><strong>Revenue Streams:</strong> How you make money</li>
        <li><strong>Key Resources:</strong> What you need to operate</li>
        <li><strong>Key Activities:</strong> What you must do to operate</li>
        <li><strong>Key Partnerships:</strong> Who helps you operate</li>
        <li><strong>Cost Structure:</strong> What it costs to operate</li>
      </ul>
      
      <h4>Common Revenue Models:</h4>
      <ul>
        <li>Product sales</li>
        <li>Subscription/recurring revenue</li>
        <li>Freemium</li>
        <li>Commission/marketplace</li>
        <li>Advertising</li>
        <li>Licensing</li>
      </ul>
      
      <p>Choose a business model that aligns with your value proposition and target market preferences.</p>
    `,
    videoUrls: [],
    order: 6,
  },
];

const greeting = {
  title: "Welcome to Your Business Journey!",
  content: `
    <p>It's fantastic that you're thinking about starting a company! Don't worry if you don't have a complete business plan yet - this step is designed to help you focus on your core business idea with some teaching materials and guidance on how to develop a strong business concept.</p>
    
    <p>If you already have your business idea worked out, you can copy and paste it into the text area below, or upload a document that we'll parse and add to your document library.</p>
    
    <p><strong>Remember:</strong> Every successful business started with just an idea. Take your time, use the learning materials below, and don't be afraid to iterate and refine your concept as you learn more.</p>
  `,
  isActive: true,
};

async function seedBusinessIdeaData() {
  try {
    console.log("🌱 Seeding business idea data...");

    // Create greeting
    console.log("Creating greeting...");
    await prisma.businessIdeaGreeting.create({
      data: greeting,
    });

    // Create learning materials
    console.log("Creating learning materials...");
    for (const material of learningMaterials) {
      await prisma.learningMaterial.create({
        data: material,
      });
    }

    console.log("✅ Business idea data seeded successfully!");
  } catch (error) {
    console.error("❌ Error seeding business idea data:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function if this file is executed directly
if (require.main === module) {
  seedBusinessIdeaData()
    .then(() => {
      console.log("Seeding completed!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Seeding failed:", error);
      process.exit(1);
    });
}

export { seedBusinessIdeaData };
