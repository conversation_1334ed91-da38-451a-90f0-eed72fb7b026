<template>
  <div class="flex min-h-screen w-full items-center justify-center">
    <div class="w-full max-w-md text-center">
      <div class="mb-6 flex justify-center">
        <div
          class="flex h-16 w-16 items-center justify-center rounded-full bg-success-100 dark:bg-success-900"
        >
          <Icon
            name="ph:check-circle-duotone"
            class="text-success-500 size-10"
          />
        </div>
      </div>
      <BaseHeading as="h1" size="2xl" weight="medium" class="mb-2">
        {{ t("auth.email_verified.title") }}
      </BaseHeading>
      <BaseParagraph size="sm" class="text-muted-400 dark:text-muted-300 mb-8">
        {{ t("auth.email_verified.description") }}
      </BaseParagraph>
      <div class="flex flex-col gap-4">
        <BaseButton to="/" variant="primary" rounded="lg" class="w-full">
          {{ t("auth.email_verified.login_button") }}
        </BaseButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";

const { t } = useI18n();

definePageMeta({
  layout: false,
  title: "Email Verified",
});
</script>
