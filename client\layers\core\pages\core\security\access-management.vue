<!-- client/layers/core/pages/core/security/access-management.vue -->

<template>
  <div class="p-6 dark:bg-muted-900">
    <TairoContentWrapper>
      <template #left>
        <div class="flex flex-col">
          <h2 class="text-xl font-bold text-muted-800 dark:text-white">
            Access Rights Management
          </h2>
          <p class="text-muted-400">
            Control which user roles can access specific pages and modules
          </p>
        </div>
      </template>
      <template #right>
        <div class="flex items-center gap-2">
          <BaseButton
            color="primary"
            :disabled="isInitializing"
            @click="initializeAccessRights"
          >
            <span v-if="isInitializing">
              <Icon name="line-md:loading-twotone-loop" class="h-5 w-5 mr-2" />
              Initializing...
            </span>
            <span v-else>Initialize Access Rights</span>
          </BaseButton>
          <BaseButton color="success" :disabled="isSaving" @click="saveChanges">
            <span v-if="isSaving">
              <Icon name="line-md:loading-twotone-loop" class="h-5 w-5 mr-2" />
              Saving...
            </span>
            <span v-else>Save Changes</span>
          </BaseButton>
        </div>
      </template>

      <div class="mt-6">
        <!-- Filters -->
        <div class="flex flex-wrap justify-between gap-4 mb-6">
          <div class="flex flex-wrap gap-4">
            <div>
              <BaseSelect
                v-model="selectedModule"
                label="Filter by Module"
                :classes="{
                  wrapper: 'w-40',
                }"
              >
                <option value="">All Modules</option>
                <option v-for="module in modules" :key="module" :value="module">
                  {{ module.charAt(0).toUpperCase() + module.slice(1) }}
                </option>
              </BaseSelect>
            </div>
            <div>
              <BaseSelect
                v-model="selectedRole"
                label="Filter by Role"
                :classes="{
                  wrapper: 'w-40',
                }"
              >
                <option value="">All Roles</option>
                <option v-for="role in roles" :key="role" :value="role">
                  {{ role }}
                </option>
              </BaseSelect>
            </div>
          </div>
          <div>
            <BaseInput
              v-model="searchQuery"
              icon="lucide:search"
              placeholder="Search by path or title"
              :classes="{
                wrapper: 'w-64',
              }"
            />
          </div>
        </div>

        <!-- Access Rights Table -->
        <div v-if="isLoading" class="flex justify-center py-10">
          <Icon
            name="line-md:loading-twotone-loop"
            class="h-10 w-10 text-primary-500"
          />
        </div>
        <div v-else-if="paginatedRoutes.length === 0" class="py-10">
          <BasePlaceholderPage
            title="No matching results"
            subtitle="Try adjusting your search or filter criteria"
          >
            <template #image>
              <img
                class="block dark:hidden"
                src="/img/illustrations/placeholders/flat/placeholder-search-4.svg"
                alt="No results"
              />
              <img
                class="hidden dark:block"
                src="/img/illustrations/placeholders/flat/placeholder-search-4-dark.svg"
                alt="No results"
              />
            </template>
          </BasePlaceholderPage>
        </div>
        <div v-else>
          <TairoTable rounded="sm" :scrollable="true">
            <template #header>
              <TairoTableHeading uppercase spaced>Path</TairoTableHeading>
              <TairoTableHeading uppercase spaced>Title</TairoTableHeading>
              <TairoTableHeading uppercase spaced>Module</TairoTableHeading>
              <TairoTableHeading
                v-for="role in roles"
                :key="role"
                uppercase
                spaced
                class="text-center"
              >
                {{ role }}
              </TairoTableHeading>
            </template>

            <TairoTableRow v-for="route in paginatedRoutes" :key="route.path">
              <TairoTableCell spaced>
                <span class="font-medium text-sm">{{ route.path }}</span>
              </TairoTableCell>
              <TairoTableCell spaced>
                <span class="text-sm">{{ route.title }}</span>
              </TairoTableCell>
              <TairoTableCell spaced>
                <BaseTag
                  :color="getModuleColor(route.module)"
                  variant="default"
                  rounded="full"
                  size="sm"
                  class="capitalize"
                >
                  {{ route.module }}
                </BaseTag>
              </TairoTableCell>
              <TairoTableCell
                v-for="role in roles"
                :key="`${route.path}-${role}`"
                spaced
                class="text-center"
              >
                <BaseCheckbox
                  v-model="accessRights[`${route.path}-${role}`]"
                  :value="true"
                  :name="`access-${route.path}-${role}`"
                  rounded="sm"
                  color="primary"
                />
              </TairoTableCell>
            </TairoTableRow>
          </TairoTable>

          <!-- Pagination -->
          <div class="mt-6">
            <BasePagination
              :total-items="filteredRoutes.length"
              :items-per-page="itemsPerPage"
              :page="currentPage"
              rounded="lg"
              @update:page="currentPage = $event"
            />
          </div>
        </div>
      </div>
    </TairoContentWrapper>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRouteExtractor } from "../../../../../.app/composables/useRouteExtractor";
import {
  useAccessRightsStore,
  type AccessRight,
} from "../../../stores/useAccessRightsStore";

// Toast notifications
const { add } = useNuiToasts();

// Access rights store
const accessRightsStore = useAccessRightsStore();

// Function to get color for module tags
function getModuleColor(module: string): string {
  const moduleColors: Record<string, string> = {
    core: "primary",
    budget: "success",
    hr: "info",
    companies: "warning",
    production: "danger",
    accounting: "purple",
    recruitment: "yellow",
    communication: "pink",
  };

  return moduleColors[module] || "muted";
}

// Roles (excluding SUPERADMIN who has access to everything)
const roles = ["ADMIN", "PROJECTLEADER", "SALESMAN", "WORKER", "CLIENT"];

// Routes and modules using the composable
const { extractAllRoutes, getAllModules } = useRouteExtractor();
const routes = ref(extractAllRoutes());
const modules = ref(getAllModules());

// Filters
const selectedModule = ref("");
const selectedRole = ref("");
const searchQuery = ref("");

// Pagination
const currentPage = ref(1);
const itemsPerPage = ref(10);

// Loading states
const isLoading = ref(false);
const isSaving = ref(false);
const isInitializing = ref(false);

// Access rights state
const accessRights = ref<Record<string, boolean>>({});

// Filtered routes based on search and filters
const filteredRoutes = computed(() => {
  let result = routes.value;

  // Filter by module
  if (selectedModule.value) {
    result = result.filter((route) => route.module === selectedModule.value);
  }

  // Filter by role
  if (selectedRole.value) {
    // If a role is selected, only show routes where that role has access
    result = result.filter((route) => {
      const key = `${route.path}-${selectedRole.value}`;
      return key in accessRights.value;
    });
  }

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(
      (route) =>
        route.path.toLowerCase().includes(query) ||
        route.title.toLowerCase().includes(query)
    );
  }

  return result;
});

// Paginated routes
const paginatedRoutes = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredRoutes.value.slice(start, end);
});

// Fetch access rights from the server
async function fetchAccessRights() {
  try {
    isLoading.value = true;

    // Fetch all access rights using the store
    const data = await accessRightsStore.getAllAccessRights();

    // Initialize access rights state
    const newAccessRights: Record<string, boolean> = {};

    // First, set all to false
    routes.value.forEach((route) => {
      roles.forEach((role) => {
        newAccessRights[`${route.path}-${role}`] = false;
      });
    });

    // Then update with values from the server
    data.forEach((right: AccessRight) => {
      newAccessRights[`${right.path}-${right.role}`] = right.canAccess;
    });

    accessRights.value = newAccessRights;
    add({
      title: "Success",
      description: "Access rights loaded successfully",
      icon: "ph:check-duotone",
      progress: true,
    });
  } catch (error) {
    console.error("Error fetching access rights:", error);
    add({
      title: "Error",
      description: "Failed to load access rights",
      icon: "ph:warning-duotone",
      progress: true,
    });
  } finally {
    isLoading.value = false;
  }
}

// Save changes to the server
async function saveChanges() {
  try {
    isSaving.value = true;

    // Convert access rights to the format expected by the API
    const accessRightsToSave: AccessRight[] = [];

    for (const key in accessRights.value) {
      const [path, role] = key.split("-");

      // Ensure path and role are defined
      if (!path || !role) continue;

      // Find the module from the routes
      const route = routes.value.find((r) => r.path === path);
      const module = route?.module || path.split("/")[1] || "core";

      // Only add if role is one of the valid roles
      if (roles.includes(role)) {
        accessRightsToSave.push({
          path,
          role,
          module,
          canAccess: accessRights.value[key] || false,
        });
      }
    }

    // Send to the server using the store
    await accessRightsStore.bulkUpdateAccessRights(accessRightsToSave);

    add({
      title: "Success",
      description: "Access rights saved successfully",
      icon: "ph:check-duotone",
      progress: true,
    });
  } catch (error) {
    console.error("Error saving access rights:", error);
    add({
      title: "Error",
      description: "Failed to save access rights",
      icon: "ph:warning-duotone",
      progress: true,
    });
  } finally {
    isSaving.value = false;
  }
}

// Initialize access rights for all routes
async function initializeAccessRights() {
  try {
    isInitializing.value = true;

    // Extract all routes
    const allRoutes = routes.value.map((route) => ({
      path: route.path,
      module: route.module,
      title: route.title,
    }));

    // Send to the server using the store
    await accessRightsStore.initializeAccessRights(allRoutes);

    // Refresh access rights
    await fetchAccessRights();

    add({
      title: "Success",
      description: "Access rights initialized successfully",
      icon: "ph:check-duotone",
      progress: true,
    });
  } catch (error) {
    console.error("Error initializing access rights:", error);
    add({
      title: "Error",
      description: "Failed to initialize access rights",
      icon: "ph:warning-duotone",
      progress: true,
    });
  } finally {
    isInitializing.value = false;
  }
}

// Fetch access rights on component mount
onMounted(async () => {
  await fetchAccessRights();
});
</script>
