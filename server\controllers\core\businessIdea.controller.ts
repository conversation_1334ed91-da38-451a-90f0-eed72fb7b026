import { Request, Response, NextFunction } from "express";
import { prisma } from "../../lib/prisma.js";
import { ActivityLogger } from "../../services/activityLogger";

// Create a simple error function instead of using http-errors
function createError(statusCode: number, message: string) {
  const error: any = new Error(message);
  error.statusCode = statusCode;
  return error;
}

// Get business idea for a company
export const getBusinessIdea = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { companyId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      return next(createError(401, "User not authenticated"));
    }

    // Check if user has access to this company
    const userCompany = await prisma.userCompany.findFirst({
      where: {
        userId: userId,
        companyId: parseInt(companyId),
      },
    });

    if (!userCompany) {
      return next(createError(403, "Access denied to this company"));
    }

    const businessIdea = await prisma.businessIdea.findUnique({
      where: { companyId: parseInt(companyId) },
      include: {
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true,
          },
        },
        updater: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true,
          },
        },
        collaborators: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true,
              },
            },
          },
        },
        notes: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true,
              },
            },
          },
          orderBy: { position: "asc" },
        },
      },
    });

    res.json(businessIdea);
  } catch (error) {
    console.error("Error fetching business idea:", error);
    next(createError(500, "Failed to fetch business idea"));
  }
};

// Create or update business idea
export const saveBusinessIdea = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { companyId } = req.params;
    const { content, isPublished } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      return next(createError(401, "User not authenticated"));
    }

    // Check if user has access to this company
    const userCompany = await prisma.userCompany.findFirst({
      where: {
        userId: userId,
        companyId: parseInt(companyId),
      },
    });

    if (!userCompany) {
      return next(createError(403, "Access denied to this company"));
    }

    const businessIdea = await prisma.businessIdea.upsert({
      where: { companyId: parseInt(companyId) },
      update: {
        content,
        isPublished: isPublished ?? false,
        updatedBy: userId,
      },
      create: {
        companyId: parseInt(companyId),
        content,
        isPublished: isPublished ?? false,
        createdBy: userId,
      },
      include: {
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true,
          },
        },
        updater: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true,
          },
        },
      },
    });

    // Log activity
    await ActivityLogger.logFromRequest(req, {
      type: "DATA_OPS",
      action:
        businessIdea.createdAt === businessIdea.updatedAt ? "CREATE" : "UPDATE",
      module: "business",
      entity: "BusinessIdea",
      entityId: businessIdea.id.toString(),
      title:
        businessIdea.createdAt === businessIdea.updatedAt
          ? "Business Idea Created"
          : "Business Idea Updated",
      description:
        businessIdea.createdAt === businessIdea.updatedAt
          ? "Created business idea"
          : "Updated business idea",
      userId,
      status: "SUCCESS",
      severity: "INFO",
      metadata: {
        companyId: parseInt(companyId),
        contentLength: content?.length || 0,
        isPublished,
      },
    });

    res.json(businessIdea);
  } catch (error) {
    console.error("Error saving business idea:", error);
    next(createError(500, "Failed to save business idea"));
  }
};

// Add collaborator to business idea
export const addCollaborator = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { companyId } = req.params;
    const { userId: collaboratorUserId } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      return next(createError(401, "User not authenticated"));
    }

    // Check if user has access to this company
    const userCompany = await prisma.userCompany.findFirst({
      where: {
        userId: userId,
        companyId: parseInt(companyId),
      },
    });

    if (!userCompany) {
      return next(createError(403, "Access denied to this company"));
    }

    // Check if collaborator is part of the company
    const collaboratorCompany = await prisma.userCompany.findFirst({
      where: {
        userId: collaboratorUserId,
        companyId: parseInt(companyId),
      },
    });

    if (!collaboratorCompany) {
      return next(createError(400, "User is not part of this company"));
    }

    // Ensure business idea exists
    let businessIdea = await prisma.businessIdea.findUnique({
      where: { companyId: parseInt(companyId) },
    });

    if (!businessIdea) {
      businessIdea = await prisma.businessIdea.create({
        data: {
          companyId: parseInt(companyId),
          createdBy: userId,
        },
      });
    }

    const collaborator = await prisma.businessIdeaCollaborator.create({
      data: {
        businessIdeaId: businessIdea.id,
        userId: collaboratorUserId,
        addedBy: userId,
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true,
          },
        },
      },
    });

    // Log activity
    await ActivityLogger.logFromRequest(req, {
      type: "DATA_OPS",
      action: "CREATE",
      module: "business",
      entity: "BusinessIdeaCollaborator",
      entityId: collaborator.id.toString(),
      title: "Collaborator Added",
      description: `Added collaborator ${collaborator.user.firstName} ${collaborator.user.lastName}`,
      userId,
      status: "SUCCESS",
      severity: "INFO",
      metadata: {
        companyId: parseInt(companyId),
        collaboratorId: collaboratorUserId,
      },
    });

    res.json(collaborator);
  } catch (error) {
    console.error("Error adding collaborator:", error);
    if (error.code === "P2002") {
      return next(createError(400, "User is already a collaborator"));
    }
    next(createError(500, "Failed to add collaborator"));
  }
};

// Remove collaborator from business idea
export const removeCollaborator = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { companyId, collaboratorId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      return next(createError(401, "User not authenticated"));
    }

    // Check if user has access to this company
    const userCompany = await prisma.userCompany.findFirst({
      where: {
        userId: userId,
        companyId: parseInt(companyId),
      },
    });

    if (!userCompany) {
      return next(createError(403, "Access denied to this company"));
    }

    const collaborator = await prisma.businessIdeaCollaborator.findUnique({
      where: { id: parseInt(collaboratorId) },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    if (!collaborator) {
      return next(createError(404, "Collaborator not found"));
    }

    await prisma.businessIdeaCollaborator.delete({
      where: { id: parseInt(collaboratorId) },
    });

    // Log activity
    await ActivityLogger.logFromRequest(req, {
      type: "DATA_OPS",
      action: "DELETE",
      module: "business",
      entity: "BusinessIdeaCollaborator",
      entityId: collaboratorId,
      title: "Collaborator Removed",
      description: `Removed collaborator ${collaborator.user.firstName} ${collaborator.user.lastName}`,
      userId,
      status: "SUCCESS",
      severity: "INFO",
      metadata: {
        companyId: parseInt(companyId),
        collaboratorUserId: collaborator.userId,
      },
    });

    res.json({ message: "Collaborator removed successfully" });
  } catch (error) {
    console.error("Error removing collaborator:", error);
    next(createError(500, "Failed to remove collaborator"));
  }
};

// Create or update a note
export const saveNote = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { companyId } = req.params;
    const { id, content, position } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      return next(createError(401, "User not authenticated"));
    }

    // Check if user has access to this company
    const userCompany = await prisma.userCompany.findFirst({
      where: {
        userId: userId,
        companyId: parseInt(companyId),
      },
    });

    if (!userCompany) {
      return next(createError(403, "Access denied to this company"));
    }

    // Ensure business idea exists
    let businessIdea = await prisma.businessIdea.findUnique({
      where: { companyId: parseInt(companyId) },
    });

    if (!businessIdea) {
      businessIdea = await prisma.businessIdea.create({
        data: {
          companyId: parseInt(companyId),
          createdBy: userId,
        },
      });
    }

    let note;
    if (id) {
      // Update existing note
      note = await prisma.businessIdeaNote.update({
        where: { id: parseInt(id) },
        data: {
          content,
          position: position ?? 0,
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              avatar: true,
            },
          },
        },
      });
    } else {
      // Create new note
      note = await prisma.businessIdeaNote.create({
        data: {
          businessIdeaId: businessIdea.id,
          userId,
          content,
          position: position ?? 0,
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              avatar: true,
            },
          },
        },
      });
    }

    res.json(note);
  } catch (error) {
    console.error("Error saving note:", error);
    next(createError(500, "Failed to save note"));
  }
};

// Delete a note
export const deleteNote = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { companyId, noteId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      return next(createError(401, "User not authenticated"));
    }

    // Check if user has access to this company
    const userCompany = await prisma.userCompany.findFirst({
      where: {
        userId: userId,
        companyId: parseInt(companyId),
      },
    });

    if (!userCompany) {
      return next(createError(403, "Access denied to this company"));
    }

    const note = await prisma.businessIdeaNote.findUnique({
      where: { id: parseInt(noteId) },
    });

    if (!note) {
      return next(createError(404, "Note not found"));
    }

    // Only allow the note creator to delete it
    if (note.userId !== userId) {
      return next(createError(403, "You can only delete your own notes"));
    }

    await prisma.businessIdeaNote.delete({
      where: { id: parseInt(noteId) },
    });

    res.json({ message: "Note deleted successfully" });
  } catch (error) {
    console.error("Error deleting note:", error);
    next(createError(500, "Failed to delete note"));
  }
};

// Get learning materials
export const getLearningMaterials = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const materials = await prisma.learningMaterial.findMany({
      where: { isActive: true },
      orderBy: { order: "asc" },
      include: {
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
        updater: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    res.json(materials);
  } catch (error) {
    console.error("Error fetching learning materials:", error);
    next(createError(500, "Failed to fetch learning materials"));
  }
};

// Update learning material (ADMIN/SUPERADMIN only)
export const updateLearningMaterial = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { materialId } = req.params;
    const { title, content, videoUrls } = req.body;
    const userId = req.user?.id;
    const userRoles = req.user?.roles || [];

    if (!userId) {
      return next(createError(401, "User not authenticated"));
    }

    // Check if user is ADMIN or SUPERADMIN
    // First check roles from JWT token
    const hasRoleAccess = userRoles.some(
      (role) => role.role === "ADMIN" || role.role === "SUPERADMIN"
    );

    // Also check if user is SUPERADMIN by ID or database property
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, isSuperAdmin: true },
    });

    const isSuperAdmin = user?.isSuperAdmin || userId === 4 || userId === 6; // Allow user 6 for testing
    const hasAdminAccess = hasRoleAccess || isSuperAdmin;

    console.log("Learning material admin access check:", {
      hasRoleAccess,
      isSuperAdmin,
      hasAdminAccess,
      userId,
      userRoles,
    });

    if (!hasAdminAccess) {
      return next(createError(403, "Admin access required"));
    }

    const material = await prisma.learningMaterial.update({
      where: { id: parseInt(materialId) },
      data: {
        title,
        content,
        videoUrls: videoUrls || [],
        updatedBy: userId,
      },
      include: {
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
        updater: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    // Log activity
    await ActivityLogger.logFromRequest(req, {
      type: "DATA_OPS",
      action: "UPDATE",
      module: "business",
      entity: "LearningMaterial",
      entityId: materialId,
      title: "Learning Material Updated",
      description: `Updated learning material: ${title}`,
      userId,
      status: "SUCCESS",
      severity: "INFO",
      metadata: {
        materialKey: material.key,
        videoCount: videoUrls?.length || 0,
      },
    });

    res.json(material);
  } catch (error) {
    console.error("Error updating learning material:", error);
    next(createError(500, "Failed to update learning material"));
  }
};

// Get greeting
export const getGreeting = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const greeting = await prisma.businessIdeaGreeting.findFirst({
      where: { isActive: true },
      include: {
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
        updater: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    res.json(greeting);
  } catch (error) {
    console.error("Error fetching greeting:", error);
    next(createError(500, "Failed to fetch greeting"));
  }
};

// Update greeting (ADMIN/SUPERADMIN only)
export const updateGreeting = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { title, content } = req.body;
    const userId = req.user?.id;
    const userRoles = req.user?.roles || [];

    if (!userId) {
      return next(createError(401, "User not authenticated"));
    }

    // Check if user is ADMIN or SUPERADMIN
    // First check roles from JWT token
    const hasRoleAccess = userRoles.some(
      (role) => role.role === "ADMIN" || role.role === "SUPERADMIN"
    );

    // Also check if user is SUPERADMIN by ID or database property
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, isSuperAdmin: true },
    });

    const isSuperAdmin = user?.isSuperAdmin || userId === 4 || userId === 6; // Allow user 6 for testing
    const hasAdminAccess = hasRoleAccess || isSuperAdmin;

    console.log("Admin access check:", {
      hasRoleAccess,
      isSuperAdmin,
      hasAdminAccess,
      userId,
      userRoles,
    });

    if (!hasAdminAccess) {
      return next(createError(403, "Admin access required"));
    }

    // First, deactivate all existing greetings
    await prisma.businessIdeaGreeting.updateMany({
      where: { isActive: true },
      data: { isActive: false },
    });

    // Create new greeting
    const greeting = await prisma.businessIdeaGreeting.create({
      data: {
        title,
        content,
        createdBy: userId,
        isActive: true,
      },
      include: {
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    // Log activity
    await ActivityLogger.logFromRequest(req, {
      type: "DATA_OPS",
      action: "UPDATE",
      module: "business",
      entity: "BusinessIdeaGreeting",
      entityId: greeting.id.toString(),
      title: "Greeting Updated",
      description: "Updated business idea greeting",
      userId,
      status: "SUCCESS",
      severity: "INFO",
      metadata: {
        title,
      },
    });

    res.json(greeting);
  } catch (error) {
    console.error("Error updating greeting:", error);
    next(createError(500, "Failed to update greeting"));
  }
};

// Get company team members for collaborator selection
export const getCompanyTeamMembers = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { companyId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      return next(createError(401, "User not authenticated"));
    }

    // Check if user has access to this company
    const userCompany = await prisma.userCompany.findFirst({
      where: {
        userId: userId,
        companyId: parseInt(companyId),
      },
    });

    if (!userCompany) {
      return next(createError(403, "Access denied to this company"));
    }

    const teamMembers = await prisma.userCompany.findMany({
      where: {
        companyId: parseInt(companyId),
        status: "ACTIVE",
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatar: true,
          },
        },
      },
    });

    res.json(teamMembers.map((member) => member.user));
  } catch (error) {
    console.error("Error fetching team members:", error);
    next(createError(500, "Failed to fetch team members"));
  }
};
