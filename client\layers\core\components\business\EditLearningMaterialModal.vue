<script setup lang="ts">
import { ref, watch } from 'vue'
import { useNuiToasts } from '#imports'

interface LearningMaterial {
  id: number
  key: string
  title: string
  content: string
  videoUrls: string[]
  isActive: boolean
  order: number
}

interface Props {
  isOpen: boolean
  material: LearningMaterial | null
}

interface Emits {
  (e: 'close'): void
  (e: 'saved', material: LearningMaterial): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Composables
const { add: addToast } = useNuiToasts()

// State
const loading = ref(false)
const formData = ref({
  title: '',
  content: '',
  videoUrls: ['']
})

// Watch for material changes
watch(() => props.material, (material) => {
  if (material) {
    formData.value = {
      title: material.title,
      content: material.content,
      videoUrls: material.videoUrls.length > 0 ? [...material.videoUrls] : ['']
    }
  }
}, { immediate: true })

// Methods
const addVideoUrl = () => {
  if (formData.value.videoUrls.length < 3) {
    formData.value.videoUrls.push('')
  }
}

const removeVideoUrl = (index: number) => {
  if (formData.value.videoUrls.length > 1) {
    formData.value.videoUrls.splice(index, 1)
  }
}

const convertToEmbedUrl = (url: string) => {
  if (!url) return ''
  
  // Convert YouTube watch URLs to embed URLs
  const youtubeRegex = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/
  const match = url.match(youtubeRegex)
  
  if (match) {
    return `https://www.youtube.com/embed/${match[1]}`
  }
  
  return url
}

const saveMaterial = async () => {
  if (!props.material) return

  loading.value = true
  try {
    // Filter out empty video URLs and convert to embed format
    const videoUrls = formData.value.videoUrls
      .filter(url => url.trim())
      .map(url => convertToEmbedUrl(url.trim()))

    const response = await $fetch(`/api/v1/business-idea/learning-materials/${props.material.id}`, {
      method: 'PUT',
      body: {
        title: formData.value.title,
        content: formData.value.content,
        videoUrls
      }
    })

    emit('saved', response)
    emit('close')

    addToast({
      title: 'Success',
      message: 'Learning material updated successfully',
      color: 'success',
      icon: 'ph:check-duotone'
    })
  } catch (error) {
    console.error('Error saving learning material:', error)
    addToast({
      title: 'Error',
      message: 'Failed to save learning material',
      color: 'danger',
      icon: 'ph:warning-duotone'
    })
  } finally {
    loading.value = false
  }
}

const closeModal = () => {
  emit('close')
}
</script>

<template>
  <TairoModal
    :open="isOpen"
    size="lg"
    @close="closeModal"
  >
    <template #header>
      <div class="flex w-full items-center justify-between p-4 md:p-6">
        <h3 class="font-heading text-muted-900 text-lg font-medium leading-6 dark:text-white">
          Edit Learning Material
        </h3>
        <BaseButtonClose @click="closeModal" />
      </div>
    </template>

    <div class="p-4 md:p-6">
      <div class="mx-auto w-full max-w-2xl space-y-6">
        <!-- Title -->
        <BaseField label="Title" required>
          <BaseInput
            v-model="formData.title"
            placeholder="Enter material title"
          />
        </BaseField>

        <!-- Content -->
        <BaseField label="Content" required>
          <RichTextEditor
            v-model="formData.content"
            placeholder="Enter the learning material content..."
            :min-height="300"
          />
        </BaseField>

        <!-- Video URLs -->
        <BaseField label="YouTube Video URLs (Optional)">
          <div class="space-y-3">
            <div
              v-for="(url, index) in formData.videoUrls"
              :key="index"
              class="flex gap-2"
            >
              <BaseInput
                v-model="formData.videoUrls[index]"
                placeholder="https://www.youtube.com/watch?v=..."
                class="flex-1"
              />
              <BaseButton
                v-if="formData.videoUrls.length > 1"
                size="icon-sm"
                variant="ghost"
                color="danger"
                @click="removeVideoUrl(index)"
              >
                <Icon name="ph:trash-duotone" class="h-4 w-4" />
              </BaseButton>
            </div>
            
            <BaseButton
              v-if="formData.videoUrls.length < 3"
              size="sm"
              variant="outline"
              color="muted"
              @click="addVideoUrl"
            >
              <Icon name="ph:plus-duotone" class="h-4 w-4 mr-2" />
              Add Video URL
            </BaseButton>
          </div>
          <BaseText size="xs" class="text-muted-500 mt-1">
            You can add up to 3 YouTube videos. Paste the full YouTube URL and we'll convert it automatically.
          </BaseText>
        </BaseField>

        <!-- Action Buttons -->
        <div class="flex gap-x-2 pt-4">
          <BaseButton
            variant="primary"
            class="flex-1"
            :disabled="!formData.title.trim() || !formData.content.trim() || loading"
            :loading="loading"
            @click="saveMaterial"
          >
            Save Changes
          </BaseButton>
          <BaseButton
            variant="outline"
            color="muted"
            class="flex-1"
            @click="closeModal"
            :disabled="loading"
          >
            Cancel
          </BaseButton>
        </div>
      </div>
    </div>
  </TairoModal>
</template>
