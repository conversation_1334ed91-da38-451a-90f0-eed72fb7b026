{"version": 3, "file": "PasteRule.d.ts", "sourceRoot": "", "sources": ["../src/PasteRule.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAA;AAGtD,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAA;AAGpC,OAAO,EACL,WAAW,EACX,eAAe,EACf,wBAAwB,EACxB,KAAK,EACL,cAAc,EACf,MAAM,YAAY,CAAA;AAInB,MAAM,MAAM,cAAc,GAAG;IAC3B,KAAK,EAAE,MAAM,CAAC;IACd,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,KAAK,CAAC,EAAE,gBAAgB,CAAC;IACzB,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAC5B,CAAC;AAEF,MAAM,MAAM,eAAe,GACvB,MAAM,GACN,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,cAAc,GAAG,IAAI,KAAK,cAAc,EAAE,GAAG,IAAI,GAAG,SAAS,CAAC,CAAC;AAE3F;;;GAGG;AACH,qBAAa,SAAS;IACpB,IAAI,EAAE,eAAe,CAAA;IAErB,OAAO,EAAE,CAAC,KAAK,EAAE;QACf,KAAK,EAAE,WAAW,CAAC;QACnB,KAAK,EAAE,KAAK,CAAC;QACb,KAAK,EAAE,wBAAwB,CAAC;QAChC,QAAQ,EAAE,cAAc,CAAC;QACzB,KAAK,EAAE,MAAM,eAAe,CAAC;QAC7B,GAAG,EAAE,MAAM,WAAW,CAAC;QACvB,UAAU,EAAE,cAAc,GAAG,IAAI,CAAC;QAClC,SAAS,EAAE,SAAS,GAAG,IAAI,CAAC;KAC7B,KAAK,IAAI,GAAG,IAAI,CAAA;gBAEL,MAAM,EAAE;QAClB,IAAI,EAAE,eAAe,CAAC;QACtB,OAAO,EAAE,CAAC,KAAK,EAAE;YACf,GAAG,EAAE,MAAM,WAAW,CAAC;YACvB,KAAK,EAAE,MAAM,eAAe,CAAC;YAC7B,QAAQ,EAAE,cAAc,CAAC;YACzB,SAAS,EAAE,SAAS,GAAG,IAAI,CAAC;YAC5B,KAAK,EAAE,wBAAwB,CAAC;YAChC,UAAU,EAAE,cAAc,GAAG,IAAI,CAAC;YAClC,KAAK,EAAE,KAAK,CAAC;YACb,KAAK,EAAE,WAAW,CAAC;SACpB,KAAK,IAAI,GAAG,IAAI,CAAC;KACnB;CAIF;AAkHD;;;;GAIG;AACH,wBAAgB,gBAAgB,CAAC,KAAK,EAAE;IAAE,MAAM,EAAE,MAAM,CAAC;IAAC,KAAK,EAAE,SAAS,EAAE,CAAA;CAAE,GAAG,MAAM,EAAE,CAuLxF"}