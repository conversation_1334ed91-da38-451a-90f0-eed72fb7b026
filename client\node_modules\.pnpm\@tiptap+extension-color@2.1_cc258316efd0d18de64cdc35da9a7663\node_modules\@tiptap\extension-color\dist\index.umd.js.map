{"version": 3, "file": "index.umd.js", "sources": ["../src/color.ts"], "sourcesContent": ["import '@tiptap/extension-text-style'\n\nimport { Extension } from '@tiptap/core'\n\nexport type ColorOptions = {\n  /**\n   * The types where the color can be applied\n   * @default ['textStyle']\n   * @example ['heading', 'paragraph']\n  */\n  types: string[],\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    color: {\n      /**\n       * Set the text color\n       * @param color The color to set\n       * @example editor.commands.setColor('red')\n       */\n      setColor: (color: string) => ReturnType,\n\n      /**\n       * Unset the text color\n       * @example editor.commands.unsetColor()\n       */\n      unsetColor: () => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to color your text.\n * @see https://tiptap.dev/api/extensions/color\n */\nexport const Color = Extension.create<ColorOptions>({\n  name: 'color',\n\n  addOptions() {\n    return {\n      types: ['textStyle'],\n    }\n  },\n\n  addGlobalAttributes() {\n    return [\n      {\n        types: this.options.types,\n        attributes: {\n          color: {\n            default: null,\n            parseHTML: element => element.style.color?.replace(/['\"]+/g, ''),\n            renderHTML: attributes => {\n              if (!attributes.color) {\n                return {}\n              }\n\n              return {\n                style: `color: ${attributes.color}`,\n              }\n            },\n          },\n        },\n      },\n    ]\n  },\n\n  addCommands() {\n    return {\n      setColor: color => ({ chain }) => {\n        return chain()\n          .setMark('textStyle', { color })\n          .run()\n      },\n      unsetColor: () => ({ chain }) => {\n        return chain()\n          .setMark('textStyle', { color: null })\n          .removeEmptyTextStyle()\n          .run()\n      },\n    }\n  },\n})\n"], "names": ["Extension"], "mappings": ";;;;;;EAgCA;;;EAGG;AACU,QAAA,KAAK,GAAGA,cAAS,CAAC,MAAM,CAAe;EAClD,IAAA,IAAI,EAAE,OAAO;MAEb,UAAU,GAAA;UACR,OAAO;cACL,KAAK,EAAE,CAAC,WAAW,CAAC;WACrB;OACF;MAED,mBAAmB,GAAA;UACjB,OAAO;EACL,YAAA;EACE,gBAAA,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;EACzB,gBAAA,UAAU,EAAE;EACV,oBAAA,KAAK,EAAE;EACL,wBAAA,OAAO,EAAE,IAAI;0BACb,SAAS,EAAE,OAAO,cAAI,OAAA,CAAA,EAAA,GAAA,OAAO,CAAC,KAAK,CAAC,KAAK,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAA,EAAA;0BAChE,UAAU,EAAE,UAAU,IAAG;EACvB,4BAAA,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;EACrB,gCAAA,OAAO,EAAE;;8BAGX,OAAO;EACL,gCAAA,KAAK,EAAE,CAAA,OAAA,EAAU,UAAU,CAAC,KAAK,CAAE,CAAA;+BACpC;2BACF;EACF,qBAAA;EACF,iBAAA;EACF,aAAA;WACF;OACF;MAED,WAAW,GAAA;UACT,OAAO;cACL,QAAQ,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,KAAI;EAC/B,gBAAA,OAAO,KAAK;EACT,qBAAA,OAAO,CAAC,WAAW,EAAE,EAAE,KAAK,EAAE;EAC9B,qBAAA,GAAG,EAAE;eACT;cACD,UAAU,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,KAAI;EAC9B,gBAAA,OAAO,KAAK;uBACT,OAAO,CAAC,WAAW,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;EACpC,qBAAA,oBAAoB;EACpB,qBAAA,GAAG,EAAE;eACT;WACF;OACF;EACF,CAAA;;;;;;;;;;;"}