<template>
  <div class="space-y-4">
    <!-- Language Tabs -->
    <div class="flex items-center gap-2 border-b border-muted-200 dark:border-muted-800">
      <button
        v-for="lang in languages"
        :key="lang.code"
        @click="activeLanguage = lang.code"
        :class="[
          'px-4 py-2 text-sm font-medium border-b-2 transition-colors',
          activeLanguage === lang.code
            ? 'border-primary-500 text-primary-600 dark:text-primary-400'
            : 'border-transparent text-muted-600 dark:text-muted-400 hover:text-muted-800 dark:hover:text-muted-200'
        ]"
      >
        {{ lang.name }}
        <span v-if="hasContent(lang.code)" class="ml-1 text-xs">✓</span>
      </button>
    </div>

    <!-- Content Editor -->
    <div class="space-y-4">
      <!-- Title Field -->
      <BaseField :label="`Title (${getLanguageName(activeLanguage)})`">
        <BaseInput
          :model-value="getCurrentTitle()"
          @update:model-value="updateTitle"
          :placeholder="`Enter title in ${getLanguageName(activeLanguage)}`"
          rounded="md"
        />
      </BaseField>

      <!-- Content Field -->
      <BaseField :label="`Content (${getLanguageName(activeLanguage)})`">
        <TiptapEditor
          :model-value="getCurrentContent()"
          @update:model-value="updateContent"
          :placeholder="`Enter content in ${getLanguageName(activeLanguage)}`"
        />
      </BaseField>

      <!-- AI Translation Button -->
      <div v-if="isAdmin && hasContentInOtherLanguage()" class="flex items-center gap-2">
        <BaseButton
          variant="ghost"
          color="primary"
          size="sm"
          @click="translateContent"
          :disabled="isTranslating"
          class="font-mono"
        >
          <Icon 
            :name="isTranslating ? 'svg-spinners:ring-resize' : 'ph:translate-duotone'" 
            class="h-4 w-4 mr-2" 
          />
          {{ isTranslating ? 'Translating...' : `Translate to ${getOtherLanguageName()}` }}
          <div v-if="!isTranslating" class="ml-2 w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
        </BaseButton>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex gap-2 pt-4 border-t border-muted-200 dark:border-muted-800">
      <BaseButton
        variant="primary"
        @click="save"
        :disabled="!hasRequiredContent() || isSaving"
      >
        <Icon 
          :name="isSaving ? 'svg-spinners:ring-resize' : 'ph:check-duotone'" 
          class="h-4 w-4 mr-2" 
        />
        {{ isSaving ? 'Saving...' : 'Save' }}
      </BaseButton>

      <BaseButton
        variant="ghost"
        color="muted"
        @click="cancel"
      >
        Cancel
      </BaseButton>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Language {
  code: 'en' | 'et';
  name: string;
}

interface MultiLanguageContent {
  titleEn: string;
  titleEt: string;
  contentEn: string;
  contentEt: string;
}

interface Props {
  modelValue: MultiLanguageContent;
  isAdmin?: boolean;
  type: 'greeting' | 'learning-material';
}

interface Emits {
  (e: 'update:modelValue', value: MultiLanguageContent): void;
  (e: 'save', value: MultiLanguageContent): void;
  (e: 'cancel'): void;
}

const props = withDefaults(defineProps<Props>(), {
  isAdmin: false,
});

const emit = defineEmits<Emits>();

// Composables
const { locale } = useI18n();
const api = useApi();

// State
const activeLanguage = ref<'en' | 'et'>(locale.value as 'en' | 'et' || 'en');
const isTranslating = ref(false);
const isSaving = ref(false);

const languages: Language[] = [
  { code: 'en', name: 'English' },
  { code: 'et', name: 'Estonian' },
];

// Local content state
const localContent = ref<MultiLanguageContent>({ ...props.modelValue });

// Watch for prop changes
watch(() => props.modelValue, (newValue) => {
  localContent.value = { ...newValue };
}, { deep: true });

// Methods
const getLanguageName = (code: string): string => {
  return languages.find(lang => lang.code === code)?.name || code;
};

const getOtherLanguageName = (): string => {
  const otherCode = activeLanguage.value === 'en' ? 'et' : 'en';
  return getLanguageName(otherCode);
};

const hasContent = (langCode: 'en' | 'et'): boolean => {
  const titleKey = `title${langCode.charAt(0).toUpperCase() + langCode.slice(1)}` as keyof MultiLanguageContent;
  const contentKey = `content${langCode.charAt(0).toUpperCase() + langCode.slice(1)}` as keyof MultiLanguageContent;
  return !!(localContent.value[titleKey] && localContent.value[contentKey]);
};

const hasContentInOtherLanguage = (): boolean => {
  const otherLang = activeLanguage.value === 'en' ? 'et' : 'en';
  return hasContent(otherLang);
};

const getCurrentTitle = (): string => {
  const key = `title${activeLanguage.value.charAt(0).toUpperCase() + activeLanguage.value.slice(1)}` as keyof MultiLanguageContent;
  return localContent.value[key] || '';
};

const getCurrentContent = (): string => {
  const key = `content${activeLanguage.value.charAt(0).toUpperCase() + activeLanguage.value.slice(1)}` as keyof MultiLanguageContent;
  return localContent.value[key] || '';
};

const updateTitle = (value: string) => {
  const key = `title${activeLanguage.value.charAt(0).toUpperCase() + activeLanguage.value.slice(1)}` as keyof MultiLanguageContent;
  localContent.value[key] = value;
  emit('update:modelValue', localContent.value);
};

const updateContent = (value: string) => {
  const key = `content${activeLanguage.value.charAt(0).toUpperCase() + activeLanguage.value.slice(1)}` as keyof MultiLanguageContent;
  localContent.value[key] = value;
  emit('update:modelValue', localContent.value);
};

const hasRequiredContent = (): boolean => {
  return !!(localContent.value.titleEn && localContent.value.contentEn);
};

const translateContent = async () => {
  if (!props.isAdmin) return;

  const fromLanguage = activeLanguage.value === 'en' ? 'et' : 'en';
  const toLanguage = activeLanguage.value;

  const fromTitleKey = `title${fromLanguage.charAt(0).toUpperCase() + fromLanguage.slice(1)}` as keyof MultiLanguageContent;
  const fromContentKey = `content${fromLanguage.charAt(0).toUpperCase() + fromLanguage.slice(1)}` as keyof MultiLanguageContent;

  const sourceTitle = localContent.value[fromTitleKey];
  const sourceContent = localContent.value[fromContentKey];

  if (!sourceTitle || !sourceContent) return;

  isTranslating.value = true;

  try {
    const endpoint = props.type === 'greeting' 
      ? '/core/business-idea/translate/greeting'
      : '/core/business-idea/translate/learning-material';

    const translation = await api.post(endpoint, {
      title: sourceTitle,
      content: sourceContent,
      fromLanguage,
      toLanguage,
    });

    if (translation) {
      updateTitle(translation.title);
      updateContent(translation.content);
    }
  } catch (error) {
    console.error('Translation error:', error);
    // You could add a toast notification here
  } finally {
    isTranslating.value = false;
  }
};

const save = async () => {
  isSaving.value = true;
  try {
    emit('save', localContent.value);
  } finally {
    isSaving.value = false;
  }
};

const cancel = () => {
  emit('cancel');
};
</script>
