import type { ComponentData } from 'nuxt-component-meta'
export type NuxtComponentMetaNames = 'AccountMenu' | 'AddonApexcharts' | 'AddonInputPassword' | 'AiAssistantButton' | 'AiAssistantWindow' | 'BaseLayerPage' | 'BaseLoader' | 'CalendarEvent' | 'CalendarEventPending' | 'CalendarSidebarCategories' | 'DemoCreditCardReal' | 'DemoPanelActivity' | 'DemoPanelInvest' | 'DemoPanelLanguage' | 'LanguageSelector' | 'LanguageToggle' | 'Logo' | 'LogoText' | 'MapMarker' | 'PasswordInputWithEye' | 'PhoneInput' | 'PwaInstallButton' | 'PwaInstallModal' | 'PwaThemeColor' | 'Sidebar' | 'SidebarBackdrop' | 'SidebarContent' | 'SidebarLayout' | 'SidebarLink' | 'SidebarLinks' | 'SidebarNav' | 'SidebarSubsidebar' | 'SidebarSubsidebarCollapsible' | 'SidebarSubsidebarCollapsibleLink' | 'SidebarSubsidebarCollapsibleTrigger' | 'SidebarSubsidebarContent' | 'SidebarSubsidebarHeader' | 'SidebarSubsidebarLink' | 'SidebarTrigger' | 'SubsidebarChat' | 'SubsidebarMessaging' | 'ThemeColorUpdater' | 'Toolbar' | 'AuthEmailVerification' | 'AuthPasswordInput' | 'AuthPaymentForm' | 'AuthRegistrationForm' | 'AuthRegistrationSuccess' | 'AuthRoleSelection' | 'AuthStripePaymentForm' | 'AuthSubscriptionPlanSelection' | 'CompanyDetailsTab' | 'CompanyDocumentsTab' | 'CompanyHeader' | 'CompanySettingsTab' | 'CompanyTeamTab' | 'CompanyDocumentsDocumentList' | 'CompanyDocumentsDocumentUploader' | 'CompanyTeamConfirmRemoveModal' | 'CompanyTeamEditRoleModal' | 'CompanyTeamInvitationRow' | 'CompanyTeamInvitationsList' | 'CompanyTeamInviteModal' | 'CompanyTeamMemberRow' | 'CompanyTeamMembersList' | 'PaymentBillingAddressForm' | 'PaymentMethodForm' | 'PaymentStripeCardInput' | 'TairoCheckAnimated' | 'TairoCheckboxAnimated' | 'TairoCheckboxCardIcon' | 'TairoContentWrapper' | 'TairoContentWrapperTabbed' | 'TairoError' | 'TairoFlexTable' | 'TairoFlexTableCell' | 'TairoFlexTableHeading' | 'TairoFlexTableRow' | 'TairoFormGroup' | 'TairoFormSave' | 'TairoFullscreenDropfile' | 'TairoImageZoom' | 'TairoInput' | 'TairoInputFileHeadless' | 'TairoMobileDrawer' | 'TairoPanels' | 'TairoRadioCard' | 'TairoSelect' | 'TairoSelectItem' | 'TairoTable' | 'TairoTableCell' | 'TairoTableHeading' | 'TairoTableRow' | 'TairoWelcome' | 'TairoCollapseBackdrop' | 'TairoCollapseCollapsible' | 'TairoCollapseCollapsibleLink' | 'TairoCollapseCollapsibleTrigger' | 'TairoCollapseContent' | 'TairoCollapseLayout' | 'TairoCollapseSidebar' | 'TairoCollapseSidebarClose' | 'TairoCollapseSidebarHeader' | 'TairoCollapseSidebarLink' | 'TairoCollapseSidebarLinks' | 'TairoMenu' | 'TairoMenuContent' | 'TairoMenuIndicator' | 'TairoMenuItem' | 'TairoMenuLink' | 'TairoMenuLinkTab' | 'TairoMenuList' | 'TairoMenuListItems' | 'TairoMenuTrigger' | 'TairoMenuViewport' | 'TairoSidebar' | 'TairoSidebarBackdrop' | 'TairoSidebarContent' | 'TairoSidebarLayout' | 'TairoSidebarLink' | 'TairoSidebarLinks' | 'TairoSidebarNav' | 'TairoSidebarSubsidebar' | 'TairoSidebarSubsidebarCollapsible' | 'TairoSidebarSubsidebarCollapsibleLink' | 'TairoSidebarSubsidebarCollapsibleTrigger' | 'TairoSidebarSubsidebarContent' | 'TairoSidebarSubsidebarHeader' | 'TairoSidebarSubsidebarLink' | 'TairoSidebarTrigger' | 'TairoSidenavBackdrop' | 'TairoSidenavCollapsible' | 'TairoSidenavCollapsibleLink' | 'TairoSidenavCollapsibleTrigger' | 'TairoSidenavContent' | 'TairoSidenavLayout' | 'TairoSidenavSidebar' | 'TairoSidenavSidebarDivider' | 'TairoSidenavSidebarHeader' | 'TairoSidenavSidebarLink' | 'TairoSidenavSidebarLinks' | 'TairoTopnavContent' | 'TairoTopnavHeader' | 'TairoTopnavLayout' | 'TairoTopnavNavbar' | 'BusinessRuleModal' | 'CoreDashboard' | 'TiptapEditor' | 'ActivityLogDetails' | 'AiDecisionCard' | 'AiDecisionDetailModal' | 'ApiEndpointCard' | 'ApiEndpointDetailModal' | 'AutomationRuleCard' | 'BrandAssetCard' | 'BusinessCollaboratorSelector' | 'BusinessEditGreetingModal' | 'BusinessEditLearningMaterialModal' | 'BusinessMultiLanguageEditor' | 'BusinessPostItNotes' | 'BusinessRichTextEditor' | 'ChartsBarChart' | 'ChartsLineChart' | 'ChartsPieChart' | 'ChartsRadialChart' | 'DashboardChartDashboardCalendar' | 'DashboardChartBudget' | 'DashboardChartGoal' | 'DashboardChartProduction' | 'DashboardChartRevenue' | 'DashboardChartSales' | 'DashboardChartDashboardCheckbox' | 'DashboardActivityCard' | 'DashboardBudgetAllocation' | 'DashboardEmployeeActivity' | 'DashboardMetricCard' | 'DashboardModuleOverview' | 'DashboardModuleUsageCard' | 'DashboardNotificationsCard' | 'DashboardProjectStatus' | 'DashboardRecentActivities' | 'DashboardSalesPerformance' | 'DashboardStatsOverview' | 'DashboardSystemHealth' | 'DashboardSystemHealthCard' | 'DashboardUpcomingEvents' | 'DashboardUserActivityCard' | 'DatabaseConnectionBuilderForm' | 'DatabaseConnectionPoolMonitor' | 'DatabaseConnectionStringForm' | 'DatabaseExplorer' | 'DatabaseHealth' | 'DatabaseSchema' | 'DatabaseTerminalWindow' | 'DocumentsDocumentCard' | 'DocumentsDocumentFilters' | 'DocumentsDocumentUploadModal' | 'EventsEventCard' | 'EventsEventDetailModal' | 'ExamplesComponentAccessExample' | 'HealthPerformanceMetricsCard' | 'HealthResourceUsageCard' | 'HealthServiceStatusCard' | 'LegalDocumentCard' | 'MlDatasetCard' | 'MlTrainingDataUploader' | 'MlTrainingFormatGuide' | 'ModulesModuleCard' | 'ModulesModuleDetailModal' | 'MonitoringAlertDetailsModal' | 'MonitoringAlertsListCard' | 'MonitoringAlertsSummaryCard' | 'MonitoringApiPerformanceCard' | 'MonitoringDatabaseResourcesCard' | 'MonitoringErrorDetailsModal' | 'MonitoringErrorLogsCard' | 'MonitoringErrorSummaryCard' | 'MonitoringPageLoadTimesCard' | 'MonitoringPerformanceMetricsCard' | 'MonitoringResourceOverviewCard' | 'MonitoringServerResourcesCard' | 'PoliciesPolicyCard' | 'PoliciesPolicyCategoryFilter' | 'PoliciesPolicyDetailModal' | 'RulesCategoryCard' | 'RulesRuleCard' | 'RulesRuleDetailModal' | 'RulesRuleFilters' | 'RulesTemplateCard' | 'RulesTemplateDetailModal' | 'RulesValidationTester' | 'SecurityAuditLogEntry' | 'SecurityAuditLogFilters' | 'SecurityEditRoleModal' | 'SecurityEditUserRolesModal' | 'SecurityRolePermissionsCard' | 'SecurityUserRolesTable' | 'SettingsAppearanceSettingsCard' | 'SettingsBackupHistoryCard' | 'SettingsBackupSettingsCard' | 'SettingsBackupStatusCard' | 'SettingsDeleteConfirmationModal' | 'SettingsEnvironmentInfoCard' | 'SettingsEnvironmentVariablesCard' | 'SettingsFormatSettingsCard' | 'SettingsGeneralSettingsCard' | 'SettingsLanguageSettingsCard' | 'SettingsPerformanceSettingsCard' | 'SettingsQuickActionsCard' | 'SettingsRestoreConfirmationModal' | 'SettingsRestoreFromFileModal' | 'SettingsRestoreSettingsCard' | 'UiStandardDialog' | 'WebhooksWebhookCard' | 'WebhooksWebhookDetailModal' | 'BudgetChartsSection' | 'BudgetDashboard' | 'BudgetIncomeForm' | 'DatePicker' | 'DemoAccountMenu' | 'DemoCalendarEvent' | 'DemoCalendarEventPending' | 'ExpensesSection' | 'GlobalFooter' | 'GlobalHeader' | 'IncomeSection' | 'TairoLogo' | 'ChartsBudgetChartsSection' | 'DashboardBudgetGoalsCard' | 'DashboardBudgetSummaryCard' | 'DashboardBudgetTrendsChart' | 'DashboardBudgetTypeSelector' | 'DashboardCategoryBreakdownCard' | 'DashboardRecentTransactionsCard' | 'ExpensesMultipleExpenses' | 'ExpensesOneTimeExpenses' | 'ExpensesRepeatedExpenses' | 'ForecastingCategories' | 'ForecastingChart' | 'ForecastingControls' | 'ForecastingScenarios' | 'ForecastingSummary' | 'IncomesMultipleIncomes' | 'IncomesOneTimeIncomes' | 'IncomesRepeatedIncomes' | 'PlannerBudgetComparisonCard' | 'PlannerMonthlyBudgetCard' | 'QuickBudgetCategoryBreakdown' | 'QuickBudgetEntryForm' | 'QuickBudgetExpensesList' | 'QuickBudgetGoals' | 'QuickBudgetIncomeList' | 'QuickBudgetSummaryCard' | 'QuickBudgetTransactionsList' | 'StatsSection' | 'TransactionsListCard' | 'EmployeeFilters' | 'EmployeeForm' | 'EmployeeList' | 'HrDashboard' | 'WorkerForm' | 'AttendanceRecordsTab' | 'AttendanceReportsTab' | 'AttendanceDailyAttendanceTab' | 'BenefitsBenefitPlansTab' | 'BenefitsBenefitSettingsTab' | 'BenefitsEmployeeBenefitsTab' | 'BonusesAllBonusesTab' | 'BonusesBonusReportsTab' | 'BonusesEmployeeBonusesTab' | 'CareerPathDetails' | 'CareerPathFormModal' | 'CareerPathOverview' | 'DepartmentsDepartmentCard' | 'DocumentsCompanyPoliciesTab' | 'DocumentsTab' | 'DocumentsEmployeeDocumentsTab' | 'EmployeesDocumentUploadForm' | 'EmployeesEmployeeDocuments' | 'EmployeesEmployeeLeave' | 'EmployeesEmployeeOverview' | 'EmployeesEmployeePayroll' | 'EmployeesEmployeePerformance' | 'EmployeesEmployeeProfileCard' | 'LeaveBalancesTab' | 'LeaveCalendarTab' | 'LeaveRequestsTab' | 'LeaveSettingsTab' | 'PayrollRunTab' | 'PayrollPayslipsTab' | 'PayrollTaxSettingsTab' | 'PerformanceEmployeePerformanceTab' | 'PerformanceAnalyticsTab' | 'PerformanceReviewsTab' | 'ShiftsShiftAssignmentsTab' | 'ShiftsShiftScheduleTab' | 'ShiftsShiftTypesTab' | 'TrainingCertificationsTab' | 'TrainingEmployeeTrainingTab' | 'TrainingEnrollEmployeesModal' | 'TrainingDetailsModal' | 'TrainingFormModal' | 'TrainingProgramCard' | 'TrainingProgramsTab' | 'CompaniesDashboard' | 'CompanyDashboard' | 'CompanyForm' | 'ClientsClientCard' | 'ClientsTable' | 'ComplianceAudits' | 'ComplianceOverview' | 'ComplianceRequirements' | 'RiskMitigationPlans' | 'RiskDashboard' | 'RiskRegister' | 'SettingsGeneralSettings' | 'SettingsIntegrationSettings' | 'SettingsNotificationSettings' | 'SettingsPermissionSettings' | 'SuppliersSupplierCard' | 'SuppliersSupplierDocuments' | 'SuppliersSupplierOrders' | 'SuppliersSupplierOverview' | 'SuppliersSupplierPerformance' | 'SuppliersSupplierPerformanceMetrics' | 'SuppliersSupplierTransactions' | 'SuppliersTable' | 'WorkforceCapacityPlanning' | 'WorkforceSkillsGapAnalysis' | 'WorkforceOverview' | 'ProductionDashboard' | 'DashboardTaskStatusChart' | 'DocumentsUploadDocumentModal' | 'DocumentsViewDocumentModal' | 'QualityChecklistCard' | 'QualityChecklistDetailModal' | 'QualityChecklistItem' | 'QualityNewChecklistModal' | 'QualityPhotoCard' | 'QualityPhotoDetailModal' | 'QualityUploadPhotoModal' | 'ReportsBarChart' | 'ReportsLineChart' | 'ReportsMetricCard' | 'ReportsPieChart' | 'ReportsReportCard' | 'ResourcesEquipmentCard' | 'ResourcesEquipmentDetailModal' | 'ResourcesMaterialCard' | 'ResourcesMaterialDetailModal' | 'ResourcesNewEquipmentModal' | 'ResourcesNewMaterialModal' | 'ResourcesNewWorkerModal' | 'ResourcesWorkforceCard' | 'ResourcesWorkforceDetailModal' | 'StatisticsProjectMetricsChart' | 'StatisticsProjectStatusChart' | 'StatisticsTaskCompletionChart' | 'TasksNewTaskModal' | 'TasksTaskBoard' | 'TasksTaskCard' | 'TasksTaskColumn' | 'TasksTaskDetailModal' | 'TimelineGanttChart' | 'AccountingDashboard' | 'JournalEntryForm' | 'RecruitmentSidebarItem' | 'AdminLanguageManager' | 'AdminTranslationsManager' | 'FormsFormBuilderFieldEditor' | 'FormsFormBuilderFieldList' | 'FormsFormBuilderPreview' | 'FormsFormBuilderPreviewField' | 'FormsFormBuilderSettings' | 'FormsFormBuilderSidebar' | 'FormsFormBuilderStepEditor' | 'FormsPublicFormField' | 'WorkforceFormAdditionalInfoStep' | 'WorkforceFormConfirmationStep' | 'WorkforceFormIntroductionStep' | 'WorkforceFormLanguageSelector' | 'WorkforceFormPersonalInfoStep' | 'WorkforceFormProfessionalExperienceStep' | 'WorkforceFormQualificationsStep' | 'WorkforceFormReferencesStep' | 'WorkforceFormSpecialtiesStep' | 'WorkforceAssignmentDetailsModal' | 'WorkforceAssignmentFilters' | 'WorkforceAssignmentsPagination' | 'WorkforceJobDetailsModal' | 'WorkforceJobFilters' | 'WorkforceJobFormModal' | 'WorkforceJobsPagination' | 'WorkforceMatchDetailsModal' | 'WorkforceMatchFilters' | 'WorkforceMatchesPagination' | 'WorkforceWorkerDetailsModal' | 'WorkforceWorkerFormModal' | 'WorkforceWorkersPagination' | 'ActivitiesActivityFormModal' | 'ActivitiesActivityViewModal' | 'AnalyticsDateRangeFilter' | 'AnalyticsForecastScenariosChart' | 'AnalyticsForecastSummaryCard' | 'AnalyticsGoalTrackingChart' | 'AnalyticsKpiCard' | 'AnalyticsReportCategoriesList' | 'AnalyticsReportFormModal' | 'AnalyticsReportViewModal' | 'AnalyticsReportsList' | 'AnalyticsRevenueForecastChart' | 'AnalyticsRevenueTrendChart' | 'AnalyticsSalesByCategoryChart' | 'AnalyticsSalesByRepresentativeChart' | 'AnalyticsSalesPipelineChart' | 'AnalyticsScheduleReportModal' | 'AnalyticsWinProbabilityChart' | 'CampaignsAddPaymentMethodModal' | 'CampaignsCampaignCard' | 'CampaignsCampaignCreateModal' | 'CampaignsCampaignEditModal' | 'CampaignsCampaignViewModal' | 'CampaignsEmailTemplateFormModal' | 'CampaignsEmailTemplateViewModal' | 'CampaignsStepsCampaignAiGenerationStep' | 'CampaignsStepsCampaignBriefStep' | 'CampaignsStepsCampaignDetailsStep' | 'CampaignsStepsCampaignPaymentStep' | 'CampaignsStepsCampaignReviewStep' | 'CommonSalesQuickActionButton' | 'DashboardSalesPipelineChart' | 'DealsDealProductsTable' | 'ModalsDealQuickCreateModal' | 'ModalsLeadConvertModal' | 'ModalsLeadQuickCreateModal' | 'ModalsMeetingQuickCreateModal' | 'ProductsProductFormModal' | 'ProductsProductViewModal' | 'QuotationsQuotationEmptyState' | 'QuotationsQuotationFilters' | 'QuotationsQuotationFormModal' | 'QuotationsQuotationList' | 'QuotationsQuotationViewModal' | 'QuotationsQuoteTemplateFormModal' | 'QuotationsQuoteTemplateViewModal' | 'SettingsApiKeyItem' | 'SettingsGoalCard' | 'SettingsGoalFormModal' | 'SettingsIntegrationCard' | 'SettingsIntegrationConfigModal' | 'SettingsStageFormModal' | 'SettingsWorkflowCard' | 'SettingsWorkflowFormModal' | 'SettingsWorkflowLogsModal' | 'BasePagination' | 'AnalyticsBillableHoursChart' | 'AnalyticsProjectDistributionChart' | 'AnalyticsTeamProductivityChart' | 'AnalyticsTimeTrendsChart' | 'AssignmentsAssignmentDetailsModal' | 'AssignmentsCreateAssignmentModal' | 'AssignmentsEditAssignmentModal' | 'CalendarAddEventModal' | 'CalendarControls' | 'CalendarDayView' | 'CalendarEventDetailsModal' | 'CalendarMonthView' | 'CalendarTeamView' | 'CalendarWeekView' | 'ClientReportsClientReportDetailModal' | 'ClientReportsClientReportFilters' | 'ClientReportsCreateClientReportModal' | 'DashboardActivityItem' | 'DashboardHoursByProjectChart' | 'DashboardHoursByTeamMemberChart' | 'DashboardSummaryCard' | 'EntriesTimeEntryFormModal' | 'LocationsLocationAlertItem' | 'LocationsWorkLocationItem' | 'LocationsWorkerLocationItem' | 'MapComponent' | 'SettingsLocationFormModal' | 'SettingsLocationPickerMap' | 'SettingsMapComponent' | 'SettingsWorkLocationSettingsItem' | 'SettingsApprovalsApprovalSettings' | 'SettingsApprovalsDeleteWorkflowModal' | 'SettingsApprovalsWorkflowFormModal' | 'SettingsApprovalsWorkflowList' | 'SettingsIntegrationsIntegrationCard' | 'SettingsIntegrationsIntegrationConfigModal' | 'SettingsIntegrationsSyncHistoryList' | 'TeamMemberDetails' | 'TeamMemberItem' | 'TimesheetsCreateTimesheetModal' | 'TimesheetsTimesheetDetailModal' | 'TimesheetsTimesheetFilters' | 'TimesheetsTimesheetList' | 'TimesheetsTimesheetSummary' | 'WorkerAssignmentItem' | 'WorkerCurrentAssignmentCard' | 'WorkerMapComponent' | 'WorkerProjectSelectionModal' | 'WorkerTimeEntryItem' | 'WorkerTimeTrackingButton' | 'WorkerTimeTrackingModal' | 'WorkerWeeklyHoursChart' | 'EndpointsEndpointDetailsModal' | 'EndpointsEndpointFieldEditor' | 'ExamplesAddonsDatepicker' | 'ExamplesAddonsMapbox' | 'ExamplesApexchartsBase' | 'ExamplesFlexTableCurved' | 'ExamplesFlexTableRounded' | 'ExamplesFlexTableSmooth' | 'ExamplesFlexTableStraight' | 'ExamplesInputPasswordBase' | 'ExamplesInputPasswordDisabled' | 'ExamplesInputPasswordLocale' | 'ExamplesInputPasswordUserInput' | 'ExamplesInputPasswordValidation' | 'ExamplesInputPhoneBase' | 'ExamplesInputPhoneCountry' | 'ExamplesInputPhoneDisabled' | 'ExamplesInputPhoneFormat' | 'ExamplesInputPhoneShape' | 'ExamplesInputPhoneSize' | 'ExamplesInputPhoneValidation' | 'ExamplesLightweightChartsBase' | 'ExamplesPanelActivity' | 'ExamplesPanelLanguage' | 'ExamplesPanelSearch' | 'ExamplesPanelTask' | 'ExamplesTableCurved' | 'ExamplesTableMediaCurved' | 'ExamplesTableMediaRounded' | 'ExamplesTableMediaSmooth' | 'ExamplesTableMediaStraight' | 'ExamplesTableRounded' | 'ExamplesTableSmooth' | 'ExamplesTableStraight' | 'ExamplesTairoCheckAnimated' | 'ExamplesTairoCheckboxAnimated' | 'ExamplesTairoCheckboxCardIcon' | 'ExamplesTairoCircularMenu' | 'ExamplesTairoError' | 'ExamplesTairoFormGroup' | 'ExamplesTairoFormSave' | 'ExamplesTairoInput' | 'ExamplesTairoLogo' | 'ExamplesTairoLogotext' | 'ExamplesTairoMenuComplete' | 'ExamplesTairoMenu' | 'ExamplesTairoMobileDrawer' | 'ExamplesTairoRadioCard' | 'ExamplesTairoSelect' | 'ExamplesTairoValidation' | 'ProseA' | 'ProseBlockquote' | 'ProseCode' | 'ProseEm' | 'ProseH1' | 'ProseH2' | 'ProseH3' | 'ProseH4' | 'ProseH5' | 'ProseH6' | 'ProseHr' | 'ProseImg' | 'ProseLi' | 'ProseOl' | 'ProseP' | 'ProsePre' | 'ProseScript' | 'ProseStrong' | 'ProseTable' | 'ProseTbody' | 'ProseTd' | 'ProseTh' | 'ProseThead' | 'ProseTr' | 'ProseUl' | 'BaseAccordion' | 'BaseAccordionItem' | 'BaseAutocomplete' | 'BaseAutocompleteGroup' | 'BaseAutocompleteItem' | 'BaseAutocompleteLabel' | 'BaseAutocompleteSeparator' | 'BaseAvatar' | 'BaseAvatarGroup' | 'BaseBreadcrumb' | 'BaseButton' | 'BaseCard' | 'BaseCheckbox' | 'BaseCheckboxGroup' | 'BaseChip' | 'BaseDropdown' | 'BaseDropdownArrow' | 'BaseDropdownCheckbox' | 'BaseDropdownItem' | 'BaseDropdownLabel' | 'BaseDropdownRadioGroup' | 'BaseDropdownRadioItem' | 'BaseDropdownSeparator' | 'BaseDropdownSub' | 'BaseField' | 'BaseHeading' | 'BaseIconBox' | 'BaseInput' | 'BaseInputFile' | 'BaseInputNumber' | 'BaseKbd' | 'BaseLink' | 'BaseList' | 'BaseListItem' | 'BaseMessage' | 'BasePaginationButtonFirst' | 'BasePaginationButtonLast' | 'BasePaginationButtonNext' | 'BasePaginationButtonPrev' | 'BasePaginationItems' | 'BaseParagraph' | 'BasePlaceholderPage' | 'BasePlaceload' | 'BasePopover' | 'BasePrimitiveField' | 'BasePrimitiveFieldController' | 'BasePrimitiveFieldDescription' | 'BasePrimitiveFieldError' | 'BasePrimitiveFieldErrorIndicator' | 'BasePrimitiveFieldLabel' | 'BasePrimitiveFieldLoadingIndicator' | 'BasePrimitiveFieldRequiredIndicator' | 'BasePrimitiveFieldSuccessIndicator' | 'BaseProgress' | 'BaseProgressCircle' | 'BaseProse' | 'BaseProviders' | 'BaseRadio' | 'BaseRadioGroup' | 'BaseSelect' | 'BaseSelectGroup' | 'BaseSelectItem' | 'BaseSelectLabel' | 'BaseSelectSeparator' | 'BaseSlider' | 'BaseSnack' | 'BaseSwitchBall' | 'BaseSwitchThin' | 'BaseTabs' | 'BaseTabsContent' | 'BaseTabsTrigger' | 'BaseTag' | 'BaseText' | 'BaseTextarea' | 'BaseThemeSwitch' | 'BaseThemeSystem' | 'BaseThemeToggle' | 'BaseToast' | 'BaseToastProvider' | 'BaseTooltip' | 'NuxtWelcome' | 'NuxtLayout' | 'NuxtErrorBoundary' | 'ClientOnly' | 'DevOnly' | 'ServerPlaceholder' | 'NuxtLink' | 'NuxtLoadingIndicator' | 'NuxtRouteAnnouncer' | 'NuxtImg' | 'NuxtPicture' | 'AccordionContent' | 'AccordionHeader' | 'AccordionItem' | 'AccordionRoot' | 'AccordionTrigger' | 'AlertDialogRoot' | 'AlertDialogTrigger' | 'AlertDialogPortal' | 'AlertDialogContent' | 'AlertDialogOverlay' | 'AlertDialogCancel' | 'AlertDialogTitle' | 'AlertDialogDescription' | 'AlertDialogAction' | 'AspectRatio' | 'AvatarRoot' | 'AvatarFallback' | 'AvatarImage' | 'CalendarRoot' | 'CalendarHeader' | 'CalendarHeading' | 'CalendarGrid' | 'CalendarCell' | 'CalendarHeadCell' | 'CalendarNext' | 'CalendarPrev' | 'CalendarGridHead' | 'CalendarGridBody' | 'CalendarGridRow' | 'CalendarCellTrigger' | 'CheckboxGroupRoot' | 'CheckboxRoot' | 'CheckboxIndicator' | 'CollapsibleRoot' | 'CollapsibleTrigger' | 'CollapsibleContent' | 'ComboboxRoot' | 'ComboboxInput' | 'ComboboxAnchor' | 'ComboboxEmpty' | 'ComboboxTrigger' | 'ComboboxCancel' | 'ComboboxGroup' | 'ComboboxLabel' | 'ComboboxContent' | 'ComboboxViewport' | 'ComboboxVirtualizer' | 'ComboboxItem' | 'ComboboxItemIndicator' | 'ComboboxSeparator' | 'ComboboxArrow' | 'ComboboxPortal' | 'ContextMenuRoot' | 'ContextMenuTrigger' | 'ContextMenuPortal' | 'ContextMenuContent' | 'ContextMenuArrow' | 'ContextMenuItem' | 'ContextMenuGroup' | 'ContextMenuSeparator' | 'ContextMenuCheckboxItem' | 'ContextMenuItemIndicator' | 'ContextMenuLabel' | 'ContextMenuRadioGroup' | 'ContextMenuRadioItem' | 'ContextMenuSub' | 'ContextMenuSubContent' | 'ContextMenuSubTrigger' | 'DateFieldRoot' | 'DateFieldInput' | 'DatePickerRoot' | 'DatePickerHeader' | 'DatePickerHeading' | 'DatePickerGrid' | 'DatePickerCell' | 'DatePickerHeadCell' | 'DatePickerNext' | 'DatePickerPrev' | 'DatePickerGridHead' | 'DatePickerGridBody' | 'DatePickerGridRow' | 'DatePickerCellTrigger' | 'DatePickerInput' | 'DatePickerCalendar' | 'DatePickerField' | 'DatePickerAnchor' | 'DatePickerArrow' | 'DatePickerClose' | 'DatePickerTrigger' | 'DatePickerContent' | 'DateRangePickerRoot' | 'DateRangePickerHeader' | 'DateRangePickerHeading' | 'DateRangePickerGrid' | 'DateRangePickerCell' | 'DateRangePickerHeadCell' | 'DateRangePickerNext' | 'DateRangePickerPrev' | 'DateRangePickerGridHead' | 'DateRangePickerGridBody' | 'DateRangePickerGridRow' | 'DateRangePickerCellTrigger' | 'DateRangePickerInput' | 'DateRangePickerCalendar' | 'DateRangePickerField' | 'DateRangePickerAnchor' | 'DateRangePickerArrow' | 'DateRangePickerClose' | 'DateRangePickerTrigger' | 'DateRangePickerContent' | 'DateRangeFieldRoot' | 'DateRangeFieldInput' | 'DialogRoot' | 'DialogTrigger' | 'DialogPortal' | 'DialogContent' | 'DialogOverlay' | 'DialogClose' | 'DialogTitle' | 'DialogDescription' | 'DropdownMenuRoot' | 'DropdownMenuTrigger' | 'DropdownMenuPortal' | 'DropdownMenuContent' | 'DropdownMenuArrow' | 'DropdownMenuItem' | 'DropdownMenuGroup' | 'DropdownMenuSeparator' | 'DropdownMenuCheckboxItem' | 'DropdownMenuItemIndicator' | 'DropdownMenuLabel' | 'DropdownMenuRadioGroup' | 'DropdownMenuRadioItem' | 'DropdownMenuSub' | 'DropdownMenuSubContent' | 'DropdownMenuSubTrigger' | 'EditableRoot' | 'EditableArea' | 'EditableInput' | 'EditablePreview' | 'EditableSubmitTrigger' | 'EditableCancelTrigger' | 'EditableEditTrigger' | 'HoverCardRoot' | 'HoverCardTrigger' | 'HoverCardPortal' | 'HoverCardContent' | 'HoverCardArrow' | 'Label' | 'ListboxRoot' | 'ListboxContent' | 'ListboxFilter' | 'ListboxItem' | 'ListboxItemIndicator' | 'ListboxVirtualizer' | 'ListboxGroup' | 'ListboxGroupLabel' | 'MenubarRoot' | 'MenubarTrigger' | 'MenubarPortal' | 'MenubarContent' | 'MenubarArrow' | 'MenubarItem' | 'MenubarGroup' | 'MenubarSeparator' | 'MenubarCheckboxItem' | 'MenubarItemIndicator' | 'MenubarLabel' | 'MenubarRadioGroup' | 'MenubarRadioItem' | 'MenubarSub' | 'MenubarSubContent' | 'MenubarSubTrigger' | 'MenubarMenu' | 'NavigationMenuRoot' | 'NavigationMenuContent' | 'NavigationMenuIndicator' | 'NavigationMenuItem' | 'NavigationMenuLink' | 'NavigationMenuList' | 'NavigationMenuSub' | 'NavigationMenuTrigger' | 'NavigationMenuViewport' | 'NumberFieldRoot' | 'NumberFieldInput' | 'NumberFieldIncrement' | 'NumberFieldDecrement' | 'PaginationRoot' | 'PaginationEllipsis' | 'PaginationFirst' | 'PaginationLast' | 'PaginationList' | 'PaginationListItem' | 'PaginationNext' | 'PaginationPrev' | 'PinInputRoot' | 'PinInputInput' | 'PopoverRoot' | 'PopoverTrigger' | 'PopoverPortal' | 'PopoverContent' | 'PopoverArrow' | 'PopoverClose' | 'PopoverAnchor' | 'ProgressRoot' | 'ProgressIndicator' | 'RadioGroupRoot' | 'RadioGroupItem' | 'RadioGroupIndicator' | 'RangeCalendarRoot' | 'RangeCalendarHeader' | 'RangeCalendarHeading' | 'RangeCalendarGrid' | 'RangeCalendarCell' | 'RangeCalendarHeadCell' | 'RangeCalendarNext' | 'RangeCalendarPrev' | 'RangeCalendarGridHead' | 'RangeCalendarGridBody' | 'RangeCalendarGridRow' | 'RangeCalendarCellTrigger' | 'ScrollAreaRoot' | 'ScrollAreaViewport' | 'ScrollAreaScrollbar' | 'ScrollAreaThumb' | 'ScrollAreaCorner' | 'SelectRoot' | 'SelectTrigger' | 'SelectPortal' | 'SelectContent' | 'SelectArrow' | 'SelectSeparator' | 'SelectItemIndicator' | 'SelectLabel' | 'SelectGroup' | 'SelectItem' | 'SelectItemText' | 'SelectViewport' | 'SelectScrollUpButton' | 'SelectScrollDownButton' | 'SelectValue' | 'SelectIcon' | 'Separator' | 'SliderRoot' | 'SliderThumb' | 'SliderTrack' | 'SliderRange' | 'SplitterGroup' | 'SplitterPanel' | 'SplitterResizeHandle' | 'StepperRoot' | 'StepperItem' | 'StepperTrigger' | 'StepperDescription' | 'StepperTitle' | 'StepperIndicator' | 'StepperSeparator' | 'SwitchRoot' | 'SwitchThumb' | 'TabsRoot' | 'TabsList' | 'TabsContent' | 'TabsTrigger' | 'TabsIndicator' | 'TagsInputRoot' | 'TagsInputInput' | 'TagsInputItem' | 'TagsInputItemText' | 'TagsInputItemDelete' | 'TagsInputClear' | 'TimeFieldInput' | 'TimeFieldRoot' | 'ToastProvider' | 'ToastRoot' | 'ToastPortal' | 'ToastAction' | 'ToastClose' | 'ToastViewport' | 'ToastTitle' | 'ToastDescription' | 'Toggle' | 'ToggleGroupRoot' | 'ToggleGroupItem' | 'ToolbarRoot' | 'ToolbarButton' | 'ToolbarLink' | 'ToolbarToggleGroup' | 'ToolbarToggleItem' | 'ToolbarSeparator' | 'TooltipRoot' | 'TooltipTrigger' | 'TooltipContent' | 'TooltipArrow' | 'TooltipPortal' | 'TooltipProvider' | 'TreeRoot' | 'TreeItem' | 'TreeVirtualizer' | 'Viewport' | 'ConfigProvider' | 'FocusScope' | 'RovingFocusGroup' | 'RovingFocusItem' | 'Presence' | 'Primitive' | 'Slot' | 'VisuallyHidden'
export type NuxtComponentMeta = Record<NuxtComponentMetaNames, ComponentData>
declare const components: NuxtComponentMeta
export { components as default, components }