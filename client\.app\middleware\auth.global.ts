// middleware/auth.global.ts
import { defineNuxtRouteMiddleware, navigateTo } from "nuxt/app";
import { useAuthStore } from "../stores/useAuthStore";
import { useAccessRightsStore } from "../../layers/core/stores/useAccessRightsStore";
import { useSubscriptionStore } from "../stores/useSubscriptionStore";

export default defineNuxtRouteMiddleware(async (to) => {
  console.log("[Auth Middleware] Handling route:", to.path);
  const authStore = useAuthStore();
  const accessRightsStore = useAccessRightsStore();

  // Log authentication state
  console.log("[Auth Middleware] Auth state:", {
    isLoggedIn: authStore.isLoggedIn,
    roles: authStore.roles,
  });

  if (authStore.isLoggedIn) {
    // If user is logged in, check access rights
    console.log("[Auth Middleware] User is logged in, checking access rights");

    // Check if trying to access public routes that should redirect to core
    if (to.path === "/" || to.path === "/auth") {
      console.log("[Auth Middleware] Redirecting from public route to /core");
      return navigateTo("/core");
    }

    // SuperAdmin has access to everything
    if (authStore.roles.includes("SUPERADMIN")) {
      console.log("[Auth Middleware] User is SUPERADMIN, allowing access");
      return;
    }

    // During development, be more permissive to avoid blocking UI
    if (process.env.NODE_ENV === "development") {
      console.log("[Auth Middleware] Development mode, allowing access");
      return;
    }

    // Prevent infinite redirects - if we're already at /core, don't redirect again
    if (to.path === "/core") {
      console.log(
        "[Auth Middleware] Already at /core, allowing access to prevent infinite redirect"
      );
      return;
    }

    // Check subscription status for non-public routes
    // Skip this check for core routes that should be accessible even without an active subscription
    const subscriptionStore = useSubscriptionStore();
    if (
      !to.path.startsWith("/core/subscription") &&
      !to.path.startsWith("/core/profile") &&
      !to.path.startsWith("/users/profile")
    ) {
      // Fetch subscription status if not already loaded
      if (!subscriptionStore.currentSubscription) {
        try {
          await subscriptionStore.fetchCurrentSubscription();
        } catch (error: Error | unknown) {
          const errorMessage =
            error instanceof Error ? error.message : "Unknown error";
          console.error(
            "[Auth Middleware] Error fetching subscription:",
            errorMessage
          );
        }
      }

      // If no active subscription, redirect to subscription page
      if (!subscriptionStore.hasActiveSubscription) {
        console.log(
          "[Auth Middleware] No active subscription, redirecting to subscription page"
        );
        return navigateTo("/core/subscription");
      }

      // Check if the module is included in the subscription
      const module = to.path.split("/")[1]; // Extract module name from path
      if (module && module !== "core") {
        const activeModules = subscriptionStore.activeModules;

        // Convert module names to lowercase for case-insensitive comparison
        const normalizedActiveModules = activeModules.map((m: string) =>
          typeof m === "string" ? m.toLowerCase() : ""
        );

        if (!normalizedActiveModules.includes(module.toLowerCase())) {
          console.log(
            `[Auth Middleware] Module ${module} not included in subscription, redirecting to core`
          );
          return navigateTo("/core");
        }
      }
    }

    // Check if user has access to the requested path
    // Try to load access rights if not already loaded
    if (!accessRightsStore.isLoaded) {
      try {
        await accessRightsStore.getCurrentUserAccessRights();
      } catch (error) {
        console.error("[Auth Middleware] Error loading access rights:", error);
      }
    }

    const hasAccess = accessRightsStore.canAccessRoute(to.path);

    if (hasAccess) {
      console.log("[Auth Middleware] User has access to", to.path);
      return;
    } else {
      console.log("[Auth Middleware] User does not have access to", to.path);
      // Redirect to core dashboard if user doesn't have access
      return navigateTo("/core");
    }
  }

  // Log public route checks
  console.log("[Auth Middleware] Checking public route access for:", to.path);
  if (
    to.path === "/" ||
    to.path === "/auth" ||
    to.path === "/auth/signup" ||
    to.path.startsWith("/auth/") ||
    to.path.startsWith("/landing")
  ) {
    console.log("[Auth Middleware] Allowing public route access");
    return;
  }

  console.log("[Auth Middleware] Redirecting to root");
  return navigateTo("/");
});
