<template>
  <div class="tiptap-editor border border-muted-200 dark:border-muted-800 rounded-md">
    <!-- Toolbar -->
    <div class="flex flex-wrap items-center gap-1 p-2 border-b border-muted-200 dark:border-muted-800 bg-muted-50 dark:bg-muted-900/50">
      <!-- Text Formatting -->
      <button
        @click="editor?.chain().focus().toggleBold().run()"
        :class="{ 'bg-primary-500 text-white': editor?.isActive('bold') }"
        class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors"
        type="button"
      >
        <Icon name="ph:text-b-bold" class="h-4 w-4" />
      </button>
      
      <button
        @click="editor?.chain().focus().toggleItalic().run()"
        :class="{ 'bg-primary-500 text-white': editor?.isActive('italic') }"
        class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors"
        type="button"
      >
        <Icon name="ph:text-italic-bold" class="h-4 w-4" />
      </button>
      
      <button
        @click="editor?.chain().focus().toggleUnderline().run()"
        :class="{ 'bg-primary-500 text-white': editor?.isActive('underline') }"
        class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors"
        type="button"
      >
        <Icon name="ph:text-underline-bold" class="h-4 w-4" />
      </button>

      <div class="w-px h-6 bg-muted-200 dark:bg-muted-700 mx-1"></div>

      <!-- Headings -->
      <button
        @click="editor?.chain().focus().toggleHeading({ level: 1 }).run()"
        :class="{ 'bg-primary-500 text-white': editor?.isActive('heading', { level: 1 }) }"
        class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors text-xs font-medium"
        type="button"
      >
        H1
      </button>
      
      <button
        @click="editor?.chain().focus().toggleHeading({ level: 2 }).run()"
        :class="{ 'bg-primary-500 text-white': editor?.isActive('heading', { level: 2 }) }"
        class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors text-xs font-medium"
        type="button"
      >
        H2
      </button>
      
      <button
        @click="editor?.chain().focus().toggleHeading({ level: 3 }).run()"
        :class="{ 'bg-primary-500 text-white': editor?.isActive('heading', { level: 3 }) }"
        class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors text-xs font-medium"
        type="button"
      >
        H3
      </button>

      <div class="w-px h-6 bg-muted-200 dark:bg-muted-700 mx-1"></div>

      <!-- Lists -->
      <button
        @click="editor?.chain().focus().toggleBulletList().run()"
        :class="{ 'bg-primary-500 text-white': editor?.isActive('bulletList') }"
        class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors"
        type="button"
      >
        <Icon name="ph:list-bullets-bold" class="h-4 w-4" />
      </button>
      
      <button
        @click="editor?.chain().focus().toggleOrderedList().run()"
        :class="{ 'bg-primary-500 text-white': editor?.isActive('orderedList') }"
        class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors"
        type="button"
      >
        <Icon name="ph:list-numbers-bold" class="h-4 w-4" />
      </button>

      <div class="w-px h-6 bg-muted-200 dark:bg-muted-700 mx-1"></div>

      <!-- Text Alignment -->
      <button
        @click="editor?.chain().focus().setTextAlign('left').run()"
        :class="{ 'bg-primary-500 text-white': editor?.isActive({ textAlign: 'left' }) }"
        class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors"
        type="button"
      >
        <Icon name="ph:text-align-left-bold" class="h-4 w-4" />
      </button>
      
      <button
        @click="editor?.chain().focus().setTextAlign('center').run()"
        :class="{ 'bg-primary-500 text-white': editor?.isActive({ textAlign: 'center' }) }"
        class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors"
        type="button"
      >
        <Icon name="ph:text-align-center-bold" class="h-4 w-4" />
      </button>
      
      <button
        @click="editor?.chain().focus().setTextAlign('right').run()"
        :class="{ 'bg-primary-500 text-white': editor?.isActive({ textAlign: 'right' }) }"
        class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors"
        type="button"
      >
        <Icon name="ph:text-align-right-bold" class="h-4 w-4" />
      </button>

      <div class="w-px h-6 bg-muted-200 dark:bg-muted-700 mx-1"></div>

      <!-- Text Colors -->
      <div class="flex items-center gap-1">
        <label class="text-xs text-muted-600 dark:text-muted-400">Color:</label>
        <input
          type="color"
          @input="editor?.chain().focus().setColor($event.target.value).run()"
          class="w-6 h-6 rounded border border-muted-200 dark:border-muted-700 cursor-pointer"
        />
      </div>
    </div>

    <!-- Editor Content -->
    <div class="prose prose-sm max-w-none p-4 min-h-[200px] focus-within:outline-none">
      <EditorContent :editor="editor" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useEditor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import TextStyle from '@tiptap/extension-text-style'
import Color from '@tiptap/extension-color'
import TextAlign from '@tiptap/extension-text-align'
import Underline from '@tiptap/extension-underline'

interface Props {
  modelValue: string
  placeholder?: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Start typing...'
})

const emit = defineEmits<Emits>()

const editor = useEditor({
  content: props.modelValue,
  extensions: [
    StarterKit,
    TextStyle,
    Color,
    TextAlign.configure({
      types: ['heading', 'paragraph'],
    }),
    Underline,
  ],
  onUpdate: ({ editor }) => {
    emit('update:modelValue', editor.getHTML())
  },
  editorProps: {
    attributes: {
      class: 'prose prose-sm max-w-none focus:outline-none',
    },
  },
})

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  if (editor.value && editor.value.getHTML() !== newValue) {
    editor.value.commands.setContent(newValue)
  }
})

onBeforeUnmount(() => {
  editor.value?.destroy()
})
</script>

<style>
.tiptap-editor .ProseMirror {
  outline: none;
}

.tiptap-editor .ProseMirror p.is-editor-empty:first-child::before {
  color: #adb5bd;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}
</style>
