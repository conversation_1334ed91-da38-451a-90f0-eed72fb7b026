import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

interface TranslationRequest {
  text: string;
  fromLanguage: 'en' | 'et';
  toLanguage: 'en' | 'et';
  context?: string;
}

interface TranslationResponse {
  translatedText: string;
  fromLanguage: string;
  toLanguage: string;
}

export class AITranslationService {
  /**
   * Translate text using OpenAI GPT
   */
  static async translateText(request: TranslationRequest): Promise<TranslationResponse> {
    const { text, fromLanguage, toLanguage, context } = request;

    // If source and target languages are the same, return original text
    if (fromLanguage === toLanguage) {
      return {
        translatedText: text,
        fromLanguage,
        toLanguage,
      };
    }

    const languageNames = {
      en: 'English',
      et: 'Estonian',
    };

    const systemPrompt = `You are a professional translator specializing in business and educational content. 
Your task is to translate text from ${languageNames[fromLanguage]} to ${languageNames[toLanguage]}.

IMPORTANT RULES:
1. Preserve ALL HTML tags and formatting exactly as they appear
2. Only translate the text content, never the HTML tags or attributes
3. Maintain the same structure and formatting
4. Use professional, business-appropriate language
5. Keep the tone and style consistent with the original
6. For business terminology, use standard business terms in the target language
7. If the text contains rich text formatting (bold, italic, headings), preserve it exactly

${context ? `Context: This text is ${context}` : ''}

Return ONLY the translated text with preserved HTML formatting. Do not add explanations or comments.`;

    try {
      const completion = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: systemPrompt,
          },
          {
            role: 'user',
            content: text,
          },
        ],
        temperature: 0.3, // Lower temperature for more consistent translations
        max_tokens: 2000,
      });

      const translatedText = completion.choices[0]?.message?.content?.trim();

      if (!translatedText) {
        throw new Error('No translation received from OpenAI');
      }

      return {
        translatedText,
        fromLanguage,
        toLanguage,
      };
    } catch (error) {
      console.error('AI Translation Error:', error);
      throw new Error(`Translation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Translate greeting content
   */
  static async translateGreeting(
    title: string,
    content: string,
    fromLanguage: 'en' | 'et',
    toLanguage: 'en' | 'et'
  ): Promise<{ title: string; content: string }> {
    const [titleTranslation, contentTranslation] = await Promise.all([
      this.translateText({
        text: title,
        fromLanguage,
        toLanguage,
        context: 'a greeting title for a business idea page',
      }),
      this.translateText({
        text: content,
        fromLanguage,
        toLanguage,
        context: 'a greeting message for a business idea page',
      }),
    ]);

    return {
      title: titleTranslation.translatedText,
      content: contentTranslation.translatedText,
    };
  }

  /**
   * Translate learning material content
   */
  static async translateLearningMaterial(
    title: string,
    content: string,
    fromLanguage: 'en' | 'et',
    toLanguage: 'en' | 'et'
  ): Promise<{ title: string; content: string }> {
    const [titleTranslation, contentTranslation] = await Promise.all([
      this.translateText({
        text: title,
        fromLanguage,
        toLanguage,
        context: 'a title for educational learning material about business',
      }),
      this.translateText({
        text: content,
        fromLanguage,
        toLanguage,
        context: 'educational content about business concepts and entrepreneurship',
      }),
    ]);

    return {
      title: titleTranslation.translatedText,
      content: contentTranslation.translatedText,
    };
  }

  /**
   * Detect language of text (simple heuristic)
   */
  static detectLanguage(text: string): 'en' | 'et' {
    // Simple Estonian language detection based on common Estonian words/characters
    const estonianIndicators = [
      'ä', 'ö', 'ü', 'õ', // Estonian specific characters
      'ja', 'on', 'ei', 'või', 'kui', 'see', 'aga', 'ning', // Common Estonian words
      'äri', 'ettevõte', 'idee', 'turg', 'klient', // Business terms in Estonian
    ];

    const lowerText = text.toLowerCase();
    const estonianMatches = estonianIndicators.filter(indicator => 
      lowerText.includes(indicator)
    ).length;

    // If we find Estonian indicators, assume it's Estonian
    return estonianMatches > 0 ? 'et' : 'en';
  }
}
