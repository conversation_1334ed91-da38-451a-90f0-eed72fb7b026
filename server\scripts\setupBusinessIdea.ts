#!/usr/bin/env ts-node

import { execSync } from "child_process";
import { seedBusinessIdeaData } from "./seedBusinessIdeaData";

async function setupBusinessIdea() {
  try {
    console.log("🚀 Setting up Business Idea module...");

    // Step 1: Generate Prisma client with new schema
    console.log("📦 Generating Prisma client...");
    execSync("pnpm prisma generate", { stdio: "inherit" });

    // Step 2: Run database migration (if needed)
    console.log("🗄️  Running database migration...");
    try {
      execSync("pnpm prisma db push", { stdio: "inherit" });
    } catch (error) {
      console.log("⚠️  Database migration may have failed, but continuing...");
    }

    // Step 3: Seed learning materials and greeting
    console.log("🌱 Seeding business idea data...");
    await seedBusinessIdeaData();

    console.log("✅ Business Idea module setup completed successfully!");
    console.log("");
    console.log("🎉 You can now:");
    console.log("   • Navigate to /core/business/idea");
    console.log("   • Start creating your business idea");
    console.log("   • Use the learning materials (editable by ADMIN/SUPERADMIN)");
    console.log("   • Add collaborators from your team");
    console.log("   • Take notes with the post-it style notes");
    console.log("");

  } catch (error) {
    console.error("❌ Setup failed:", error);
    process.exit(1);
  }
}

// Run setup if this file is executed directly
if (require.main === module) {
  setupBusinessIdea()
    .then(() => {
      console.log("Setup completed!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Setup failed:", error);
      process.exit(1);
    });
}

export { setupBusinessIdea };
