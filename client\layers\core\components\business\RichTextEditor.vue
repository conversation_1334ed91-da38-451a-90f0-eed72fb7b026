<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'

interface Props {
  modelValue: string
  placeholder?: string
  minHeight?: number
}

interface Emits {
  (e: 'update:modelValue', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Start typing...',
  minHeight: 200
})

const emit = defineEmits<Emits>()

// State
const editorRef = ref<HTMLDivElement>()
const content = ref(props.modelValue)

// Watch for external changes
watch(() => props.modelValue, (newValue) => {
  if (newValue !== content.value) {
    content.value = newValue
    if (editorRef.value) {
      editorRef.value.innerHTML = newValue
    }
  }
})

// Methods
const updateContent = () => {
  if (editorRef.value) {
    content.value = editorRef.value.innerHTML
    emit('update:modelValue', content.value)
  }
}

const execCommand = (command: string, value?: string) => {
  document.execCommand(command, false, value)
  updateContent()
  editorRef.value?.focus()
}

const insertHeading = (level: number) => {
  execCommand('formatBlock', `h${level}`)
}

const insertList = (type: 'ul' | 'ol') => {
  execCommand(type === 'ul' ? 'insertUnorderedList' : 'insertOrderedList')
}

const setTextColor = (color: string) => {
  execCommand('foreColor', color)
}

const handlePaste = (event: ClipboardEvent) => {
  event.preventDefault()
  const text = event.clipboardData?.getData('text/plain') || ''
  execCommand('insertText', text)
}

// Lifecycle
onMounted(() => {
  if (editorRef.value) {
    editorRef.value.innerHTML = content.value
  }
})
</script>

<template>
  <div class="border border-muted-200 dark:border-muted-800 rounded-lg overflow-hidden">
    <!-- Toolbar -->
    <div class="bg-muted-50 dark:bg-muted-900 border-b border-muted-200 dark:border-muted-800 p-2">
      <div class="flex flex-wrap gap-1">
        <!-- Headings -->
        <div class="flex gap-1">
          <BaseButton
            size="icon-sm"
            variant="ghost"
            @click="insertHeading(1)"
            title="Heading 1"
          >
            <span class="text-xs font-bold">H1</span>
          </BaseButton>
          <BaseButton
            size="icon-sm"
            variant="ghost"
            @click="insertHeading(2)"
            title="Heading 2"
          >
            <span class="text-xs font-bold">H2</span>
          </BaseButton>
          <BaseButton
            size="icon-sm"
            variant="ghost"
            @click="insertHeading(3)"
            title="Heading 3"
          >
            <span class="text-xs font-bold">H3</span>
          </BaseButton>
        </div>

        <div class="w-px h-6 bg-muted-200 dark:bg-muted-700"></div>

        <!-- Text Formatting -->
        <div class="flex gap-1">
          <BaseButton
            size="icon-sm"
            variant="ghost"
            @click="execCommand('bold')"
            title="Bold"
          >
            <Icon name="ph:text-b-duotone" class="h-4 w-4" />
          </BaseButton>
          <BaseButton
            size="icon-sm"
            variant="ghost"
            @click="execCommand('italic')"
            title="Italic"
          >
            <Icon name="ph:text-italic-duotone" class="h-4 w-4" />
          </BaseButton>
        </div>

        <div class="w-px h-6 bg-muted-200 dark:bg-muted-700"></div>

        <!-- Lists -->
        <div class="flex gap-1">
          <BaseButton
            size="icon-sm"
            variant="ghost"
            @click="insertList('ul')"
            title="Bullet List"
          >
            <Icon name="ph:list-bullets-duotone" class="h-4 w-4" />
          </BaseButton>
          <BaseButton
            size="icon-sm"
            variant="ghost"
            @click="insertList('ol')"
            title="Numbered List"
          >
            <Icon name="ph:list-numbers-duotone" class="h-4 w-4" />
          </BaseButton>
        </div>

        <div class="w-px h-6 bg-muted-200 dark:bg-muted-700"></div>

        <!-- Text Colors -->
        <div class="flex gap-1">
          <BaseButton
            size="icon-sm"
            variant="ghost"
            @click="setTextColor('#ef4444')"
            title="Red Text"
            class="text-red-500"
          >
            <Icon name="ph:text-aa-duotone" class="h-4 w-4" />
          </BaseButton>
          <BaseButton
            size="icon-sm"
            variant="ghost"
            @click="setTextColor('#3b82f6')"
            title="Blue Text"
            class="text-blue-500"
          >
            <Icon name="ph:text-aa-duotone" class="h-4 w-4" />
          </BaseButton>
          <BaseButton
            size="icon-sm"
            variant="ghost"
            @click="setTextColor('#10b981')"
            title="Green Text"
            class="text-green-500"
          >
            <Icon name="ph:text-aa-duotone" class="h-4 w-4" />
          </BaseButton>
          <BaseButton
            size="icon-sm"
            variant="ghost"
            @click="setTextColor('#000000')"
            title="Black Text"
            class="text-black dark:text-white"
          >
            <Icon name="ph:text-aa-duotone" class="h-4 w-4" />
          </BaseButton>
        </div>
      </div>
    </div>

    <!-- Editor -->
    <div
      ref="editorRef"
      contenteditable="true"
      class="p-4 prose prose-muted dark:prose-invert max-w-none focus:outline-none"
      :style="{ minHeight: `${minHeight}px` }"
      :placeholder="placeholder"
      @input="updateContent"
      @paste="handlePaste"
    />
  </div>
</template>

<style scoped>
[contenteditable]:empty:before {
  content: attr(placeholder);
  color: rgb(156 163 175);
  font-style: italic;
}

[contenteditable]:focus:before {
  content: '';
}

/* Ensure proper styling for the editor content */
[contenteditable] {
  outline: none;
}

[contenteditable] h1,
[contenteditable] h2,
[contenteditable] h3 {
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

[contenteditable] h1 {
  font-size: 1.5rem;
}

[contenteditable] h2 {
  font-size: 1.25rem;
}

[contenteditable] h3 {
  font-size: 1.125rem;
}

[contenteditable] p {
  margin-bottom: 0.75rem;
}

[contenteditable] ul,
[contenteditable] ol {
  margin-bottom: 0.75rem;
  padding-left: 1.5rem;
}

[contenteditable] li {
  margin-bottom: 0.25rem;
}
</style>
